<?php

$param_elezioni = estrai_parametri_singoli('ABILITA_ELEZIONI_PORTALE');
$template->assign('param_elezioni', $param_elezioni);

switch ($stato_secondario) {
    case "pubblicazione_note_disciplinari_update":
        //{{{ <editor-fold defaultstate="collapsed">
        if (is_array($pubblicazione)) {
            foreach ($pubblicazione as $id_nota_disciplinare => $stato) {
                if ($changed[$id_nota_disciplinare] == 'SI') {
                    aggiorna_stato_nota_disciplinare((int) $id_nota_disciplinare, $stato, (int) $current_user, $current_key, $note[$id_nota_disciplinare], $ufficiale[$id_nota_disciplinare]);
                } else {
                    aggiorna_stato_nota_disciplinare((int) $id_nota_disciplinare, $stato, (int) $current_user, $current_key);
                }
            }
        }
    //}}} </editor-fold>
    //Break omesso volutamente
    case "pubblicazione_note_disciplinari_display":
        //{{{ <editor-fold defaultstate="collapsed">
        $parametri_ricerca = null;
        if (isset($stato)) {
            $parametri_ricerca['stato'] = $stato;
        }

        if (isset($search)) {
            $parametri_ricerca['search'] = $search;
        }

        if ($start > 0) {
            $mat_start = explode('/', $start);
            $start_interval = mktime(0, 0, 0, intval($mat_start[1]), intval($mat_start[0]), intval($mat_start[2]));
        } else {
            $start_interval = mktime(0, 0, 0);
            $start = date('d/m/Y');
        }

        $parametri_ricerca['start_interval'] = $start_interval;

        if ($end > 0) {
            $mat_end = explode('/', $end);
            $end_interval = mktime(23, 59, 59, intval($mat_end[1]), intval($mat_end[0]), intval($mat_end[2]));
        } else {
            $end_interval = mktime(23, 59, 59);
            $end = date('d/m/Y');
        }

        $parametri_ricerca['end_interval'] = $end_interval;

        $elenco_note = ricerca_note_disciplinari($parametri_ricerca, 'SI', $current_key);
        $elenco_valori_note = estrai_valori_note_disciplinari();
        $template->assign('elenco_note', $elenco_note);
        $template->assign('elenco_valori_note', $elenco_valori_note);
        $template->assign('start', $start);
        $template->assign('end', $end);
        $template->assign('stato', $stato);
        $template->assign('search', $search);
        $stato_secondario = 'pubblicazione_note_disciplinari_display';
        //}}} </editor-fold>
        break;
    case "controllo_firme_registro_update":
        //{{{ <editor-fold defaultstate="collapsed">
        if ($update_id_professore) {
            $mat_day = explode('/', $update_orario_d);
            $mat_hour = explode(':', $update_orario_h);
            $data = mktime(intval($mat_hour[0]), intval($mat_hour[1]), 59, intval($mat_day[1]), intval($mat_day[0]), intval($mat_day[2]));

            if ($update_id_firma > 0) {
                if ($operazione == 'delete') {
                    elimina_firma_professore((int) $update_id_firma, (int) $current_user);
                } else {
                    modifica_firma_professore((int) $update_id_firma, (int) $update_id_classe, (int) $update_id_materia, (int) $update_id_professore, $data, (int) $current_user);
                }
            } else {
                inserisci_firma_professore((int) $update_id_classe, (int) $update_id_materia, (int) $update_id_professore, $data, $data, (int) $current_user, $data);
            }
        }
    //}}} </editor-fold>
    //Break omesso volutamente
    case "controllo_firme_registro_display":
        //{{{ <editor-fold defaultstate="collapsed">
        // TODO: verificare perchè richiede tanta memoria
        ini_set('memory_limit', '1024M');

        if ($select_id_professore > 0) {
            $elenco_classi = estrai_classi_select((int) $current_user);
            $template->assign('elenco_classi', $elenco_classi);
            $elenco_materie = estrai_materie_select('descrizione');
            $template->assign('elenco_materie', $elenco_materie);

            $dati_professore = estrai_dati_professore((int) $select_id_professore);
            $template->assign('select_docente', $dati_professore['cognome'] . ' ' . $dati_professore['nome']);

            if ($select_id_firma > 0) {
                $dati_firma = estrai_firma_professore((int) $select_id_firma);
                $select_id_classe = $dati_firma['id_classe'];
                $select_id_materia = $dati_firma['id_materia'];
                $select_orario = $dati_firma['data'];
            }

            $template->assign('select_id_professore', $select_id_professore);
            $template->assign('select_id_classe', $select_id_classe);
            $template->assign('select_id_materia', $select_id_materia);
            $template->assign('select_orario', $select_orario);
            $template->assign('select_id_firma', $select_id_firma);
        }

        $parametri_ricerca = null;

        if (in_array($tipo_visualizzazione, ['giornaliera', 'settimana']) || $search != '') {
            $parametri_ricerca['search'] = encode($search);
            $parametri_ricerca['tipo_visualizzazione'] = $tipo_visualizzazione;
            $elenco_firme = calendari_firme_professori($parametri_ricerca, $anno_inizio);
            $elenco_professori = estrai_professori();
        }

        $template->assign('elenco_firme', $elenco_firme);
        $template->assign('elenco_professori', $elenco_professori);
        $template->assign('start', $start);
        $template->assign('end', $end);
        $template->assign('search', $search);
        $template->assign('tipo_visualizzazione', $tipo_visualizzazione);
        $template->assign('parametri_ricerca', $parametri_ricerca);
        $template->assign('anno_scolastico_attuale', $stringa_anno_scolastico);
        $stato_secondario = 'controllo_firme_registro_display';
        //}}} </editor-fold>
        break;

    case "controllo_registro_oldstyle_display":
        //{{{ <editor-fold defaultstate="collapsed">
        if (strlen($search) > 0) {
            $elenco_professori = cerca_professore($search);
        }

        $template->assign('elenco_professori', $elenco_professori);
        $template->assign('search', $search);
        //}}} </editor-fold>
        break;
    case "gestione_mensa_professori":
        //{{{ <editor-fold defaultstate="collapsed">
        $template->assign('elenco_professori', estrai_mensa_professori());
        //}}} </editor-fold>
        break;

    case "controllo_colloqui_docenti_update_old":
        //{{{ <editor-fold defaultstate="collapsed">
        if ($update_id_professore) {
            // Uso lo stesso giorno per inizio e fine colloquio
            if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $update_data, $mat_day) != 1) {
                $mat_day = [null, (int) date('d'), (int) date('m'), (int) date('Y')];
            }

            if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $update_ora_inizio, $mat_hour_start) != 1) {
                $mat_hour_start = [null, (int) date('H') + 1, 00];
            }
            $data_inizio = mktime(intval($mat_hour_start[1]), intval($mat_hour_start[2]), 0, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));

            if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $update_ora_fine, $mat_hour_end) != 1) {
                $mat_hour_end = [null, (int) date('H') + 2, 00];
            }
            $data_fine = mktime(intval($mat_hour_end[1]), intval($mat_hour_end[2]), 59, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));


            if ($update_id_prenotazione > 0) {
                /* {{{ */
                if ($operazione == 'delete') {
                    elimina_prenotazione_colloquio((int) $update_id_prenotazione, (int) $current_user);
                }
                /* }}} */
            } elseif ($update_id_colloquio > 0) {
                /* {{{ */
                if ($operazione == 'delete') {
                    elimina_annotazione_agenda((int) $update_id_colloquio, (int) $current_user, $current_key);
                } else {
                    $stato = estrai_festivita_calendario($data_inizio);
                    if ($stato['tipo_giornata'] == 'A') {
                        modifica_annotazione_agenda((int) $update_id_colloquio, 0, 0, $data_inizio, $data_fine, encode($update_titolo), $update_testo, 0, $current_user, $current_key, 1, intval($update_numero_slot));
                    } else {
                        $messaggio = "Impossibile modificare il colloquio: la data selezionata è considerata festività.";
                    }
                }
                /* }}} */
            } else {
                /* {{{ */
                if ($ripeti == 'SI') {
                    if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $update_data_fine, $mat_day) != 1) {
                        $mat_day = [null, (int) date('d'), (int) date('m'), (int) date('Y')];
                    }
                    $data_ripetizione = mktime(intval($mat_hour_start[1]), intval($mat_hour_start[2]), 59, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));
                }
                if ($ripeti == 'SI' and $data_ripetizione > $data_fine) {
                    $settimane = determina_settimane_intervallo(intval(mktime(0, 0, 0, intval(date('m', $data_inizio)), intval(date('d', $data_inizio)), intval(date('Y', $data_inizio)))), $data_ripetizione);

                    $ora_inizio = ($mat_hour_start[1] * 3600) + ($mat_hour_start[2] * 60);
                    $ora_fine = ($mat_hour_end[1] * 3600) + ($mat_hour_end[2] * 60);
                    $codice_gruppo = $current_user . "_" . time();

                    foreach ($settimane as $settimana) {
                        $stato = estrai_festivita_calendario($settimana['giorno_ripetuto_ts']);

                        if ($stato['tipo_giornata'] == 'A') {
                            $annotazione_agenda = inserisci_annotazione_agenda(
                                    0, 0, $settimana['giorno_ripetuto_ts'] + $ora_inizio, $settimana['giorno_ripetuto_ts'] + $ora_fine, encode($update_titolo), encode($update_testo), 0, $current_user, $current_key, 1, intval($update_numero_slot), $update_id_professore, $codice_gruppo
                            );
                        }
                    }
                } else {
                    inserisci_annotazione_agenda(
                            0, 0, $data_inizio, $data_fine, encode($update_titolo), encode($update_testo), 0, $current_user, $current_key, 1, intval($update_numero_slot), $update_id_professore
                    );
                }
                /* }}} */
            }
        }
    //}}} </editor-fold>
    //Break omesso volutamente
    case "controllo_colloqui_docenti_display_old":
        //{{{ <editor-fold defaultstate="collapsed">

        if ($select_id_professore > 0) {
            $dati_professore = estrai_dati_professore((int) $select_id_professore);
            $template->assign('select_docente', $dati_professore['cognome'] . ' ' . $dati_professore['nome']);
            $template->assign('select_id_professore', $select_id_professore);
            $template->assign('select_id_colloquio', $select_id_colloquio);
        }

        $parametri_ricerca = null;

        $inizio_default = $data_inizio_anno_scolastico;
        $fine_default = $data_fine_anno_scolastico;
        if (in_array($tipo_visualizzazione, ['giornaliera', 'settimanale']) || $search != '') {
            $parametri_ricerca['search'] = encode($search);
            switch ($tipo_visualizzazione) {
                case 'giornaliera':
                    $data_inizio = date('d/m/Y');
                    $data_fine = date('d/m/Y');
                    break;
                case 'settimanale':
                    $settimana = determina_intervallo_settimana_data_corrente(time());
                    $data_inizio = date('d/m/Y', $settimana['data_inizio']);
                    $data_fine = date('d/m/Y', $settimana['data_fine']);
                    break;
            }
        }

        /* {{{ Setup ore di ricerca */
        if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $ora_inizio, $mat_hour) != 1) {
            $mat_hour = [null, 0, 0];
        }
        if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $data_inizio, $mat_day) != 1) {
            $mat_day = [null, date('m', $inizio_default), date('d', $inizio_default), date('Y', $inizio_default)];
        }
        $data_inizio_calc = mktime(intval($mat_hour[1]), intval($mat_hour[2]), 0, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));


        if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $ora_fine, $mat_hour) != 1) {
            $mat_hour = [null, 23, 59];
        }
        if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $data_fine, $mat_day) != 1) {
            $mat_day = [null, date('m', $fine_default), date('d', $fine_default), date('Y', $fine_default)];
        }
        $data_fine_calc = mktime(intval($mat_hour[1]), intval($mat_hour[2]), 59, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));

        $data_inizio_trad = date("d/m/Y", $data_inizio_calc);
        $ora_inizio_trad = date("H:i", $data_inizio_calc);

        $data_fine_trad = date("d/m/Y", $data_fine_calc);
        $ora_fine_trad = date("H:i", $data_fine_calc);

        $parametri_ricerca['start_interval'] = $data_inizio_calc;
        $parametri_ricerca['end_interval'] = $data_fine_calc;
        /* }}} */


        $template->assign('elenco_firme', $elenco_firme);
        $template->assign('elenco_professori', $elenco_professori);
        $template->assign('data_inizio_trad', $data_inizio_trad);
        $template->assign('data_fine_trad', $data_fine_trad);
        $template->assign('ora_inizio_trad', $ora_inizio_trad);
        $template->assign('ora_fine_trad', $ora_fine_trad);
        $template->assign('search', $search);

        $template->assign('tipo_visualizzazione', $tipo_visualizzazione);
        $template->assign('parametri_ricerca', $parametri_ricerca);
        $template->assign('anno_scolastico_attuale', $stringa_anno_scolastico);
        $stato_secondario = 'controllo_colloqui_docenti_display';
        //}}} </editor-fold>
        break;

    case "controllo_agenda_registro_update":
        //{{{ <editor-fold defaultstate="collapsed">
        if ($update_id_professore > 0 or $update_id_classe > 0 or ( $operazione == 'update_massa' and count($mat_abbinamenti) > 0)) {
            if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $update_data_inizio, $mat_day) != 1) {
                $mat_day = [null, (int) date('d'), (int) date('m'), (int) date('Y')];
            }
            if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $update_ora_inizio, $mat_hour) != 1) {
                $mat_hour = [null, (int) date('H') + 1, 0];
            }
            $data_inizio = mktime(intval($mat_hour[1]), intval($mat_hour[2]), 0, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));

            if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $update_data_fine, $mat_day) != 1) {
                $mat_day = [null, (int) date('d'), (int) date('m'), (int) date('Y')];
            }
            if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $update_ora_fine, $mat_hour) != 1) {
                $mat_hour = [null, (int) date('H') + 2, 0];
            }
            $data_fine = mktime(intval($mat_hour[1]), intval($mat_hour[2]), 0, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));

            $mat_abbinamento = explode('|', $update_id_abbinamento);
            if ($update_id_professore > 0) {
                $id_classe = $mat_abbinamento[0];
                $id_materia = $mat_abbinamento[1];
                $id_professore = $update_id_professore;
            } elseif ($update_id_classe > 0) {
                $id_classe = $update_id_classe;
                $id_materia = $mat_abbinamento[1];
                $id_professore = $mat_abbinamento[0];
            }


            if ($update_id_agenda > 0) {
                if ($operazione == 'delete') {
                    elimina_annotazione_agenda((int) $update_id_agenda, (int) $current_user, $current_key);
                } else {
                    modifica_annotazione_agenda((int) $update_id_agenda, (int) $id_classe, (int) $id_materia, $data_inizio, $data_fine, encode($update_titolo), encode($update_testo), 0, $current_user, $current_key, 0, 0);
                }
            } else {

                if ($operazione == 'update_massa' and count($mat_abbinamenti) > 0) {
                    foreach ($mat_abbinamenti as $abbinamento) {
                        $mat_abbinamento = explode('|', $abbinamento);
                        $id_classe = $mat_abbinamento[0];
                        $id_materia = $mat_abbinamento[1];
                        inserisci_annotazione_agenda((int) $id_classe, (int) $id_materia, $data_inizio, $data_fine, encode($update_titolo), encode($update_testo), 0, $current_user, $current_key, 0, 0, $current_user);
                    }
                } else {
                    inserisci_annotazione_agenda((int) $id_classe, (int) $id_materia, $data_inizio, $data_fine, encode($update_titolo), encode($update_testo), 0, $current_user, $current_key, 0, 0, $current_user);
                }
            }
        }
    //}}} </editor-fold>
    //Break omesso volutamente
    case "controllo_agenda_registro_display":
        //{{{ <editor-fold defaultstate="collapsed">

        if (!isset($tipo_visualizzazione)) {
            $tipo_visualizzazione = 'classi';
        }

        if ($select_id_professore > 0) {
            $elenco_abbinamenti = estrai_abbinamenti((int) $select_id_professore, 'professore');
            $template->assign('elenco_abbinamenti', $elenco_abbinamenti);

            $dati_professore = estrai_dati_professore((int) $select_id_professore);
            $template->assign('select_descrizione', $dati_professore['cognome'] . ' ' . $dati_professore['nome']);
            $template->assign('select_id_professore', $select_id_professore);
            $template->assign('select_id_agenda', $select_id_agenda);
        } elseif ($select_id_classe > 0) {
            $elenco_abbinamenti = estrai_abbinamenti((int) $select_id_classe, 'classe');
            $template->assign('elenco_abbinamenti', $elenco_abbinamenti);

            $dati_classe = estrai_classe((int) $select_id_classe);
            $template->assign('select_descrizione', $dati_classe['desc_standard']);
            $template->assign('select_id_classe', $select_id_classe);
            $template->assign('select_id_agenda', $select_id_agenda);
        }

        if ($operazione == 'inserisci_massa') {
            $elenco_abbinamenti = estrai_abbinamenti_totali();
            $template->assign('elenco_abbinamenti', $elenco_abbinamenti);
            $template->assign('operazione', $operazione);
        }

        $parametri_ricerca = null;

        $inizio_default = $data_inizio_anno_scolastico;
        $fine_default = $data_fine_anno_scolastico;
        $parametri_ricerca['cerca'] = encode($cerca);
        $parametri_ricerca['cerca_titolo'] = encode($cerca_titolo);
        switch ($preset_data) {
            case 'giornaliera':
                $data_inizio = date('d/m/Y');
                $data_fine = date('d/m/Y');
                break;
            case 'settimanale':
                $settimana = determina_intervallo_settimana_data_corrente(time());
                $data_inizio = date('d/m/Y', $settimana['data_inizio']);
                $data_fine = date('d/m/Y', $settimana['data_fine']);
                break;
        }

        /* {{{ Setup ore di ricerca */
        if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $ora_inizio, $mat_hour) != 1) {
            $mat_hour = [null, 0, 0];
        }
        if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $data_inizio, $mat_day) != 1) {
            $mat_day = [null, date('m', $inizio_default), date('d', $inizio_default), date('Y', $inizio_default)];
        }
        $data_inizio_calc = mktime(intval($mat_hour[1]), intval($mat_hour[2]), 0, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));


        if (preg_match("/^\s*([0-1]?[0-9]|2[0-3])\s*\W\s*([0-5][0-9])\s*$/", $ora_fine, $mat_hour) != 1) {
            $mat_hour = [null, 23, 59];
        }
        if (preg_match("/^\s*(3[0-1]|[0-2]?[0-9])\s*\W\s*(0?[0-9]|1[0-2])\s*\W\s*([0-9]{4}|[0-9]{2})\s*$/", $data_fine, $mat_day) != 1) {
            $mat_day = [null, date('m', $fine_default), date('d', $fine_default), date('Y', $fine_default)];
        }
        $data_fine_calc = mktime(intval($mat_hour[1]), intval($mat_hour[2]), 59, intval($mat_day[2]), intval($mat_day[1]), intval($mat_day[3]));

//		$data_inizio_trad = date("d/m/Y", $data_inizio_calc);
//		$ora_inizio_trad = date("H:i", $data_inizio_calc);

        $data_inizio_trad = date("d/m/Y");
        $ora_inizio_trad = date("H:i", $data_inizio_calc);

//		$data_fine_trad = date("d/m/Y", $data_fine_calc);
//		$ora_fine_trad = date("H:i", $data_fine_calc);

        $data_fine_trad = date("d/m/Y");
        $ora_fine_trad = date("H:i", $data_fine_calc);

        $parametri_ricerca['start_interval'] = $data_inizio_calc;
        $parametri_ricerca['end_interval'] = $data_fine_calc;
        /* }}} */


        $template->assign('data_inizio_trad', $data_inizio_trad);
        $template->assign('data_fine_trad', $data_fine_trad);
        $template->assign('ora_inizio_trad', $ora_inizio_trad);
        $template->assign('ora_fine_trad', $ora_fine_trad);
        $template->assign('cerca', $cerca);
        $template->assign('cerca_titolo', $cerca_titolo);

        $template->assign('preset_data', $preset_data);
        $template->assign('tipo_visualizzazione', $tipo_visualizzazione);
        $template->assign('parametri_ricerca', $parametri_ricerca);
        $template->assign('anno_scolastico_attuale', $stringa_anno_scolastico);
        $stato_secondario = 'controllo_agenda_registro_display';
        //}}} </editor-fold>
        break;
    case "riepilogo_ore_corsi_per_materia_docente":
        //{{{ <editor-fold defaultstate="collapsed">
        ini_set('memory_limit', '1024M');

        $template->assign('tipo_visualizzazione', $tipo_visualizzazione);

        //{{{ <editor-fold defaultstate="collapsed" desc="Setto data inizio e fine">
        if (!$data_inizio_Month)
        {
            $inizio = time();
            $fine = time();
        }
        else
        {
            $inizio = mktime(0, 0, 0, intval($data_inizio_Month), intval($data_inizio_Day), intval($data_inizio_Year));
            $fine = mktime(23, 59, 0, intval($data_fine_Month), intval($data_fine_Day), intval($data_fine_Year));
        }
        $template->assign('inizio', $inizio);
        $template->assign('fine', $fine);
        //}}} </editor-fold>

        $calcola_assenze = null;
        if ($campanelle_rosse != 'SI')
        {
            $campanelle_rosse = 'NO';
            $calcola_assenze = 'campanelle_verdi';
        }
        $template->assign('campanelle_rosse', $campanelle_rosse);

        $settimane_anno = array();
        for($cont=1; $cont<53; $cont++)
        {
            if (date('Y',$inizio) == date('Y',$fine))
            {
                if($cont >= intval(date('W',$inizio)) and $cont <= intval(date('W',$fine)))
                {
                    $settimane_anno[$cont] = sprintf("%'.02d", $cont);
                }
            }
            else
            {
                if($cont >= intval(date('W',$inizio)))
                {
                    $chiave = date('Y', $inizio).sprintf("%'.02d", $cont);
                    $settimane_anno[$chiave] = sprintf("%'.02d", $cont);
                }

                if($cont <= intval(date('W',$fine)))
                {
                    $chiave = date('Y', $fine).sprintf("%'.02d", $cont);
                    $settimane_anno[$chiave] = sprintf("%'.02d", $cont);
                }
            }
        }

        ksort($settimane_anno);
        $template->assign('settimane_anno', $settimane_anno);

        $dati_orario = array();
        $elenco_classi = estrai_classi('base', 'classi_principali');
        $template->assign('elenco_classi', $elenco_classi);

        $firme_docenti = array();
        $firme_docenti = estrai_firme_periodo($inizio, $fine);
        $elenco_firme_ordinato = array();
        foreach($firme_docenti as $firma)
        {
            $elenco_firme_ordinato[$firma['id_professore']][$firma['id_classe']][$firma['id_materia']][$firma['data']] = $firma;
        }

        //attenzione non vengono estratte le ore fatte sulle materie di riferimento

        if ($tipo_visualizzazione == 'classi')
        {
            if ($id_classe_selezionata > 0)
            {

                $template->assign('id_classe_selezionata', $id_classe_selezionata);
                $orario_studenti_classe = estrai_orario_studente_con_corsi($id_classe_selezionata, $inizio, $fine, $calcola_assenze, 'con_sovrapposizioni', $id_studente_selezionato);

                foreach ($orario_studenti_classe as $id_studente => $orario_studente)
                {
                    //cambiare nome descrizione_classe
                    $dati_orario[$id_studente]['dati']['descrizione_studente'] = $orario_studente['dati']['cognome'] . " " . $orario_studente['dati']['nome'];
                    $dati_orario[$id_studente]['dati']['totale_ore'] = $orario_studente['dati']['totale'];
                    foreach ($orario_studente['orario'] as $singolo_orario)
                    {
                        $dati_orario[$id_studente]['materie'][$singolo_orario['id_materia_riferimento']]['dati']['descrizione_materia'] = $singolo_orario['descrizione_materia_riferimento'];
                        $dati_orario[$id_studente]['materie'][$singolo_orario['id_materia_riferimento']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi']++;
                        $dati_orario[$id_studente]['materie'][$singolo_orario['id_materia_riferimento']]['totale']['numero_periodi']++;
                        $dati_orario[$id_studente]['totali']['totale_settimana'][$singolo_orario['settimana_anno']]['numero_periodi']++;
                        $dati_orario[$id_studente]['totali']['totale_complessivo']['numero_periodi']++;
                        $dati_orario[$id_studente]['sotto_materie'][$singolo_orario['id_materia']]['dati']['descrizione_materia'] = $singolo_orario['descrizione_materia'];
                        $dati_orario[$id_studente]['sotto_materie'][$singolo_orario['id_materia']]['dati']['id_materia_riferimento'] = $singolo_orario['id_materia_riferimento'];
                        $dati_orario[$id_studente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi']++;
                        $dati_orario[$id_studente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['numero_periodi']++;

                        if(count($elenco_firme_ordinato[$singolo_orario['id_professore']][$singolo_orario['id_classe']][$singolo_orario['id_materia']]) > 0)
                        {
                            foreach($elenco_firme_ordinato[$singolo_orario['id_professore']][$singolo_orario['id_classe']][$singolo_orario['id_materia']] as $data_firma => $ora_firmata)
                            {
                                if($data_firma >= $singolo_orario['data_inizio'] and $data_firma <= $singolo_orario['data_fine'])
                                {
                                    $dati_orario[$id_studente]['materie'][$singolo_orario['id_materia_riferimento']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi_firmati']++;
                                    $dati_orario[$id_studente]['materie'][$singolo_orario['id_materia_riferimento']]['totale']['numero_periodi_firmati']++;
                                    $dati_orario[$id_studente]['totali']['totale_settimana'][$singolo_orario['settimana_anno']]['numero_periodi_firmati']++;
                                    $dati_orario[$id_studente]['totali']['totale_complessivo']['numero_periodi_firmati']++;
                                    $dati_orario[$id_studente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi_firmati']++;
                                    $dati_orario[$id_studente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['numero_periodi_firmati']++;
                                }
                            }
                        }
                        //inserire qua il calcolo per ore se serve
                    }
                }
            }
        }
        elseif (
                $tipo_visualizzazione == 'docenti'
                || $tipo_visualizzazione == 'ruolo_generale'
                || $tipo_visualizzazione == 'docente_generale'
                || $tipo_visualizzazione == 'docente_ruolo'
                )
        {

            $firme_docenti = array();
            $firme_docenti = estrai_firme_periodo($inizio, $fine);
            $elenco_firme_ordinato = array();
            foreach($firme_docenti as $firma)
            {
                $elenco_firme_ordinato[$firma['id_professore']][$firma['id_classe']][$firma['id_materia']][$firma['data']] = $firma;
            }


            $tipi_corso_temp = estrai_tipi_corso();
            $tipi_corso = array();
            $elenco_lunghezze_periodo = array();
            foreach($tipi_corso_temp as $tipo_corso)
            {
                $tipi_corso[$tipo_corso['id_tipo_corso']] = $tipo_corso;
                if($tipo_corso['lunghezza_periodo'] > 0)
                {
                    $elenco_lunghezze_periodo[$tipo_corso['lunghezza_periodo']] = $tipo_corso['lunghezza_periodo'];
                    $lunghezza_periodo_generale = $tipo_corso['lunghezza_periodo'];
                }
            }
            if(count($elenco_lunghezze_periodo) != 1)
            {
                $lunghezza_periodo_generale = 60;
            }


            if (!$id_ruolo){
                $id_ruolo = null;
            }

            if (!$id_professore){
                $id_professore = null;
            }

            $orario_docenti = array();
            $orario_docenti = estrai_orario_docenti_con_corsi($inizio, $fine, $calcola_assenze, $id_ruolo, $id_professore);

            if($tipo_visualizzazione != 'docenti')
            {
                $dati_orario = array();
                foreach ($orario_docenti as $id_docente => $orario_docente)
                {
                    foreach ($orario_docente as $singolo_orario)
                    {
                        $dati_orario[$id_docente]['dati']['descrizione_docente'] = $singolo_orario['cognome'] . " " . $singolo_orario['nome'];
                        $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['dati']['descrizione_materia'] = $singolo_orario['descrizione_materia_riferimento'];

                        if($tipi_corso[$singolo_orario['id_tipo_corso']]['tipo_periodi'] == 'PERIODI')
                        {
                            $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi']++;
                            $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['totale']['numero_periodi']++;
                            $dati_orario[$id_docente]['totali']['totale_settimana'][$singolo_orario['settimana_anno']]['numero_periodi']++;
                            //$dati_orario[$id_docente]['totali']['totale_complessivo']['numero_periodi']++;
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['dati']['descrizione_materia'] = $singolo_orario['descrizione_materia'];
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['dati']['id_materia_riferimento'] = $singolo_orario['id_materia_riferimento'];
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi']++;
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['numero_periodi']++;

                            if(count($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']]) > 0)
                            {
                                foreach($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']] as $data_firma => $ora_firmata)
                                {
                                    if($data_firma >= $singolo_orario['data_inizio'] and $data_firma <= $singolo_orario['data_fine'])
                                    {
                                        $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi_firmati']++;
                                        $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['totale']['numero_periodi_firmati']++;
                                        $dati_orario[$id_docente]['totali']['totale_settimana'][$singolo_orario['settimana_anno']]['numero_periodi_firmati']++;

                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi_firmati']++;
                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['numero_periodi_firmati']++;
                                    }
                                }
                            }
                        }
                        elseif($tipi_corso[$singolo_orario['id_tipo_corso']]['tipo_periodi'] == 'ORE')
                        {
                            //sommo i minuti totali e trasformo ogni volta il totale minuti per calcolare il numero ore/periodi
                            $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali'] += $singolo_orario['minuti_periodo'];
                            $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['settimane'][$singolo_orario['settimana_anno']]['lunghezza_periodo'] = $tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo'];
                            $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['totale']['minuti_totali'] += $singolo_orario['minuti_periodo'];
                            $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['totale']['lunghezza_periodo'] = $tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo'];

                            $dati_orario[$id_docente]['totali']['totale_settimana'][$singolo_orario['settimana_anno']]['minuti_totali'] += $singolo_orario['minuti_periodo'];
                            $dati_orario[$id_docente]['totali']['totale_settimana'][$singolo_orario['settimana_anno']]['lunghezza_periodo'] = $tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo'];
                            $dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_totali'] += $singolo_orario['minuti_periodo'];
                            $dati_orario[$id_docente]['totali']['totale_complessivo']['lunghezza_periodo'] = $tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo'];

                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['dati']['descrizione_materia'] = $singolo_orario['descrizione_materia'];
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['dati']['id_materia_riferimento'] = $singolo_orario['id_materia_riferimento'];

                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali'] += $singolo_orario['minuti_periodo'];
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi'] = intval($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali']/$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']);
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_resto'] = sprintf("%'.02d\n", ($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali']%$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']));

                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_totali'] += $singolo_orario['minuti_periodo'];
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['numero_periodi'] = intval($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_totali']/$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']);
                            $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_resto'] = sprintf("%'.02d\n", ($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_totali']%$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']));

                            if(count($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']]) > 0)
                            {
                                foreach($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']] as $data_firma => $ora_firmata)
                                {
                                    if($data_firma >= $singolo_orario['data_inizio'] and $data_firma <= $singolo_orario['data_fine'])
                                    {
                                        $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali_firmati'] += $singolo_orario['minuti_periodo'];
                                        $dati_orario[$id_docente]['materie'][$singolo_orario['id_materia_riferimento']]['totale']['minuti_totali_firmati'] += $singolo_orario['minuti_periodo'];

                                        $dati_orario[$id_docente]['totali']['totale_settimana'][$singolo_orario['settimana_anno']]['minuti_totali_firmati'] += $singolo_orario['minuti_periodo'];
                                        $dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_totali_firmati'] += $singolo_orario['minuti_periodo'];

                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali_firmati'] += $singolo_orario['minuti_periodo'];
                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['numero_periodi_firmati'] = intval($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali_firmati']/$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']);
                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_resto_firmati'] = sprintf("%'.02d\n", ($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['settimane'][$singolo_orario['settimana_anno']]['minuti_totali_firmati']%$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']));

                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_totali_firmati'] += $singolo_orario['minuti_periodo'];
                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['numero_periodi_firmati'] = intval($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_totali_firmati']/$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']);
                                        $dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_resto_firmati'] = sprintf("%'.02d\n", ($dati_orario[$id_docente]['sotto_materie'][$singolo_orario['id_materia']]['totale']['minuti_totali_firmati']%$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']));
                                    }
                                }
                            }
                        }
                    }
                }
                $dati_orario_tmp = $dati_orario;
                foreach($dati_orario_tmp as $id_docente => $orario_docente_tmp)
                {
                    //$dati_orario[$id_docente]['totali']['totale_complessivo']['numero_periodi'] = 0;
                    foreach($orario_docente_tmp['materie'] as $id_materia_riferimento_tmp => $valore)
                    {
                        foreach($valore['settimane'] as $settimana => $dati)
                        {
                            $dati_orario[$id_docente]['materie'][$id_materia_riferimento_tmp]['settimane'][$settimana]['numero_periodi'] = "";
                            $dati_orario[$id_docente]['materie'][$id_materia_riferimento_tmp]['settimane'][$settimana]['numero_periodi_firmati'] = "";
                        }
                        $dati_orario[$id_docente]['materie'][$id_materia_riferimento_tmp]['totale']['numero_periodi'] = "";
                        $dati_orario[$id_docente]['materie'][$id_materia_riferimento_tmp]['totale']['numero_periodi_firmati'] = "";
                    }
                    foreach($orario_docente_tmp['sotto_materie'] as $id_materia_tmp => $valore)
                    {
                        $dati_orario[$id_docente]['totali']['totale_complessivo']['numero_periodi'] += $valore['totale']['numero_periodi'];
                        $dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto'] += $valore['totale']['minuti_resto'];
                        $dati_orario[$id_docente]['totali']['totale_complessivo']['numero_periodi_firmati'] += $valore['totale']['numero_periodi_firmati'];
                        $dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto_firmati'] += $valore['totale']['minuti_resto_firmati'];
                    }
                    foreach($orario_docente_tmp['totali']['totale_settimana'] as $settimana => $dati)
                    {
                        $dati_orario[$id_docente]['totali']['totale_settimana'][$settimana]['numero_periodi'] = "";
                        $dati_orario[$id_docente]['totali']['totale_settimana'][$settimana]['numero_periodi_firmati'] = "";
                    }
                    $dati_orario[$id_docente]['totali']['totale_complessivo']['numero_periodi'] += intval($dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto']/$lunghezza_periodo_generale);
                    $dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto'] = sprintf("%'.02d\n", ($dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto']%$lunghezza_periodo_generale));
                    $dati_orario[$id_docente]['totali']['totale_complessivo']['numero_periodi_firmati'] += intval($dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto_firmati']/$lunghezza_periodo_generale);
                    $dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto_firmati'] = sprintf("%'.02d\n", ($dati_orario[$id_docente]['totali']['totale_complessivo']['minuti_resto_firmati']%$lunghezza_periodo_generale));
                }
            }

            $elenco_ruoli = estrai_elenco_ruoli();

            $orario_docenti_ruoli = array();
            $ore_da_aggiungere = array();
            $ore_da_aggiungere_firmate = array();
            $periodi_da_aggiungere = array();
            $periodi_da_aggiungere_firmati = array();
            $minuti_totali_resto_ruolo = array();
            $minuti_totali_resto_ruolo_firmati = array();
            $ore_totali_resto_ruolo = array();
            $ore_totali_resto_ruolo_firmate = array();
            foreach ($orario_docenti as $id_docente => $orario_docente)
            {
                $ore_da_aggiungere_totali = 0;
                $ore_da_aggiungere_totali_firmate = 0;
                $periodi_da_aggiungere_totali = 0;
                $periodi_da_aggiungere_totali_firmati = 0;
                $minuti_totali_resto = 0;
                $minuti_totali_resto_firmati = 0;

                foreach ($orario_docente as $singolo_orario)
                {
                    $orario_docenti_ruoli[$id_docente]['dati']['descrizione_docente'] = $singolo_orario['cognome'] . " " . $singolo_orario['nome'];
                    $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['dati']['descrizione_ruolo'] = $singolo_orario['ruolo_descrizione'];
                    if($tipi_corso[$singolo_orario['id_tipo_corso']]['tipo_periodi'] == 'PERIODI')
                    {
                        $periodi_da_aggiungere[$id_docente][$singolo_orario['id_ruolo']]++;
                        $periodi_da_aggiungere_totali++;
                        if(count($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']]) > 0)
                        {
                            foreach($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']] as $data_firma => $ora_firmata)
                            {
                                if($data_firma >= $singolo_orario['data_inizio'] and $data_firma <= $singolo_orario['data_fine'])
                                {
                                    $periodi_da_aggiungere_firmati[$id_docente][$singolo_orario['id_ruolo']]++;
                                    $periodi_da_aggiungere_totali_firmati++;
                                }
                            }
                        }
                    }
                    elseif($tipi_corso[$singolo_orario['id_tipo_corso']]['tipo_periodi'] == 'ORE')
                    {
                        //sommo i minuti totali e trasformo ogni volta il totale minuti per calcolare il numero ore/periodi
                        $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_totali'] += $singolo_orario['minuti_periodo'];
                        $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_ore'] = intval($orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_totali']/$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']);
                        $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_resto'] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_totali'] % $tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo'];
                        $ore_da_aggiungere[$id_docente][$singolo_orario['id_ruolo']] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_ore'];
                        $ore_totali_resto_ruolo[$id_docente][$singolo_orario['id_ruolo']] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_ore'];
                        $minuti_totali_resto_ruolo[$id_docente][$singolo_orario['id_ruolo']] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_resto'];

                        if(count($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']]) > 0)
                        {
                            foreach($elenco_firme_ordinato[$id_docente][$singolo_orario['id_classe']][$singolo_orario['id_materia']] as $data_firma => $ora_firmata)
                            {
                                if($data_firma >= $singolo_orario['data_inizio'] and $data_firma <= $singolo_orario['data_fine'])
                                {
                                    $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_totali_firmati'] += $singolo_orario['minuti_periodo'];
                                    $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_ore_firmate'] = intval($orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_totali_firmati']/$tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo']);
                                    $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_resto_firmati'] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_totali_firmati'] % $tipi_corso[$singolo_orario['id_tipo_corso']]['lunghezza_periodo'];
                                    $ore_da_aggiungere_firmate[$id_docente][$singolo_orario['id_ruolo']] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_ore_firmate'];
                                    $ore_totali_resto_ruolo_firmate[$id_docente][$singolo_orario['id_ruolo']] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_ore_firmate'];
                                    $minuti_totali_resto_ruolo_firmati[$id_docente][$singolo_orario['id_ruolo']] = $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['minuti_resto_firmati'];
                                }
                            }
                        }
                    }
                    $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_periodi'] = ($periodi_da_aggiungere[$id_docente][$singolo_orario['id_ruolo']] + $ore_da_aggiungere[$id_docente][$singolo_orario['id_ruolo']]);
                    $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_orario['id_ruolo']]['valori']['numero_periodi_firmati'] = ($periodi_da_aggiungere_firmati[$id_docente][$singolo_orario['id_ruolo']] + $ore_da_aggiungere_firmate[$id_docente][$singolo_orario['id_ruolo']]);
                }
                //ciclo per ore totali
                foreach($minuti_totali_resto_ruolo[$id_docente] as $minuti_ruolo)
                {
                    $minuti_totali_resto += $minuti_ruolo;
                }
                $ore_da_aggiungere_totali = intval($minuti_totali_resto/$lunghezza_periodo_generale);
                $minuti_totali_resto = $minuti_totali_resto%$lunghezza_periodo_generale;
                foreach($ore_totali_resto_ruolo[$id_docente] as $ore_ruolo)
                {
                    $ore_da_aggiungere_totali += $ore_ruolo;
                }
                $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi'] += $periodi_da_aggiungere_totali;
                $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi'] += $ore_da_aggiungere_totali;

                if($minuti_totali_resto > 0)
                {
                    $minuti_totali_resto = sprintf("%'.02d\n", $minuti_totali_resto);
                    $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi'] = $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi'] . ":" . $minuti_totali_resto;
                }
                else
                {
                    $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi'] = $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi'];
                }
                //ripeto sezione per ore firmate
                foreach($minuti_totali_resto_ruolo_firmati[$id_docente] as $minuti_ruolo_firmati)
                {
                    $minuti_totali_resto_firmati += $minuti_ruolo_firmati;
                }
                $ore_da_aggiungere_totali_firmate = intval($minuti_totali_resto_firmati/$lunghezza_periodo_generale);
                $minuti_totali_resto_firmati = $minuti_totali_resto_firmati%$lunghezza_periodo_generale;
                foreach($ore_totali_resto_ruolo_firmate[$id_docente] as $ore_ruolo_firmate)
                {
                    $ore_da_aggiungere_totali_firmate += $ore_ruolo_firmate;
                }
                $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi_firmati'] += $periodi_da_aggiungere_totali_firmati;
                $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi_firmati'] += $ore_da_aggiungere_totali_firmate;

                if($minuti_totali_resto_firmati > 0)
                {
                    $minuti_totali_resto_firmati = sprintf("%'.02d\n", $minuti_totali_resto_firmati);
                    $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi_firmati'] = $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi_firmati'] . ":" . $minuti_totali_resto_firmati;
                }
                else
                {
                    $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi_firmati'] = $orario_docenti_ruoli[$id_docente]['totali']['totale_docente']['numero_periodi_firmati'];
                }


                if ($id_professore == $id_docente)
                {
                    $docente_analizzato['id_docente'] = $id_docente;
                    $docente_analizzato['descrizione'] = $orario_docenti_ruoli[$id_docente]['dati']['descrizione_docente'];
                    $template->assign('docente_analizzato', $docente_analizzato);
                }
            }

            foreach($orario_docenti_ruoli as $id_docente => $orario_docente)
            {
                foreach($elenco_ruoli as $singolo_ruolo)
                {
                    if(!(is_array($orario_docente['ruoli'][$singolo_ruolo['id_ruolo']])))
                    {
                        $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_ruolo['id_ruolo']]['dati']['descrizione_ruolo'] = $singolo_ruolo['descrizione'];
                        $orario_docenti_ruoli[$id_docente]['ruoli'][$singolo_ruolo['id_ruolo']]['valori']['numero_periodi'] = 0;
                    }

                    if ($id_ruolo == $singolo_ruolo['id_ruolo'])
                    {
                        $ruolo_analizzato = $singolo_ruolo;
                        $template->assign('ruolo_analizzato', $ruolo_analizzato);
                    }
                }
            }
            $template->assign('elenco_ruoli', $elenco_ruoli);
            $template->assign('orario_docenti_ruoli', $orario_docenti_ruoli);
        }

        //{{{ <editor-fold defaultstate="collapsed" desc="Esportazioni">
        if ($tipo_esportazione != '')
        {
            $tipo_file_esportato = "xls";
            $testo_excel = '<html>
                <head>
                </head>

                <body>
                <table><tr><td colspan="5">Stampato il ' . date('d/m/Y \a\l\l\e H:i') . '</td></tr>
                    <tr><td colspan="5">Periodo selezionato: dal ' . date('d/m/Y', $inizio) . ' al ' . date('d/m/Y', $fine) . '</td></tr>
                    <tr><td colspan="5">Includi le ore con campanelle rosse: ' . $campanelle_rosse . '</td></tr>
                    <tr><td>&nbsp;</td></tr></table>
                    ';

            if ($tipo_esportazione == 'classi')
            {
                //{{{ <editor-fold defaultstate="collapsed">
                $tipo_stampa = 'export_ore_classi_corsi';

                //compatto
                foreach($dati_orario as $classe)
                {
                    $testo_excel .= '<table><tr><td>Classe: ' . $classe['dati']['descrizione_classe'] . "</td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($classe['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px">' . $materia['settimane'][$valore]['numero_periodi'] . "</td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi'] . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                    }

                    $testo_excel .= "<td align='right' colspan='2'><b>TOTALE</b></td>";
                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>' . $classe['totali']['totale_settimana'][$valore]['numero_periodi'] . "</b></td>";
                    }
                    $testo_excel .= '<td style="width:100px"><b>' . $classe['totali']['totale_complessivo']['numero_periodi'] . "</b></td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }

                // esploso
                foreach($dati_orario as $classe)
                {
                    $testo_excel .= '<table><tr><td>Classe: ' . $classe['dati']['descrizione_classe'] . "</td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($classe['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px"><b>' . $materia['settimane'][$valore]['numero_periodi'] . "</b></td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi'] . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                        foreach ($classe['sotto_materie'] as $sotto_materia)
                        {
                            if($sotto_materia['dati']['id_materia_riferimento'] == $id_materia_riferimento)
                            {
                                $testo_excel .= "<td>" . '-----' . decode($sotto_materia['dati']['descrizione_materia']) . "</td><td><b>Ore</b></td>";
                                foreach ($settimane_anno as $valore)
                                {
                                    $testo_excel .= '<td style="width:100px">' . $sotto_materia['settimane'][$valore]['numero_periodi'] . "</td>";
                                }
                                $testo_excel .= "<td><b>" . $sotto_materia['totale']['numero_periodi'] . "</b></td>";
                                $testo_excel .= "</tr><tr>";
                            }
                        }
                        $testo_excel .= "</tr><tr>";
                    }
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }
                //}}} </editor-fold>
            }
            elseif ($tipo_esportazione == 'docenti')
            {
                //{{{ <editor-fold defaultstate="collapsed">
                $tipo_stampa = 'export_ore_docenti_ruoli_generale_corsi';

                $testo_excel .= "<table><tr><td></td><td></td><td align='center' colspan=".count($elenco_ruoli)."><b>Ruoli</b></td></tr>";

                $testo_excel .= "<tr><td><b>Docenti</b></td><td>&nbsp;</td>";
                foreach ($elenco_ruoli as $ruolo)
                {
                    $testo_excel .= '<td style="width:100px"><b>'. $ruolo['descrizione'] . "</b></td>";
                }
                $testo_excel .= "<td align='center'><b>TOT</b></td>";
                $testo_excel .= "</tr>";

                foreach($orario_docenti_ruoli as $docente)
                {
                    $testo_excel .= '<tr><td>' . $docente['dati']['descrizione_docente'] . "</td><td><b>Ore</b></td>";
                    foreach ($elenco_ruoli as $ruolo)
                    {
                        $testo_excel .= '<td style="width:100px">' . intval($docente['ruoli'][$ruolo['id_ruolo']]['valori']['numero_periodi']) . "</td>";
                    }
                    $testo_excel .= "<td><b>" . $docente['totali']['totale_docente']['numero_periodi'] . "</b></td>";
                    $testo_excel .= "</tr>";
                }

                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                    //}}} </editor-fold>
            }
            elseif ($tipo_esportazione == 'ruolo_generale')
            {
                //{{{ <editor-fold defaultstate="collapsed">
                $tipo_stampa = 'export_ore_docenti_ruolo_singolo_corsi';

                //compatto
                foreach($dati_orario as $docente)
                {
                    $testo_excel .= '<table><tr><td><b>Docente: ' . $docente['dati']['descrizione_docente'] . "</b></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Ruolo analizzato: " . $ruolo_analizzato['descrizione'] . "</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($docente['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px">' . $materia['settimane'][$valore]['numero_periodi'] . "</td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi']  . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                    }

                    $testo_excel .= "<td align='right' colspan='2'><b>TOTALE</b></td>";
                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_settimana'][$valore]['numero_periodi'] . "</b></td>";
                    }
                    $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_complessivo']['numero_periodi'] . "</b></td>";

                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }

                //esploso
                foreach($dati_orario as $docente)
                {
                    $testo_excel .= '<table><tr><td><b>Docente: ' . $docente['dati']['descrizione_docente'] . "</b></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Ruolo analizzato: " . $ruolo_analizzato['descrizione'] . "</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($docente['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px"><b>' . $materia['settimane'][$valore]['numero_periodi'] . "</b></td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi']  . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                        foreach ($docente['sotto_materie'] as $sotto_materia)
                        {
                            if($sotto_materia['dati']['id_materia_riferimento'] == $id_materia_riferimento)
                            {
                                $testo_excel .= "<td>" . '-----' . decode($sotto_materia['dati']['descrizione_materia']) . "</td><td><b>Ore</b></td>";
                                foreach ($settimane_anno as $valore)
                                {
                                    $testo_excel .= '<td style="width:100px">' . $sotto_materia['settimane'][$valore]['numero_periodi'] . "</td>";
                                }
                                $testo_excel .= "<td><b>" . $sotto_materia['totale']['numero_periodi'] . "</b></td>";
                                $testo_excel .= "</tr><tr>";
                            }
                        }
                        $testo_excel .= "</tr><tr>";
                    }

                    $testo_excel .= "<td align='right' colspan='2'><b>TOTALE</b></td>";
                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_settimana'][$valore]['numero_periodi'] . "</b></td>";
                    }
                    $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_complessivo']['numero_periodi'] . "</b></td>";

                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }
                //}}} </editor-fold>
            }
            elseif ($tipo_esportazione == 'docente_generale')
            {
                //{{{ <editor-fold defaultstate="collapsed">
                $tipo_stampa = 'export_ore_docente_singolo_corsi';

                // compatto
                foreach($dati_orario as $docente)
                {
                    $testo_excel .= '<table><tr><td>Docente: ' . $docente['dati']['descrizione_docente'] . "</td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($docente['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px">' . $materia['settimane'][$valore]['numero_periodi'] . "</td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi']  . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                    }

                    $testo_excel .= "<td align='right' colspan='2'><b>TOTALE</b></td>";
                    foreach ($settimane_anno as $valore)
                    {

                        $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_settimana'][$valore]['numero_periodi'] . "</b></td>";
                    }
                    $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_complessivo']['numero_periodi'] . "</b></td>";

                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }

                // esploso
                foreach($dati_orario as $docente)
                {
                    $testo_excel .= '<table><tr><td>Docente: ' . $docente['dati']['descrizione_docente'] . "</td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($docente['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px"><b>' . $materia['settimane'][$valore]['numero_periodi'] . "</b></td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi']  . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                        foreach ($docente['sotto_materie'] as $sotto_materia)
                        {
                            if($sotto_materia['dati']['id_materia_riferimento'] == $id_materia_riferimento)
                            {
                                $testo_excel .= "<td>" . '-----' . decode($sotto_materia['dati']['descrizione_materia']) . "</td><td><b>Ore</b></td>";
                                foreach ($settimane_anno as $valore)
                                {
                                    $testo_excel .= '<td style="width:100px">' . $sotto_materia['settimane'][$valore]['numero_periodi'] . "</td>";
                                }
                                $testo_excel .= "<td>" . $sotto_materia['totale']['numero_periodi'] . "</td>";
                                $testo_excel .= "</tr><tr>";
                            }
                        }
                        $testo_excel .= "</tr><tr>";
                    }

                    $testo_excel .= "<td align='right' colspan='2'><b>TOTALE</b></td>";
                    foreach ($settimane_anno as $valore)
                    {

                        $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_settimana'][$valore]['numero_periodi'] . "</b></td>";
                    }
                    $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_complessivo']['numero_periodi'] . "</b></td>";

                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }
                //}}} </editor-fold>
            }
            elseif ($tipo_esportazione == 'docente_ruolo')
            {
                //{{{ <editor-fold defaultstate="collapsed">
                $tipo_stampa = 'export_ore_docente_ruolo_specificis_corsi';

                //compatto
                foreach($dati_orario as $docente)
                {
                    $testo_excel .= '<table><tr><td><b>Docente: ' . $docente['dati']['descrizione_docente'] . "</b></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Ruolo analizzato: " . $ruolo_analizzato['descrizione'] . "</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($docente['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px">' . $materia['settimane'][$valore]['numero_periodi'] . "</td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi']  . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                    }

                    $testo_excel .= "<td align='right' colspan='2'><b>TOTALE</b></td>";
                    foreach ($settimane_anno as $valore)
                    {

                        $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_settimana'][$valore]['numero_periodi'] . "</b></td>";
                    }
                    $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_complessivo']['numero_periodi'] . "</b></td>";

                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }

                //esploso
                foreach($dati_orario as $docente)
                {
                    $testo_excel .= '<table><tr><td><b>Docente: ' . $docente['dati']['descrizione_docente'] . "</b></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Ruolo analizzato: " . $ruolo_analizzato['descrizione'] . "</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td></td><td></td><td align='center' colspan=".count($settimane_anno)."><b>Settimane</b></td></tr></table>";
                    $testo_excel .= "<table><tr><td><b>Materie</b></td><td><b>Settimane</b></td>";

                    foreach ($settimane_anno as $valore)
                    {
                        $testo_excel .= '<td style="width:100px"><b>'. $valore . "</b></td>";
                    }
                    $testo_excel .= "<td align='center'><b>TOT</b></td>";
                    $testo_excel .= "</tr><tr>";

                    foreach ($docente['materie'] as $id_materia_riferimento => $materia)
                    {
                        $testo_excel .= "<td><b>" . decode($materia['dati']['descrizione_materia']) . "</b></td><td><b>Ore</b></td>";
                        foreach ($settimane_anno as $valore)
                        {
                            $testo_excel .= '<td style="width:100px">' . $materia['settimane'][$valore]['numero_periodi'] . "</td>";
                        }
                        $testo_excel .= "<td><b>" . $materia['totale']['numero_periodi']  . "</b></td>";
                        $testo_excel .= "</tr><tr>";
                        foreach ($docente['sotto_materie'] as $sotto_materia)
                        {
                            if($sotto_materia['dati']['id_materia_riferimento'] == $id_materia_riferimento)
                            {
                                $testo_excel .= "<td>" . '-----' . decode($sotto_materia['dati']['descrizione_materia']) . "</td><td><b>Ore</b></td>";
                                foreach ($settimane_anno as $valore)
                                {
                                    $testo_excel .= '<td style="width:100px">' . $sotto_materia['settimane'][$valore]['numero_periodi'] . "</td>";
                                }
                                $testo_excel .= "<td>" . $sotto_materia['totale']['numero_periodi'] . "</td>";
                                $testo_excel .= "</tr><tr>";
                            }
                        }
                        $testo_excel .= "</tr><tr>";
                    }

                    $testo_excel .= "<td align='right' colspan='2'><b>TOTALE</b></td>";
                    foreach ($settimane_anno as $valore)
                    {

                        $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_settimana'][$valore]['numero_periodi'] . "</b></td>";
                    }
                    $testo_excel .= '<td style="width:100px"><b>' . $docente['totali']['totale_complessivo']['numero_periodi'] . "</b></td>";

                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr><tr><td>&nbsp;</td>";
                    $testo_excel .= "</tr></table>";
                }
                //}}} </editor-fold>
            }

            if($tipo_file_esportato == 'xls')
            {
                //cancello tutte i file temporanei fatti da più di un'ora
                $dir = 'tmp_xls';

                CleanFiles($dir);
                //creo i nuovi file temporanei
                $file=basename(tempnam($dir, 'tmp'));
                rename($dir . '/' . $file, $dir . '/' . $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls');
                $file = $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls';
                //Salva il file xls come file
                $nuovo_nome = $dir . '/' . $file;
                $handle = fopen($nuovo_nome, 'w');
                fwrite($handle,$testo_excel);
                fclose($handle);

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
            }
        }
        //}}} </editor-fold>

        $template->assign('dati_orario', $dati_orario);
        //}}} </editor-fold>
        break;
    case "progettazione_corsi":
        //{{{ <editor-fold defaultstate="collapsed">
        $template->assign("db_key",$db_key);
        $template->assign("tipo_utente", 'amministratore');
        $messaggio = '';

        if (!in_array($tipo_visualizzazione, ['elenco', 'gruppi'])){
            $tipo_visualizzazione = "";
        }
        $template->assign("tipo_visualizzazione", $tipo_visualizzazione);
        $template->assign("sezioni_aperte_gruppi", $sezioni_aperte_gruppi);
        $template->assign("sezioni_aperte_gruppi_array", json_decode($sezioni_aperte_gruppi, true));

        //modifica corso
        //aggiungere colonna monteore personale docente
        //aggiungere inserimento di monteore personale docente


        switch ($operazione)
        {
            case "inserimento_docenti_corso":
                //{{{ <editor-fold defaultstate="collapsed">
                if ((int)$id_classe > 0 && (int)$id_materia > 0)
                {
                    $abbinamenti_presenti = [];
                    $elenco_docenti = [];
                    $abbinamenti_presenti_tmp = estrai_abbinamenti($id_classe, 'corso', 'anno_succ');

                    foreach ($abbinamenti_presenti_tmp as $abbinamento)
                    {
                        if ($abbinamento['id_materia'] == $id_materia)
                        {
                            $abbinamenti_presenti[$abbinamento['id_professore']] = $abbinamento['id_professore'];
                            $elenco_docenti[$abbinamento['id_professore']]['docente'] = $abbinamento['cognome'] . ' ' . $abbinamento['nome'];
                        }
                    }

                    foreach ($docenti_corsi as $id_docente => $dati)
                    {
                        if ($dati['selezionato'] == 'SI')
                        {
                            if (!in_array($id_docente, $abbinamenti_presenti))
                            {
                                if ((int)$dati['monte_ore_personale'] > 0){
                                    $monteore = (int)$dati['monte_ore_personale'];
                                } else {
                                    $monteore = 0;
                                }
                                $results = inserisci_abbinamento($id_docente, $id_materia, $id_classe, '', $current_user, 0,1,1,1,'NO', $dati['id_ruolo'], "anno_succ", $monteore);
                            }
                            else
                            {
                                $messaggio .= $elenco_docenti[$id_docente]['docente'] . " già abbinato<br>";
                            }
                        }
                    }

                    if ($messaggio == '')
                    {
                        $messaggio = "Inserimento effettuato";
                    }

                    $template->assign('corso_corrente', $id_classe);
                }
                else
                {
                    $messaggio = "Errore, corso non trovato";
                }
                //}}} </editor-fold>
                break;
            case "inserimento_gruppi_corso":
                //{{{ <editor-fold defaultstate="collapsed">
                if ((int)$id_classe > 0 && (int)$id_materia > 0)
                {
                    $abbinamenti_presenti = [];
                    $elenco_gruppi = [];
                    $abbinamenti_presenti_tmp = estrai_gruppi_corso($id_classe);

                    foreach ($abbinamenti_presenti_tmp as $abbinamento)
                    {
                        $abbinamenti_presenti[$abbinamento['id_classe_collegata']] = $abbinamento['id_classe_collegata'];
                        $elenco_gruppi[$abbinamento['id_classe_collegata']]['descrizione'] = $abbinamento['descrizione'];
                    }

                    foreach ($gruppi_corsi as $id_classe_inserimento => $dati)
                    {
                        if ($dati['selezionato'] == 'SI')
                        {
                            if (!in_array($id_classe_inserimento, $abbinamenti_presenti))
                            {
                                $results = inserisci_gruppo_corso($id_classe, $id_classe_inserimento, '', $dati['sovrapposto'], $current_user);
                            }
                            else
                            {
                                $messaggio .= $elenco_gruppi[$id_classe_inserimento]['descrizione'] . " già presente<br>";
                            }
                        }
                    }

                    if ($messaggio == '')
                    {
                        $messaggio = "Inserimento effettuato";
                    }

                    $template->assign('corso_corrente', $id_classe);
                }
                else
                {
                    $messaggio = "Errore, corso non trovato";
                }
                //}}} </editor-fold>
                break;
            case "inserimento_nuovo_corso":
                //{{{ <editor-fold defaultstate="collapsed">
                $insert = inserisci_corso($titolo_nuovo_corso, $descrizione_nuovo_corso, 1, 0, '', -1, $current_user, $sottotitolo_nuovo_corso, $id_materia_riferimento_nuovo_corso, $monteore_nuovo_corso, $id_tipo_nuovo_corso, 'anno_succ');

                if (isset($fonti_finanziamento_nuovo_corso) && count($fonti_finanziamento_nuovo_corso) > 0)
                {
                    elimina_finanziamento_corsi($insert['id_classe']);
                    foreach ($fonti_finanziamento_nuovo_corso as $id_fonte)
                    {
                        inserisci_finanziamento_corso($insert['id_classe'], $id_fonte);
                    }
                }

                $template->assign('corso_corrente', $insert['id_classe']);
                //}}} </editor-fold>
                break;
            case "modifica_corso":
                //{{{ <editor-fold defaultstate="collapsed">
                $dati_corso = estrai_corsi_succ()['corsi'][$id_modifica_corso];

                modifica_corso($id_modifica_corso,$dati_corso['id_materia'],$titolo_modifica_corso,$descrizione_modifica_corso,$dati_corso['periodo'],$dati_corso['max_iscritti'],$dati_corso['vincoli_corso'],$current_user, $sottotitolo_modifica_corso, $dati_corso['id_materia_riferimento'], $monteore_modifica_corso, $id_tipo_modifica_corso, 'anno_succ');

                elimina_finanziamento_corsi($id_modifica_corso);
                if (isset($fonti_finanziamento_modifica_corso) && count($fonti_finanziamento_modifica_corso) > 0)
                {
                    foreach ($fonti_finanziamento_modifica_corso as $id_fonte)
                    {
                        inserisci_finanziamento_corso($id_modifica_corso, $id_fonte);
                    }
                }

                $template->assign('corso_corrente', $id_modifica_corso);
                //}}} </editor-fold>
                break;
            default:
                break;
        }

        if (in_array($tipo_visualizzazione, ['elenco', 'gruppi'])){
            $elenco_corsi_succ = estrai_corsi_succ();

            $elenco_tipi_corso = estrai_tipi_corso();
            $template->assign('elenco_tipi_corso', $elenco_tipi_corso);

            $elenco_materie = estrai_materie();
            $template->assign('elenco_materie', $elenco_materie);

            $elenco_fonti_finanziamento = estrai_fonti_finanziamento();
            $template->assign('elenco_fonti_finanziamento', $elenco_fonti_finanziamento);

            $elenco_ruoli = estrai_elenco_ruoli();
            $template->assign('elenco_ruoli', $elenco_ruoli);

            $elenco_classi_principali = estrai_classi('base', 'classi_principali', 'anno_succ');
            $template->assign('elenco_classi_principali', $elenco_classi_principali);

            if ($tipo_visualizzazione == 'elenco') {
                unset($elenco_corsi_succ['nuovo_array']);
            } elseif ($tipo_visualizzazione == 'gruppi') {
                unset($elenco_corsi_succ['corsi']);
            }

            // Filtro i risultati
            if (trim($search_corso) != '' || trim($search_docenti) != '' || trim($search_gruppo) != '')
            {
                $elenco_corsi_tmp = [];

                if ($tipo_visualizzazione == 'elenco'){
                    foreach ($elenco_corsi_succ['corsi'] as $id_corso => $dati_corso)
                    {
                        $mantieni = false;
                        if (strpos(strtoupper($dati_corso['descrizione']), strtoupper(trim($search_corso))) !== false)
                        {
                            $mantieni = true;
                        }

                        if (!$mantieni && trim($search_docenti) != '' && is_array($dati_corso['professori']) && !empty($dati_corso['professori']))
                        {
                            foreach ($dati_corso['professori'] as $professore)
                            {
                                if (strpos(strtoupper($professore['nome_completo']), strtoupper(trim($search_docenti))) !== false)
                                {
                                    $mantieni = true;
                                    break;
                                }
                            }
                        }

                        if (!$mantieni && trim($search_gruppo) != '' && is_array($dati_corso['raggruppamenti']) && !empty($dati_corso['raggruppamenti']))
                        {
                            foreach ($dati_corso['raggruppamenti'] as $id_raggruppamento => $dati_raggruppamento)
                            {
                                if (strpos(strtoupper($dati_raggruppamento['descrizione']), strtoupper(trim($search_gruppo))) !== false)
                                {
                                    $mantieni = true;
                                    break;
                                }
                            }
                        }

                        if ($mantieni)
                        {
                            $elenco_corsi_tmp[$id_corso] = $dati_corso;
                        }
                    }

                    $elenco_corsi_succ['corsi'] = $elenco_corsi_tmp;
                } elseif ($tipo_visualizzazione == 'gruppi'){

                }
            }
            $template->assign('elenco_corsi_succ', $elenco_corsi_succ);
            $template->assign('search_corso', $search_corso);
            $template->assign('search_docenti', $search_docenti);
            $template->assign('search_gruppo', $search_gruppo);
        }

        $template->assign('messaggio', $messaggio);
        //}}} </editor-fold>
        break;
    case 'anagrafica':
        //{{{ <editor-fold defaultstate="collapsed">
        //  tabella smarty popolata da chiamata ajax
        $template->assign("anno_scolastico_attuale", estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"));
        $template->assign("db_key",$db_key);
        $template->assign("tipo_utente", 'amministratore');
        //}}} </editor-fold>
        break;
    case "preiscrizioni_colloqui":
        //{{{ <editor-fold defaultstate="collapsed">
        $template->assign("db_key",$db_key);
        $template->assign("tipo_utente", 'amministratore');
        $mastercom_id = estrai_parametri_singoli('MASTERCOM_ID');
        $messaggio = '';

        $stampa_scheda_candidato_sa_mi = estrai_stampa_personalizzata('stampa_scheda_candidato_sa_mi');
        if (!is_null($stampa_scheda_candidato_sa_mi)){
            $template->assign("stampa_scheda_candidato_sa_mi", $stampa_scheda_candidato_sa_mi);
        }

        switch ($operazione)
        {
            case 'salva_colloquio':
                //{{{ <editor-fold defaultstate="collapsed">
                $id_preiscrizione = $preiscrizione['id_preiscrizione'];

                if ($id_preiscrizione > 0)
                {
                    $dati_preiscrizione = estrai_preiscrizione($id_preiscrizione);

                    foreach ($preiscrizione['dati_colloquio'] as $key => $value)
                    {
                        $value = str_replace('"', '\\"', $value);
                        $value = str_replace("\r\n", "\\n", $value);
                        $preiscrizione['dati_colloquio'][$key] = $value;
                    }

                    $dati_colloquio_serializzati = json_encode($preiscrizione['dati_colloquio'], JSON_HEX_APOS);

                    $result = modifica_preiscrizione($id_preiscrizione, $dati_preiscrizione['codice_fiscale_studente'], $dati_preiscrizione['cognome_studente'], $dati_preiscrizione['nome_studente'], $dati_preiscrizione['cognome_padre'], $dati_preiscrizione['nome_padre'], $dati_preiscrizione['cognome_madre'], $dati_preiscrizione['nome_madre'], $dati_colloquio_serializzati, $preiscrizione['colloquio'], $preiscrizione['punteggio'], $preiscrizione['data_colloquio'], $current_user);
                    if ($result){
                        $messaggio = print_label('Salvataggio effettuato');
                    } else {
                        $messaggio = print_label('Errore salvataggio!');
                    }
                }
                else
                {
                    $messaggio = print_label('Errore!');
                }
                //}}} </editor-fold>
                break;
            case 'esporta_csv':
                //{{{ <editor-fold defaultstate="collapsed">
                $dati = "SELECT *
                        FROM preiscrizioni_studenti
                        WHERE flag_canc = 0
                            AND anno_scolastico = '".strtoupper(encode(trim($filter_anno_scolastico)))."' ";

                $result = pgsql_query($dati) or die ("Invalid $dati");
                $numero = pg_num_rows($result);

                $preiscrizioni = array();
                if($numero > 0)
                {
                    $preiscrizioni = pg_fetch_all($result);
                }

                $elenco_chiavi_stud = array();
                $elenco_chiavi_colloquio = array();
                $elenco_chiavi_colloquio['COLLOQUIO EFFETTUATO'] = 'COLLOQUIO EFFETTUATO';
                $elenco_chiavi_colloquio['DATA COLLOQUIO'] = 'DATA COLLOQUIO';
                $elenco_chiavi_colloquio['PUNTEGGIO'] = 'PUNTEGGIO';
                $elenco_chiavi_colloquio['ANNO SCOLASTICO'] = 'ANNO SCOLASTICO';

                foreach($preiscrizioni as $studente)
                {
                    $temp_dati_stud = json_decode($studente['dati_modulo_serializzati'], true);
                    $temp_colloquio_stud = json_decode($studente['dati_colloquio_serializzati'], true);
                    foreach ($temp_dati_stud as $key => $value)
                    {
                        $elenco_chiavi_stud[$key] = $key;
                    }
                    foreach ($temp_colloquio_stud as $key => $value)
                    {
                        $elenco_chiavi_colloquio[$key] = $key;
                    }
                }

                foreach($preiscrizioni as $studente)
                {
                    $preiscrizioni_finale[$studente['id_preiscrizione']]['COLLOQUIO EFFETTUATO MC'] = $studente['colloquio_effettuato'];
                    $preiscrizioni_finale[$studente['id_preiscrizione']]['DATA COLLOQUIO MC'] = $studente['data_colloquio'];
                    $preiscrizioni_finale[$studente['id_preiscrizione']]['PUNTEGGIO'] = $studente['punteggio'];
                    $preiscrizioni_finale[$studente['id_preiscrizione']]['ANNO SCOLASTICO'] = $studente['anno_scolastico'];
                    $temp_dati_stud = json_decode($studente['dati_modulo_serializzati'], true);
                    $temp_colloquio_stud = json_decode($studente['dati_colloquio_serializzati'], true);

                    foreach ($elenco_chiavi_stud as $value)
                    {
                        $preiscrizioni_finale[$studente['id_preiscrizione']][$value] = $temp_dati_stud[$value];
                    }

                    foreach ($elenco_chiavi_colloquio as $value)
                    {
                        $preiscrizioni_finale[$studente['id_preiscrizione']]['COLLOQUIO ' . $value] = $temp_colloquio_stud[$value];
                    }
                }

                //$testo_csv = '';
                $testo_csv = '<table border="1">';
                //$cont = 0;
                foreach ($preiscrizioni_finale as $studente)
                {
                    //if($cont == 0)
                    //{
                        $testo_csv .= '<tr>';
                        foreach($studente as $key => $valore)
                        {
                            //$testo_csv .= '"' . $key . '",';
                            $testo_csv .= '<td>' . $key . '</td>';
                        }
                        //$testo_csv .= chr(13) . chr(10);
                        //$cont++;
                        $testo_csv .= '</tr>';
                        break;
                    //}
                }

                foreach ($preiscrizioni_finale as $studente)
                {
                    $testo_csv .= '<tr>';
                    foreach($studente as $key => $value)
                    {
                        if ($key === 'DATA COLLOQUIO MC') {
                            if ($value > 0){
                                $valore = date('Y-m-d', $value);
                            } else {
                                $valore = "";
                            }
                        }
                        elseif (in_array(str_replace(" ", "", strtoupper($key)),
                        [
                            'STUDENTE:LUOGO_NASCITA',
                            'STUDENTE:CITTA',
                            'STUDENTE:PROVENIENZA_SCOLASTICA_COMUNE',
                            'STUDENTE:CITTA_PARROCCHIA',
                            'PADRE:LUOGO_NASCITA',
                            'PADRE:CITTA_RESIDENZA',
                            'MADRE:LUOGO_NASCITA',
                            'MADRE:CITTA_RESIDENZA',
                            'TUTORE:LUOGO_NASCITA',
                            'TUTORE:CITTA_RESIDENZA'])) {
                            $valore =  estrai_dati_comune(encode($value))['descrizione'];
                        }
                        elseif(stripos($value, "SELEZIONARE") === false)
                        {
                            $valore = $value;
                        }
                        else
                        {
                            $valore = "";
                        }
                        //$testo_csv .= '"' . str_replace("\"", ' ',str_replace("\n", ' ', $valore)) . '",';
                        $testo_csv .= '<td>' .  preg_replace('/[^A-Za-z0-9_ àòèìùÀÉÈÌÒÙ\-\_\!\@\/\?\.\,\;\:\']/', ' ', str_ireplace('<BR>',' ',str_ireplace('&nbsp',' ',decode($valore)))) . '</td>';
                    }
                    //$testo_csv .= chr(13) . chr(10);
                    $testo_csv .= '</tr>';
                }
                $testo_csv .= '</table>';

                //cancello tutte i file temporanei fatti da più di un'ora
                $dir = 'tmp_xls';
                $tipo_stampa = 'preiscrizioni_' . str_replace('/', '-', $filter_anno_scolastico);

                CleanFiles($dir);
                //creo i nuovi file temporanei
                $file=basename(tempnam($dir, 'tmp'));
                rename($dir . '/' . $file, $dir . '/' . $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls');
                $file = $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls';
                //Salva il file xls come file
                $nuovo_nome = $dir . '/' . $file;
                $handle = fopen($nuovo_nome, 'w');
                fwrite($handle,$testo_csv);
                fclose($handle);

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
                //}}} </editor-fold>
                break;
            case 'stampa':
                switch ($stampa['tipo_stampa']) {
                    case 'stampa_scheda_candidato_sa_mi':
                        $id_preiscrizione = $stampa['id_preiscrizione'];
                        $pdf = new MASTERCOM_PDF();
                        require_once "stampe/stampe_personalizzate/stampa_scheda_candidato_sa_mi.php";
                        $pdf->Output($preiscrizione['cognome_studente'].'_'.$preiscrizione['nome_studente'].'_scheda_candidato_' . date('Y-m-d_H-i') . '.pdf', 'D');
                        exit;
                        break;
                    default:
                        break;
                }
                break;
            case 'stampa_selezionati':
                switch ($stampa['tipo_stampa']) {
                    case 'stampa_scheda_candidato_sa_mi':
                        $pdf = new MASTERCOM_PDF();

                        foreach ($preiscrizioni_da_stampare as $id_preiscrizione){
                            require "stampe/stampe_personalizzate/stampa_scheda_candidato_sa_mi.php";
                        }

                        $pdf->Output('stampa_schede_candidati_selezionati_' . str_replace("/", "_", $filter_anno_scolastico) .'_' . date('Y-m-d_H-i') . '.pdf', 'D');
                        exit;
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }

        $elenco_anni_preiscrizioni = estrai_anni_preiscrizioni();
        if (!isset($filter_anno_scolastico)){
            $filter_anno_scolastico = $elenco_anni_preiscrizioni[0];
        }

        $filter = [
            "studente"              =>  encode(trim($search_studente)),
            "padre"                 =>  encode(trim($search_padre)),
            "madre"                 =>  encode(trim($search_madre)),
            "colloquio_effettuato"  =>  encode(trim($search_colloquio)),
            "anno_scolastico"       =>  encode(trim($filter_anno_scolastico))
        ];
        $elenco_studenti = estrai_studenti_preiscrizioni($filter);

        if ($mastercom_id == 'donbosco-bs'){
            // estraggo l'istituto da mostrare in tabella
            foreach ($elenco_studenti as $key => $dati_studente_preiscrizione){
                $elenco_studenti[$key]['istituto'] = $dati_studente_preiscrizione['dati_modulo_serializzati']['Studente:  istituto'];
            }

            if (trim($search_istituto) != ""){
                foreach ($elenco_studenti as $key => $dati_studente_preiscrizione) {
                    if (stripos(trim($dati_studente_preiscrizione['istituto']), trim($search_istituto)) === false){
                        unset($elenco_studenti[$key]);
                    }
                }
            }

            $mostra_istituto = 'SI';
            $template->assign("mostra_istituto", $mostra_istituto);
            $template->assign('search_istituto', $search_istituto);
        }

//        foreach ($elenco_studenti as $studente)
//        {
//            $ciao['SCELTA 1'][$studente['dati_colloquio_serializzati']['SCELTA 1']] = $studente['dati_colloquio_serializzati']['SCELTA 1'];
//            $ciao['SCELTA 2'][$studente['dati_colloquio_serializzati']['SCELTA 2']] = $studente['dati_colloquio_serializzati']['SCELTA 2'];
//            $ciao['SCELTA DEFINITIVA'][$studente['dati_colloquio_serializzati']['SCELTA DEFINITIVA']] = $studente['dati_colloquio_serializzati']['SCELTA DEFINITIVA'];
//            $ciao['CONSIGLIO ORIENTATIVO'][$studente['dati_colloquio_serializzati']['CONSIGLIO ORIENTATIVO']] = $studente['dati_colloquio_serializzati']['CONSIGLIO ORIENTATIVO'];
//            $ciao['NON DEFINITO'][$studente['dati_colloquio_serializzati']['NON DEFINITO']] = $studente['dati_colloquio_serializzati']['NON DEFINITO'];
//            $ciao['SCELTA INDIRIZZO'][$studente['dati_colloquio_serializzati']['SCELTA INDIRIZZO']] = $studente['dati_colloquio_serializzati']['SCELTA INDIRIZZO'];
//        }

        $template->assign('search_studente', $search_studente);
        $template->assign('search_padre', $search_padre);
        $template->assign('search_madre', $search_madre);
        $template->assign('search_colloquio', $search_colloquio);
        $template->assign('filter_anno_scolastico', $filter_anno_scolastico);
        $template->assign('elenco_anni_preiscrizioni', $elenco_anni_preiscrizioni);
        $template->assign('elenco_studenti', $elenco_studenti);
        $template->assign('messaggio', $messaggio);
        //}}} </editor-fold>
        break;
    case "preiscrizioni_graduatorie":
    case "preiscrizioni_importazione":
        //{{{ <editor-fold defaultstate="collapsed">
        $template->assign("db_key",$db_key);
        $template->assign("db_official", $db_official);
        $template->assign("tipo_utente", 'amministratore');

        $elenco_anni_preiscrizioni = estrai_anni_preiscrizioni();
        if (!isset($filter_anno_scolastico)){
            $filter_anno_scolastico = $elenco_anni_preiscrizioni[0];
        }

        $template->assign('filter_anno_scolastico', $filter_anno_scolastico);
        $template->assign('filter_anno_scolastico_id', str_replace('/', '', $filter_anno_scolastico));
        $template->assign('elenco_anni_preiscrizioni', $elenco_anni_preiscrizioni);
        //}}} </editor-fold>
        break;
    case "preiscrizioni_elenco":
        $template->assign("db_key", $db_key);
        $template->assign("tipo_utente", 'amministratore');

        function traduci_dato($dato){
            $valore = "";

            switch ($dato['tipo']){
                case 'select':
                    if ($dato['valore'] != ''){
                        $valore = $dato['opzioni'][$dato['valore']];
                    }
                    break;
                case 'check':
                        $valore = ($dato['valore'] == true) ? 'SI' : 'NO';
                    break;
                case 'comune':
                    if ($dato['valore'] != ''){
                        $comune = estrai_dati_comune($dato['valore']);
                        $valore = $comune['descrizione'] . ' (' . $comune['provincia'] . ')';
                    }
                    break;
                case 'stato':
                    if ($dato['valore'] != ''){
                        $stato = estrai_nazione($dato['valore']);
                        $valore = $stato['descrizione'];
                    }
                    break;
                case 'text':
                case 'area':
                case 'date':
                default:
                    $text = str_ireplace("\n", "", $dato['valore']);
                    $text = str_ireplace("\r", "", $text);
                    $text = str_ireplace("\t", "", $text);
                    $text = str_ireplace("<br>", "", $text);
                    $valore = $text;
                    break;
            }

            return $valore;
        }

        $lista_template = nextapi_call('phpoffice/phpword/getTemplatesList', 'GET', ["filter" => ["locazioni" => ['PREISCRIZIONI']]], $current_key);
        $template->assign("id_template", $lista_template[0]['id_template']);

        switch ($operazione){
            case 'esporta_moduli':
                if ($filtro == ""){
                    $filtro = 'open';
                }

                $report = nextapi_call('registrations/templates/'.$id_percorso.'/report', 'GET', null, $current_key);
                $csv = "";

                foreach ($report['cols'] as $col){
                    if ($col['ref'] != -1 && $col['label'] != null) {
                        $csv .= $col['label'] . "\t";
                    }
                }

                $csv .= "\n";

                foreach ($report['rows'] as $row){
                    if ($row['modulo::status'] == $filtro){
                        foreach ($report['cols'] as $key_col => $col) {
                            if ($col['ref'] != -1 && $col['label'] != null) {
                                $dato = str_replace("\t", " ", $row[$key_col]);
                                $dato = str_replace("\n", ". ", $dato);
                                $dato = str_replace("\n", "", $dato);
                                $dato = str_replace("\r", "", $dato);
                                $dato = str_replace("<br>", ". ", $dato);
                                $csv .= $dato . "\t";
                            }
                        }
                        $csv .= "\n";
                    }
                }

                // $moduli_tmp = nextapi_call('registrations', 'GET', ["id_template" => $id_percorso, "status" => $filtro, "dati_importazioni" => 'SI', "dati_colloqui" => 'SI'], $current_key);
                // $moduli = [];
                // $elenco_campi_generici = [
                //     "nome"  =>  ["label" => "Nome", "campo" => "nome"],
                //     "fase"  =>  ["label" => "Fase", "campo" => "fase"],
                //     "insert_date"  =>  ["label" => "Data inserimento", "campo" => "insert_date"],
                //     "insert_name"  =>  ["label" => "Inserito da", "campo" => "insert_name"],
                //     "punteggio_generale"  =>  ["label" => "Punteggio graduatorie", "campo" => "punteggio_generale"],
                // ];
                // $elenco_campi_opzioni = [];
                // $elenco_campi_studente = [];
                // $elenco_campi_parenti = [];
                // $elenco_campi_colloqui = [];

                // $csv = "";

                // // estraggo i moduli singoli
                // foreach ($moduli_tmp as $modulo){
                //     $dati_modulo = nextapi_call('registrations/editor/'.$modulo['_id'], 'GET', [], $current_key);
                //     $moduli[] = $dati_modulo;
                // }

                // // censisco tutti i campi
                // foreach ($moduli as $modulo){
                //     foreach ($modulo['opzioni'] as $opzione){
                //         $elenco_campi_opzioni[$opzione['campo']]['label'] = $opzione['label'];
                //         $elenco_campi_opzioni[$opzione['campo']]['campo'] = $opzione['campo'];
                //     }

                //     foreach ($modulo['studente'] as $studente){
                //         $elenco_campi_studente[$studente['campo']]['label'] = $studente['label'] . " (studente)";
                //         $elenco_campi_studente[$studente['campo']]['campo'] = $studente['campo'];
                //     }

                //     $i = 0;
                //     foreach ($modulo['colloqui'] as $colloquio){
                //         if (!(isset($colloquio['deleted']))){
                //             foreach ($colloquio as $campo => $valori){
                //                 $elenco_campi_colloqui[$campo . "_" . $i]['label'] = $campo . " (colloquio " . $i . ")";
                //                 $elenco_campi_colloqui[$campo . "_" . $i]['campo'] = $campo;
                //                 $elenco_campi_colloqui[$campo . "_" . $i]['indice'] = $i;
                //             }
                //             $i++;
                //         }
                //     }

                //     $modulo_parenti_tmp = [];
                //     $cont_par = 0;
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'P') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'A') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'M') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par1] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'Y') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] != 'P') && ($campi['valore'] != 'M') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }

                //     $i = 0;
                //     foreach ($modulo_parenti_tmp as $parente){
                //         foreach ($parente as $campi){
                //             if (isset($campi['campo'])){
                //                 $elenco_campi_parenti[$campi['campo'] . "_" . $i]['label'] = $campi['label'] . " (parente " . $i . ")";
                //                 $elenco_campi_parenti[$campi['campo'] . "_" . $i]['campo'] = $campi['campo'];
                //                 $elenco_campi_parenti[$campi['campo'] . "_" . $i]['indice'] = $i;
                //             }
                //         }
                //         $i++;
                //     }

                //     foreach ($modulo['pagante'] as $pagante) {
                //         $elenco_campi_pagante[$pagante['campo']]['label'] = $pagante['label'];
                //         $elenco_campi_pagante[$pagante['campo']]['campo'] = $pagante['campo'];
                //     }
                // }

                // // scrivo la riga dei titoli
                // foreach ($elenco_campi_generici as $campo) {
                //     $csv .= $campo['label'] . "\t";
                // }
                // foreach ($elenco_campi_opzioni as $campo) {
                //     $csv .= $campo['label'] . "\t";
                // }
                // $csv .= "Tipo studente\t";
                // foreach ($elenco_campi_studente as $campo) {
                //     $csv .= $campo['label'] . "\t";
                // }
                // foreach ($elenco_campi_parenti as $campo) {
                //     $csv .= $campo['label'] . "\t";
                // }
                // foreach ($elenco_campi_pagante as $campo) {
                //     $csv .= $campo['label'] . "\t";
                // }
                // foreach ($elenco_campi_colloqui as $campo) {
                //     $csv .= $campo['label'] . "\t";
                // }

                // $csv .= "\n";

                // // scrivo i dati dei moduli
                // foreach ($moduli as $modulo) {
                //     foreach ($elenco_campi_generici as $campo) {
                //         if ($campo['campo'] == 'fase'){
                //             $csv .= $modulo['fasi'][$modulo[$campo['campo']]] . ' (' . $modulo[$campo['campo']] . ')';
                //         } else {
                //             $csv .= $modulo[$campo['campo']];
                //         }
                //         $csv .= "\t";
                //     }

                //     foreach ($elenco_campi_opzioni as $campo) {
                //         foreach ($modulo['opzioni'] as $opzioni_modulo){
                //             if ($opzioni_modulo['campo'] == $campo['campo']){
                //                 $csv .= traduci_dato($opzioni_modulo);
                //             }
                //         }
                //         $csv .= "\t";
                //     }

                //     $csv .= ($modulo['label_id'] > 0) ? 'INTERNO' : 'ESTERNO';
                //     $csv .= "\t";

                //     foreach ($elenco_campi_studente as $campo) {
                //         foreach ($modulo['studente'] as $campo_studente_modulo){
                //             if ($campo_studente_modulo['campo'] == $campo['campo']){
                //                 $csv .= traduci_dato($campo_studente_modulo);
                //             }
                //         }
                //         $csv .= "\t";
                //     }

                //     $modulo_parenti_tmp = [];
                //     $cont_par = 0;
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'P') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'A') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'M') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par1] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] == 'Y') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par1] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }
                //     foreach ($modulo['parenti'] as $parente){
                //         $inserito = 'NO';
                //         foreach ($parente as $campi){
                //             if(($campi['campo'] == 'parentela') && ($campi['valore'] != 'P') && ($campi['valore'] != 'M') && ($inserito == 'NO')){
                //                 $modulo_parenti_tmp[$cont_par] = $parente;
                //                 $inserito = 'SI';
                //             }
                //         }
                //         if($inserito == 'SI'){
                //             $cont_par++;
                //         }
                //     }

                //     $parenti_tmp = array_values($modulo_parenti_tmp);
                //     foreach ($elenco_campi_parenti as $campo) {
                //         foreach ($parenti_tmp[$campo['indice']] as $campi_parente_modulo) {
                //             if ($campi_parente_modulo['campo'] == $campo['campo']){
                //                 $csv .= traduci_dato($campi_parente_modulo);
                //             }
                //         }
                //         $csv .= "\t";
                //     }

                //     foreach ($elenco_campi_pagante as $campo) {
                //         foreach ($modulo['pagante'] as $pagante_modulo) {
                //             if ($pagante_modulo['campo'] == $campo['campo']) {
                //                 $csv .= traduci_dato($pagante_modulo);
                //             }
                //         }
                //         $csv .= "\t";
                //     }

                //     $colloqui_tmp = array_filter($modulo['colloqui'], function ($val){
                //         return (!isset($val['deleted']));
                //     });
                //     $colloqui_tmp = array_values($colloqui_tmp);
                //     foreach ($elenco_campi_colloqui as $campo) {
                //         foreach ($colloqui_tmp[$campo['indice']] as $campo_colloquio_modulo => $valore) {
                //             if ($campo_colloquio_modulo == $campo['campo']) {
                //                 switch ($campo['campo']){
                //                     case 'data':
                //                         if ($valore != ""){
                //                             $csv .= date('d/m/Y', $valore);
                //                         }
                //                         break;
                //                     case 'effettuato':
                //                         $csv .= ($valore == 1) ? 'SI' : 'NO';
                //                         break;
                //                     case 'fase':
                //                         $csv .= $modulo['fasi'][$valore] . ' (' . $valore . ')';
                //                         break;
                //                     default:
                //                         $csv .= $valore;
                //                         break;
                //                 }
                //             }
                //         }
                //         $csv .= "\t";
                //     }

                //     $csv .= "\n";
                // }

                //cancello tutte i file temporanei fatti da più di un'ora
                $dir = 'tmp_xls';
                $tipo_stampa = 'preiscrizioni_elenco_' . date('Y-m-d_H:i');

                CleanFiles($dir);
                //creo i nuovi file temporanei
                $file = basename(tempnam($dir, 'tmp'));
                rename($dir . '/' . $file, $dir . '/' . $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls');
                $file = $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls';
                //Salva il file xls come file
                $nuovo_nome = $dir . '/' . $file;
                $handle = fopen($nuovo_nome, 'w');
                fwrite($handle, $csv);
                fclose($handle);

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
                exit;
                break;
            default:
                break;
        }

        break;
    case "preiscrizioni_attivazione_moduli_studenti":
        $template->assign("db_key", $db_key);
        $template->assign("tipo_utente", 'amministratore');

        $template->assign("id_percorso", $id_percorso);

        if ($id_percorso != ""){
            // carico gli studenti
            $sql = "SELECT
                    id_studente,
                    cognome,
                    nome,
                    id_classe,
                    id_indirizzo,
                    classe,
                    sezione,
                    codice_indirizzi,
                    descrizione_indirizzi
                FROM studenti_completi
                WHERE ordinamento = '0'
                    OR preiscrizioni = 1
                ORDER BY codice_indirizzi, classe, sezione, cognome, nome
                ";
            $result = pgsql_query($sql) or die("Invalid $sql");
            $elenco_studenti = pg_fetch_all($result);

            if (count($elenco_studenti) > 0){
                foreach ($elenco_studenti as $key => $studente){
                    foreach ($studente as $key2 => $value){
                        $elenco_studenti[$key][$key2] = decode($value);
                    }

                    //filtrare successivamente quelli gia' creati qui
                }
            }

            $template->assign("elenco_studenti", $elenco_studenti);
        }

        switch ($operazione){
            case 'attiva_moduli_studenti':
                if ($id_percorso != ""){
                    if (count($genera_studente) > 0){
                        $mast_file = '/etc/mastercom/configuration.ini';
                        $mast_conf = parse_ini_file($mast_file, true);

                        if (isset($mast_conf['mastercom']) && isset($mast_conf['mastercom']['public_url']) && strlen($mast_conf['mastercom']['public_url']) > 0) {
                            $host = $mast_conf['mastercom']['public_url'];
                        } else {
                            $host = $protocol . "://" . estrai_parametri_singoli("MASTERCOM_ID") . '.registroelettronico.com';
                        }

                        $csv = "Percorso: {$nome_percorso}\n\n";

                        $csv .= "Moduli generati per i seguenti studenti\n\n";

                        $csv .= "Studente\tClasse\tIndirizzo\tParente\tParentela\tEmail\tLink\n";

                        foreach ($genera_studente as $id_studente){

                            $preiscrizione_studente = estrai_studente_preiscrizione_e_importazioni($id_studente, $db_key);
                            if ($preiscrizione_studente[0]['id_modulo'] != ""){
                                $modulo = nextapi_call("modules/" . $preiscrizione_studente[0]['id_modulo'], 'GET', [], $current_key);
                                $owner_id = $modulo['owner_id'];
                            }

                            $parenti = estrai_genitori_studente($id_studente);
                            if (count($parenti) > 0) {
                                $couch_parente_scelto = null;
                                $parente_scelto = null;
                                foreach ($parenti as $parente) {
                                    // estraggo l'utente del parente
                                    $as = str_replace('/', '_', estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE'));
                                    $couch_parente = nextapi_call("user/mastercom/parent/" . $parente['id_parente'] . "/" . $as, 'GET', [], $current_key);

                                    if (isset($couch_parente['_id'])) {
                                        if (is_null($couch_parente_scelto)){
                                            // se non settato prendo il primo
                                            $couch_parente_scelto = $couch_parente;
                                            $parente_scelto = $parente;
                                        }

                                        switch ($tipo_generazione){
                                            case 'paganti':
                                                if ($parente['pagante'] == 't') {
                                                    $couch_parente_scelto = $couch_parente;
                                                    $parente_scelto = $parente;
                                                }
                                                break;
                                            case 'madri':
                                                if ($parente['parentela'] == 'M') {
                                                    $couch_parente_scelto = $couch_parente;
                                                    $parente_scelto = $parente;
                                                }
                                                break;
                                            case 'padri':
                                                if ($parente['parentela'] == 'P') {
                                                    $couch_parente_scelto = $couch_parente;
                                                    $parente_scelto = $parente;
                                                }
                                                break;
                                            // case 'proprietari_modulo_preiscrizione':
                                                // if ($couch_parente['_id'] == $owner_id){
                                                //     $couch_parente_scelto = $couch_parente;
                                                //     $parente_scelto = $parente;
                                                // }
                                                // break;
                                            default:
                                                break;
                                        }
                                    }
                                }

                                if (!is_null($couch_parente_scelto)){
                                    $res = nextapi_call("registrations/template/{$id_percorso}/{$id_studente}", 'POST', ["owner_id" => $couch_parente_scelto['_id']], $current_key);
                                    $dati_studente = [];

                                    if (isset($res['_id'])) {
                                        foreach ($elenco_studenti as $studente) {
                                            if ($studente['id_studente'] == $id_studente) {
                                                $dati_studente = $studente;
                                                break;
                                            }
                                        }

                                        $csv .= $dati_studente['cognome'] . ' ' . $dati_studente['nome'] . "\t";
                                        $csv .= $dati_studente['classe'] . $dati_studente['sezione'] . "\t";
                                        $csv .= $dati_studente['descrizione_indirizzi'] . "\t";
                                        $csv .= $parente_scelto['cognome'] . ' ' . $parente_scelto['nome'] . "\t";
                                        $csv .= $parente_scelto['parentela_estesa'] . "\t";
                                        $csv .= $parente_scelto['email'] . "\t";

                                        if (isset($couch_parente_scelto['_id'])) {
                                            //creo il token per il link del parente
                                            // scadenza
                                            if ($data_scadenza != '' && $ora_scadenza != ''){
                                                $scadenza = strtotime($data_scadenza . ' ' . $ora_scadenza) - time();
                                            } else {
                                                $scadenza = strtotime('+1 hour') - time();
                                            }
                                            $dati_parente = nextapi_call('contacts/' . $couch_parente_scelto['_id'] . '/contact-data/tokens/' . $scadenza, 'GET', [], $current_key)[0];

                                            if ($dati_parente['token'] != "") {
                                                $csv .= $host . "/preiscrizioni/index.html?a=" . $dati_parente['token'];
                                            }
                                        }
                                    } else {
                                        $csv .= $dati_studente['cognome'] . ' ' . $dati_studente['nome'] . "\t";
                                        $csv .= $dati_studente['classe'] . $dati_studente['sezione'] . ' ' . $dati_studente['codice_indirizzi'] . "\t";
                                        $csv .= "Modulo non generato";
                                    }

                                    $csv .= "\n";
                                }
                            } else {
                                $csv .= $dati_studente['cognome'] . ' ' . $dati_studente['nome'] . "\t";
                                $csv .= $dati_studente['classe'] . $dati_studente['sezione'] . ' ' . $dati_studente['codice_indirizzi'] . "\t";
                                $csv .= "\n";
                            }
                        }
                    } else {
                        $messaggio = print_label("Errore, nessuno studente selezionato");
                    }
                } else {
                    $messaggio = print_label("Errore, nessun percorso trovato");
                }

                //cancello tutte i file temporanei fatti da più di un'ora
                $dir = 'tmp_xls';
                $tipo_stampa = 'creazione_moduli_studenti_' . date('Y-m-d_H:i');

                CleanFiles($dir);
                //creo i nuovi file temporanei
                $file = basename(tempnam($dir, 'tmp'));
                rename($dir . '/' . $file, $dir . '/' . $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls');
                $file = $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls';
                //Salva il file xls come file
                $nuovo_nome = $dir . '/' . $file;
                $handle = fopen($nuovo_nome, 'w');
                fwrite($handle, $csv);
                fclose($handle);

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
                exit;
                break;
            default:
                break;
        }

        break;
    case "preiscrizioni_graduatorie_moduli_studenti":
        $template->assign("db_key", $db_key);
        $template->assign("tipo_utente", 'amministratore');
        break;
    case "preiscrizioni_importazione_moduli_studenti":
        $db_futuri = estrai_database_futuri();
        foreach ($db_futuri as $key => $db){
            $db_futuri[$key]['anno'] = explode('_', $db['nome'])[1] . '/' . explode('_', $db['nome'])[2];
        }
        $template->assign("db_futuri", $db_futuri);

        $importazione = 'NO';
        if ($db_key == $db_official){
            $importazione = 'SI';
        }
        $template->assign("importazione", $importazione);

        $template->assign("db_key", $db_key);
        $template->assign("tipo_utente", 'amministratore');
        break;
    case "controllo_colloqui_docenti_display":
    case "controllo_colloqui_generali_docenti_display":
        //{{{ <editor-fold defaultstate="collapsed">

        switch ($operazione) {
            case 'inserisci_colloqui':
                // inserimento colloqui docenti
                switch (true) {
                    case ($inserisci_generale_data == '' && $tipo_colloqui == 'generali'):
                        $messaggio = print_label('Errore! La data non è compilata');
                        break;
                    case ($inserisci_dal == '' && $tipo_colloqui == 'settimanali'):
                        $messaggio = print_label('Errore! La data di inizio non è compilata');
                        break;
                    case ($inserisci_al == '' && $tipo_colloqui == 'settimanali'):
                        $messaggio = print_label('Errore! La data di fine non è compilata');
                        break;
                    case ($inserisci_dal > $inserisci_al && $tipo_colloqui == 'settimanali'):
                        $messaggio = print_label('Errore! Ora di inizio non può essere maggiore di ora fine');
                        break;
                    case ($inserisci_dalle == '' || $inserisci_alle == ''):
                        $messaggio = print_label('Errore! Ora di inizio e fine devono essere compilate');
                        break;
                    case ($inserisci_dalle >= $inserisci_alle):
                        $messaggio = print_label('Errore! Ora inizio deve essere precedente a ora fine');
                        break;
                    case ($inserisci_posti <= 0):
                        $messaggio = print_label('Errore! Deve esserci almeno un posto disponibile');
                        break;
                    default:
                        // INSERIMENTO COLLOQUIO
                        // se devo inserire piu' di un colloquio creo un codice gruppo
                        //$codice_gruppo = (count($settimane) > 1) ? $current_user . "_" . time() : '';
                        $codice_gruppo = '';

                        // determino se ci sono abbinamenti a indirizzi e/o annualita' da fare
                        $array_abbinamenti = [];
                        if (is_array($inserisci_limiti) && count($inserisci_limiti) > 0) {
                            foreach ($inserisci_limiti as $tipo => $array_id) {
                                foreach ($array_id as $id) {
                                    $abb_tmp['id'] = $id;
                                    $abb_tmp['tipo'] = $tipo;
                                    $array_abbinamenti[] = $abb_tmp;
                                }
                            }
                        }

                        if ($tipo_colloqui == 'settimanali') {
                            $giorno_corr = strtotime($inserisci_dal.' 00:00');
                            $fine_ts = strtotime($inserisci_al.' 23:59');
                            $tipo_colloquio = 'INDIVIDUALE';
                        } elseif ($tipo_colloqui == 'generali') {
                            $giorno_corr = strtotime($inserisci_generale_data . ' 00:00');
                            $fine_ts = strtotime($inserisci_generale_data . ' 23:59');
                            $tipo_colloquio = 'GENERALE';
                        }
                        $invia_notifica = "SI";
                        while ($giorno_corr < $fine_ts) {
                            if (date('Y-m-d', $giorno_corr) >= date('Y-m-d', time())
                                    && (
                                            (in_array(date('N', $giorno_corr), $inserisci_giorni) && $tipo_colloqui == 'settimanali')
                                            ||
                                            $tipo_colloqui == 'generali'
                                        )
                                )
                                {
                                $stato = estrai_festivita_calendario($giorno_corr);
                                if ($stato['tipo_giornata'] == 'A') {
                                    foreach ($inserisci_docente as $id_docente => $dati_docente) {
                                        $annotazione_agenda = inserisci_annotazione_agenda(
                                            0,
                                            0,
                                            strtotime(date('Y-m-d', $giorno_corr) . ' ' . $inserisci_dalle . ':01'),
                                            strtotime(date('Y-m-d', $giorno_corr) . ' ' . $inserisci_alle),
                                            encode($dati_docente['titolo']),
                                            encode($dati_docente['testo']),
                                            $privata = 0,
                                            $current_user,
                                            $current_key,
                                            $colloquio = 1,
                                            intval($inserisci_posti),
                                            $id_docente,
                                            $codice_gruppo,
                                            $array_abbinamenti,
                                            $tipo_colloquio,
                                            $inserisci_posti_riservati,
                                            $invia_notifica
                                        );
                                    }
                                    $invia_notifica = "NO";
                                }
                            }
                            $giorno_corr = strtotime('+1 day', $giorno_corr);
                        }
                        $messaggio = print_label('Inserimento effettuato con successo');
                        break;
                }
                break;
            default:
                break;
        }

        // $elenco_indirizzi = estrai_indirizzi('anno_corr', 'fake');
        $elenco_indirizzi = estrai_indirizzi('anno_corr');
        $template->assign('elenco_indirizzi', $elenco_indirizzi);

        if ($primo_submit !== 'NO'){
        // PRIMA VOLTA CHE ENTRO
            $filtro_indirizzi = [];
            $tmp_filtro_indirizzi = array_column($elenco_indirizzi, 'id_indirizzo');
            $filtro_indirizzi = array_combine($tmp_filtro_indirizzi, $tmp_filtro_indirizzi);
            $inserisci_dal = date('Y-m-d');
            $inserisci_al = date('Y-m-d');
            $inserisci_generale_data = date('Y-m-d');
            $inserisci_dalle = '08:00';
            $inserisci_alle = '08:00';
            $inserisci_posti = 0;
            $inserisci_posti_riservati = 0;
            $max_posti = 0;
            // switch (date('N')) {
            //     case 1: $inserisci_giorni['lu'] = 1; break;
            //     case 2: $inserisci_giorni['ma'] = 2; break;
            //     case 3: $inserisci_giorni['me'] = 3; break;
            //     case 4: $inserisci_giorni['gio'] = 4; break;
            //     case 5: $inserisci_giorni['ve'] = 5; break;
            //     case 6: $inserisci_giorni['sa'] = 6; break;
            //     case 7: $inserisci_giorni['do'] = 7; break;
            //     default: break;
            // }
        }

        $elenco_docenti = [];
        if (!empty($filtro_indirizzi)){
            // estraggo tutti i professori associati agli indirizzi (fake compresi per gestire il caso in cui il professore è associato solo ad indirizzi fake)
            $elenco_docenti = estrai_professori_indirizzi($filtro_indirizzi);
            foreach ($elenco_docenti as $key => $value){
                $elenco_docenti[$key]['testo_indirizzi'] = implode(", ", array_column($value['indirizzi'], 'descrizione_indirizzi'));
            }
        }

        //fasce
        $select = "SELECT distinct to_timestamp(data_inizio)::time as data_inizio, to_timestamp(data_fine)::time as data_fine FROM orario_classi ORDER BY data_inizio, data_fine";
        $result = pgsql_query($select) or die("Invalid $select");
        $elenco_fasce = pg_fetch_all($result);
        foreach ($elenco_fasce as $key => $value) {
            $elenco_fasce[$key]['ora_inizio_tradotta'] = explode(':', $value['data_inizio'])[0].':'.explode(':', $value['data_inizio'])[1];
            $elenco_fasce[$key]['ora_fine_tradotta'] = explode(':', $value['data_fine'])[0] . ':' . explode(':', $value['data_fine'])[1];
        }

        //date colloqui generali
        $select = "SELECT distinct to_timestamp(data)::date as data, to_timestamp(data)::time as ora_inizio, to_timestamp(data_fine)::time as ora_fine FROM annotazioni_agenda WHERE flag_canc = 0 AND tipo = 'GENERALE' AND data >= ".time()." ORDER BY data, ora_inizio, ora_fine LIMIT 15";
        $result = pgsql_query($select) or die("Invalid $select");
        $elenco_date = pg_fetch_all($result);
        foreach ($elenco_date as $key => $value) {
            $elenco_date[$key]['ora_inizio_tradotta'] = explode(':', $value['ora_inizio'])[0].':'.explode(':', $value['ora_inizio'])[1];
            $elenco_date[$key]['ora_fine_tradotta'] = explode(':', $value['ora_fine'])[0] . ':' . explode(':', $value['ora_fine'])[1];
            $giorno = traduci_giorno_in_lettere(date('N', strtotime($value['data'])), false);
            $elenco_date[$key]['display'] = $giorno . '&emsp;' . date('d/m/Y', strtotime($value['data'])) . '&emsp;' . date('H:i', strtotime($value['ora_inizio'])) . ' - ' . date('H:i', strtotime($value['ora_fine']));
        }

        $template->assign('elenco_fasce', $elenco_fasce);
        $template->assign('elenco_date', $elenco_date);
        $template->assign('elenco_docenti', $elenco_docenti);
        $template->assign('inserisci_giorni', $inserisci_giorni);
        $template->assign('inserisci_dal', $inserisci_dal);
        $template->assign('inserisci_al', $inserisci_al);
        $template->assign('inserisci_generale_data', $inserisci_generale_data);
        $template->assign('inserisci_dalle', $inserisci_dalle);
        $template->assign('inserisci_alle', $inserisci_alle);
        $template->assign('inserisci_posti', $inserisci_posti);
        $template->assign('inserisci_posti_riservati', $inserisci_posti_riservati);
        $template->assign('max_posti', $max_posti);
        $template->assign('inserisci_titolo', $inserisci_titolo);
        $template->assign('inserisci_testo', $inserisci_testo);
        $template->assign('filtro_indirizzi', $filtro_indirizzi);
        $template->assign('primo_submit', $primo_submit);
        $template->assign('messaggio', $messaggio);
        //}}} </editor-fold>
        break;
    case "controllo_colloqui_docenti_ricerca":
    case "controllo_colloqui_generali_docenti_ricerca":
        //{{{ <editor-fold defaultstate="collapsed">
        $mod_prenotazione = estrai_parametri_singoli("MODALITA_PRENOTAZIONE_COLLOQUI");

        switch ($operazione) {
            case 'modifica_colloquio':
                if ($modifica_colloquio['id'] > 0){
                    $colloquio = estrai_annotazione_agenda($modifica_colloquio['id'], $current_user);
                    if ($colloquio['id_annotazione_agenda'] > 0){

                        if (!isset($modifica_colloquio['indirizzi'])){
                            $modifica_colloquio['indirizzi'] = [];
                        }

                        $id_studenti_prenotazioni = [];
                        foreach ($colloquio['slot_prenotazioni'] as $slot){
                            if ($slot['id_utente'] > 0){
                                $id_studenti_prenotazioni[$slot['id_utente']] = $slot['id_utente'];
                            }
                        }

                        $procedi_update = true;

                        if (empty($id_studenti_prenotazioni)){
                            //nessuna prenotazione -> assegno semplicemente i limiti
                            $procedi_update = true;
                        } elseif (!empty($modifica_colloquio['indirizzi'])){
                            $sql = "SELECT DISTINCT id_studente, id_indirizzo
                                    FROM studenti_completi
                                    WHERE id_studente IN (".implode(", ", $id_studenti_prenotazioni).")";
                            $result = pgsql_query($sql) or die("Invalid $sql");
                            $elenco_tmp = pg_fetch_all($result);

                            foreach ($elenco_tmp as $stud_ind) {
                                $elenco_indirizzi_studenti[$stud_ind['id_studente']][] = $stud_ind['id_indirizzo'];
                            }

                            foreach ($elenco_indirizzi_studenti as $id_studente => $indirizzi){
                                $studente_ok = false;
                                foreach ($indirizzi as $id_indirizzo){
                                    if (in_array($id_indirizzo, $modifica_colloquio['indirizzi'])){
                                        $studente_ok = true;
                                        break;
                                    }
                                }

                                if (!$studente_ok){
                                    // studente prenotato che verrebbe escluso dai nuovi limiti -> BLOCCO UPDATE
                                    $procedi_update = false;
                                    $messaggio = print_label("Errore! I limiti impostati escludono studenti già prenotati. Modifiche non salvate");
                                    break;
                                }
                            }
                        } else {
                            //nessun limite -> assegno a limiti l'array che sara' vuoto
                            $procedi_update = true;
                        }

                        $limiti = [];
                        foreach ($modifica_colloquio['indirizzi'] as $id_indirizzo){
                            $limiti[] = ["id" => $id_indirizzo, "tipo" => "indirizzo"];
                        }
                        foreach ($modifica_colloquio['annualita'] as $annualita){
                            $limiti[] = ["id" => $annualita, "tipo" => "annualita"];
                        }
                        if ($procedi_update){
                            $invia_notifica = 'SI';
                            if (intval($modifica_colloquio['posti'] < count($id_studenti_prenotazioni))){
                                $messaggio = print_label("Errore! Posti inseriti minori delle prenotazini presenti. Dati non salvati.");
                            } else {
                                modifica_annotazione_agenda($modifica_colloquio['id'],
                                                            $colloquio['id_classe'],
                                                            $colloquio['id_materia'],
                                                            strtotime($modifica_colloquio['data'].' '.$modifica_colloquio['ora_inizio']),
                                                            strtotime($modifica_colloquio['data'] . ' ' . $modifica_colloquio['ora_fine']),
                                                            encode($modifica_colloquio['titolo']),
                                                            encode($modifica_colloquio['testo']),
                                                            $colloquio['privata'],
                                                            $current_user,
                                                            $current_key,
                                                            $colloquio['colloquio'],
                                                            intval($modifica_colloquio['posti']),
                                                            'singola',
                                                            $limiti,
                                                            $invia_notifica);

                                if (date('Y-m-d H:m', strtotime($modifica_colloquio['data'] . ' ' . $modifica_colloquio['ora_inizio'])) != date('Y-m-d H:m', $colloquio['data'])){
                                    $titolo = 'Colloquio SPOSTATO';
                                    $messaggio = "Il colloquio con il docente " . $colloquio['autore'] . " del "
                                        . $colloquio['data_giorno'] . " dalle " . $colloquio['data_ora'] . " alle " . $colloquio['data_fine_ora'] . " è stato SPOSTATO al "
                                        . date('d/m/Y', strtotime($modifica_colloquio['data'])) . " dalle " . $modifica_colloquio['ora_inizio'] . " alle " . $modifica_colloquio['ora_fine'];
                                } elseif ($colloqui['slot'] != $modifica_colloquio['posti']){
                                    $titolo = 'Modifica ordine di ricevimento colloquio';
                                    $messaggio = "Nel colloquio con il docente " . $colloquio['autore'] . " del "
                                        . $colloquio['data_giorno'] . " dalle " . $colloquio['data_ora'] . " alle " . $colloquio['data_fine_ora'] . " è stato MODIFICATO l'ordine di RICEVIMENTO";
                                } else {
                                    $titolo = 'Colloquio modificato';
                                    $messaggio = "Il colloquio con il docente " . $colloquio['autore'] . " del "
                                        . $colloquio['data_giorno'] . " dalle " . $colloquio['data_ora'] . " alle " . $colloquio['data_fine_ora'] . " è stato MODIFICATO";
                                }

                                $lista_destinatari = [];
                                foreach ($colloquio['slot_prenotazioni'] as $slot){
                                    if ($slot['id_utente'] > 0){
                                        $genitori = estrai_genitori_studente($slot['id_utente']);
                                        if (!empty($genitori)){
                                            foreach ($genitori as $genitore){
                                                $destinatario['id'] = $genitore['id_parente'] . 'G'; //aggiungo un carattere perche' la funzione di invio messaggio toglie l'ultimo carattere del'id (??)
                                                $destinatario['tipo'] = 'G';
                                                $destinatario['tipo_nuovo_messenger'] = 'parente';
                                                $destinatario['id_nuovo_messenger'] = $genitore['id_parente'];
                                                $destinatario['id_studente_nuovo_messenger'] = $slot['id_utente'];
                                                $lista_destinatari[] = $destinatario;
                                            }
                                        }
                                    }
                                }

                                if (!empty($lista_destinatari)){
                                    messengerSendMessage($lista_destinatari, $titolo, $messaggio, $current_user, $current_key);
                                }
                                $messaggio = print_label('Colloquio modificato correttamente');
                            }
                        }
                    } else {
                        $messaggio = print_label('Errore! Colloquio non trovato');
                    }
                } else {
                    $messaggio = print_label('Errore! Nessun colloquio selezionato');
                }
                break;
            case 'inserisci_prenotazioni':
                $id_colloquio = $inserisci_prenotazioni['id'];
                $prenotazioni_da_inserire = json_decode($inserisci_prenotazioni['elenco'], true);

                $studenti_gia_prenotati = array_column(estrai_prenotazioni_colloqui($id_colloquio), 'id_utente');

                foreach ($prenotazioni_da_inserire as $id_prenotazione => $id_studente){
                    if ($id_studente > 0 && !in_array($id_studente, $studenti_gia_prenotati)){
                        modifica_prenotazione_colloquio($id_prenotazione, "", $id_studente, 0, $current_user, $current_key, null);
                        //inserisci_prenotazione_colloquio($id_colloquio, $slot, '', $id_studente, $current_user, $current_key);
                        $studenti_gia_prenotati[] = $id_studente;
                    } else {
                        // passaggio tra libero-riservato
                        modifica_prenotazione_colloquio($id_prenotazione, "", $id_studente, 0, $current_user, $current_key, null);
                    }
                }
                break;
            default:
                break;
        }

        $lista_professori = estrai_utenti("P");
        $search = trim($search);

        $elenco_indirizzi = estrai_indirizzi('anno_corr');

        if ($stato_secondario == 'controllo_colloqui_generali_docenti_ricerca'){
            //estraggo i giorni con colloqui generali
            $elenco_giorni[0] = [
                "display"   =>  print_label("Tutti i giorni"),
                "value"     =>  0,
                "data"     =>  0
            ];
            $sql = "SELECT DISTINCT to_timestamp(data)::date as data
                    FROM annotazioni_agenda
                    WHERE flag_canc = 0
                        AND tipo = 'GENERALE'
                    ORDER BY data";
            $result = pgsql_query($sql) or die("Invalid $sql");
            $elenco_giorni_tmp = pg_fetch_all($result);
            foreach ($elenco_giorni_tmp as $giorno_coll_generale) {
                $tmp = [];
                $tmp['display'] = date('d/m/Y', strtotime($giorno_coll_generale['data']));
                $tmp['value'] = $giorno_coll_generale['data'];
                $tmp['data'] = strtotime($giorno_coll_generale['data']);
                if ($cerca_data == $giorno_coll_generale['data']){
                    $tmp['selected'] = 'SI';
                }
                $elenco_giorni[] =  $tmp;
            }
        }

        if ($primo_submit == 'NO'){
            $cerca_dal_data = date('Y-m-d');
            $cerca_dal_ora = "08:00";
            $cerca_al_data = date('Y-m-d');
            $cerca_al_ora = "19:00";
            $solo_con_colloqui = 'SI';

            $selected = false;
            if(count($elenco_giorni) > 1){
                foreach ($elenco_giorni as $key => $giorno) {
                    if (date('Y-m-d', $giorno['data']) >= date('Y-m-d', time()) && !$selected) {
                        $selected = true;
                        $elenco_giorni[$key]['selected'] = 'SI';
                        $cerca_data = $giorno['value'];
                        break;
                    }
                }
            }
            if (!$selected){
                $elenco_giorni[0]['selected'] = 'SI';
                $cerca_data = $elenco_giorni[0]['value'];
            }
        }

        if ($cerca_dal_data !== '' && $cerca_dal_ora !== '' && $cerca_al_data !== '' && $cerca_al_ora !== '') {

            $elenco_colloqui = [];

            if ($stato_secondario == 'controllo_colloqui_docenti_ricerca'){
                $data_inizio = strtotime($cerca_dal_data . ' ' . $cerca_dal_ora);
                $data_fine = strtotime($cerca_al_data . ' ' . $cerca_al_ora);
                $tipo_estrazione = 'INDIVIDUALE';
            } elseif ($stato_secondario == 'controllo_colloqui_generali_docenti_ricerca') {
                if ($cerca_data > 0){
                    $data_inizio = strtotime($cerca_data . ' 00:00');
                    $data_fine = strtotime($cerca_data . ' 23:59');
                } else {
                    $anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
                    $data_inizio = strtotime(explode('/', $anno_scolastico_attuale)[0] . '-08-01 00:00');
                    $data_fine = strtotime(explode('/', $anno_scolastico_attuale)[1] . '-07-31 23:59');
                }
                $tipo_estrazione = 'GENERALE';
            }

            foreach ($lista_professori as $professore) {
                if (strlen($search) > 0) {
                    if (stripos($professore['cognome'] . " " . $professore['nome'], $search) !== false) {
                        $id_professore = $professore['id_utente'];
                    } else {
                        $id_professore = null;
                    }
                } else {
                    $id_professore = $professore['id_utente'];
                }

                // TODO: DA OTTIMIZZARE
                $elenco_tmp = estrai_elenco_colloqui_professore($id_professore, $data_inizio, $data_fine, $current_user, $tipo_estrazione);
                if (count($elenco_tmp) > 0){
                    foreach ($elenco_tmp as $key => $value){
                        if (count($value['elenco_indirizzi']) > 0){
                            $elenco_tmp[$key]['elenco_validita_indirizzi'] = implode(", ", array_column($value['elenco_indirizzi'], "descrizione"));
                        } else {
                            $elenco_tmp[$key]['elenco_validita_indirizzi'] = "<i>Tutti</i>";
                        }

                        if (count($value['elenco_annualita']) > 0){
                            $elenco_tmp[$key]['elenco_validita_annualita'] = implode(", ", array_column($value['elenco_annualita'], "id_abbinato"));
                        } else {
                            $elenco_tmp[$key]['elenco_validita_annualita'] = "<i>Tutte</i>";
                        }

                        if ($value['data_fine'] < time()){
                            $elenco_tmp[$key]['passato'] = 'SI';
                        }

                        $slot_prenotazioni = estrai_prenotazioni_colloqui($value['id_annotazione_agenda']);
                        $elenco_tmp[$key]['slot_prenotazioni'] = $slot_prenotazioni;

                        if (!empty($slot_prenotazioni)) {
                            $intervallo_tempo = $value['data_fine'] - $value['data_inizio'];
                            $step = intval($intervallo_tempo / count($slot_prenotazioni));
                            $cont = 0;

                            foreach ($slot_prenotazioni as $key2 => $slot) {
                                if ($mod_prenotazione !== 'NUMERICA'){
                                    $time = $cont * $step + $value['data'];

                                    $elenco_tmp[$key]['slot_prenotazioni'][$key2]['slot_data'] = $time;
                                    $elenco_tmp[$key]['slot_prenotazioni'][$key2]['slot_ora'] = date('G:i', $time);

                                    $cont++;
                                }

                                if ($slot['id_utente'] > 0) {
                                    $classe = estrai_classi_studente($slot['id_utente']);
                                    $elenco_tmp[$key]['slot_prenotazioni'][$key2]['classe'] = "{$classe[0]['classe']}&ordf; {$classe[0]['sezione']}";
                                }
                            }
                        }
                    }
                }

                if ($solo_con_colloqui != 'SI' || !empty($elenco_tmp)){
                    $elenco_colloqui[$id_professore]['id_professore'] = $professore['id_utente'];
                    $elenco_colloqui[$id_professore]['cognome'] = $professore['cognome'];
                    $elenco_colloqui[$id_professore]['nome'] = $professore['nome'];
                    $elenco_colloqui[$id_professore]['colloqui'] = $elenco_tmp;
                }
            }

            //estraggo gli studenti direttamente per l'inserimento delle prenotazioni
            $sql = "SELECT id_studente, cognome, nome, id_indirizzo, classe, sezione, ordinamento FROM studenti_completi ORDER BY cognome, nome";
            $result = pgsql_query($sql) or die("Invalid $sql");
            $studenti_tmp = pg_fetch_all($result);
            foreach ($studenti_tmp as $studente){
                $elenco_studenti[$studente['id_studente']]['id_studente'] = $studente['id_studente'];
                $elenco_studenti[$studente['id_studente']]['cognome'] = decode($studente['cognome']);
                $elenco_studenti[$studente['id_studente']]['nome'] = decode($studente['nome']);
                $elenco_studenti[$studente['id_studente']]['classe'] = $studente['classe'];
                $elenco_studenti[$studente['id_studente']]['indirizzi'][] = $studente['id_indirizzo'];
                if (!isset($elenco_studenti[$studente['id_studente']]['classe'])){
                    $elenco_studenti[$studente['id_studente']]['classe_composta'] = "";
                }
                if ($studente['ordinamento'] == '0'){
                    $elenco_studenti[$studente['id_studente']]['classe_composta'] = "(".$studente['classe']."ª ".$studente['sezione'].")";
                }
            }

            //ordinamento alfabetico
            $ord_arr = [];
            foreach ($elenco_studenti as $key => $studente) {
                $ord_arr['cognome'][$key] = $studente['cognome'];
                $ord_arr['nome'][$key] = $studente['nome'];
            }
            array_multisort($ord_arr['cognome'], SORT_ASC, $ord_arr['nome'], SORT_ASC, $elenco_studenti);
        }

        $template->assign('elenco_studenti', str_replace("'", "\'", json_encode($elenco_studenti)));
        $template->assign('elenco_indirizzi', $elenco_indirizzi);
        $template->assign('elenco_giorni', $elenco_giorni);
        $template->assign('elenco_fasce', $elenco_fasce);
        $template->assign('elenco_colloqui', $elenco_colloqui);
        $template->assign('search', $search);
        $template->assign('cerca_dal_data', $cerca_dal_data);
        $template->assign('cerca_dal_ora', $cerca_dal_ora);
        $template->assign('cerca_al_data', $cerca_al_data);
        $template->assign('cerca_al_ora', $cerca_al_ora);
        $template->assign('solo_con_colloqui', $solo_con_colloqui);
        $template->assign("db_key", $db_key);
        $template->assign("tipo_utente", 'amministratore');
        //}}} </editor-fold>
        break;
    case 'adozione_libri':
        $template->assign('db_key', $db_key);
        $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
        $raggruppa_classi_adozioni = estrai_parametri_singoli('RAGGRUPPA_CLASSI_ADOZIONI');
        $template->assign('raggruppa_classi_adozioni', $raggruppa_classi_adozioni);
        $tipo_raggruppamento = ($raggruppa_classi_adozioni == 'SI') ? "RAGGRUPPA_ANNO" : "ESPLODI_SEZIONI";
        $parametri_estrazione_classi = ['tipo_raggruppamento' => $tipo_raggruppamento, 'anno_db' => $db_key];

        $host_aie = 'https://serveraie-mastercom.registroelettronico.com';
        $parametri_login_aie = [
            'username'  =>  'utente_aie',
            'password'  =>  'fV$?2TbxZq'
        ];

        if (!isset($stato_terziario)){
            $stato_terziario = 'selezione_classi';
        }

        switch ($operazione) {
            case 'inserisci_adozione':
                if ($verifica_isbn == 'SI' || $tipo_adozione == 'materiale_docente' || $tipo_adozione == 'materiale_docente_specifico') {
                    switch ($tipo_adozione) {
                        case 'materiale_docente':
                            $adozione['tipo_adozione'] = 'MATERIALE_DOCENTE';
                            $adozione['stato_adozione'] = 'PROPOSTA';                   //??
                            $adozione['approvazione_segreteria'] = 'NO';                //??
                            $adozione['approvazione_responsabile_didattica'] = 'NO';    //??

                            $parametri_inserimento = [];
                            $parametri_inserimento['adozione'] = [
                                'tipo_raggruppamento'                   =>  $tipo_raggruppamento,
                                'id_raggruppamento'                     =>  $id_raggruppamento,
                                'id_classe'                             =>  $id_classe,
                                'id_indirizzo'                          =>  $id_indirizzo,
                                'id_materia_mastercom'                  =>  $id_materia,
                                'tipo_adozione'                         =>  $adozione['tipo_adozione'],
                                'stato_adozione'                        =>  $adozione['stato_adozione'],
                                'approvazione_segreteria'               =>  $adozione['approvazione_segreteria'],
                                'approvazione_responsabile_didattica'   =>  $adozione['approvazione_responsabile_didattica'],
                                'dati_json'                             =>  json_encode($dati_libro, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE)
                            ];
                            break;
                        case 'materiale_docente_specifico':
                            $adozione['tipo_adozione'] = 'MATERIALE_DOCENTE_SPECIFICO';
                            $adozione['stato_adozione'] = 'PROPOSTA';                   //??
                            $adozione['approvazione_segreteria'] = 'NO';                //??
                            $adozione['approvazione_responsabile_didattica'] = 'NO';    //??

                            $parametri_inserimento = [];
                            $parametri_inserimento['adozione'] = [
                                'tipo_raggruppamento'                   =>  $tipo_raggruppamento,
                                'id_raggruppamento'                     =>  $id_raggruppamento,
                                'id_classe'                             =>  $id_classe,
                                'id_indirizzo'                          =>  $id_indirizzo,
                                'id_materia_mastercom'                  =>  $id_materia,
                                'tipo_adozione'                         =>  $adozione['tipo_adozione'],
                                'stato_adozione'                        =>  $adozione['stato_adozione'],
                                'approvazione_segreteria'               =>  $adozione['approvazione_segreteria'],
                                'approvazione_responsabile_didattica'   =>  $adozione['approvazione_responsabile_didattica'],
                                'dati_json'                             =>  json_encode($dati_libro, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE)
                            ];

                            $inserisci = false;

                            if (isset($adozione['titolo']) && trim($adozione['titolo']) != ''){
                                $parametri_inserimento['adozione']['titolo'] = $adozione['titolo'];
                                $inserisci = true;
                            }
                            if (isset($adozione['autori']) && trim($adozione['autori']) != ''){
                                $parametri_inserimento['adozione']['autori'] = $adozione['autori'];
                                $inserisci = true;
                            }
                            if (isset($adozione['progressivo_volume']) && trim($adozione['progressivo_volume']) != '' && is_numeric($adozione['progressivo_volume'])){
                                $parametri_inserimento['adozione']['progressivo_volume'] = (int)$adozione['progressivo_volume'];
                                $inserisci = true;
                            }
                            if (isset($adozione['prezzo']) && is_numeric($adozione['prezzo']) && (float)$adozione['prezzo'] > 0){
                                $parametri_inserimento['adozione']['prezzo'] = round((float)$adozione['prezzo'], 2);
                                $inserisci = true;
                            }

                            $parametri_inserimento['adozione']['descrizione_editore'] = $adozione['descrizione_editore'];
                            $parametri_inserimento['adozione']['in_possesso'] = $adozione['in_possesso'];

                            if (!$inserisci){
                                unset($parametri_inserimento['adozione']);
                            }
                            break;
                        case 'libro_anno_precedente':
                        case 'nuova_adozione':
                        default:
                            if ($tipo_adozione == 'libro_anno_precedente') {
                                $adozione['tipo_adozione'] = 'ANNO_PRECEDENTE';
                                $adozione['stato_adozione'] = 'PROPOSTA';
                                $adozione['approvazione_segreteria'] = 'NO';
                                $adozione['approvazione_responsabile_didattica'] = 'NO';
                            } else {
                                $adozione['tipo_adozione'] = 'NUOVA';
                                $adozione['stato_adozione'] = 'PROPOSTA';
                                $adozione['approvazione_segreteria'] = 'NO';
                                $adozione['approvazione_responsabile_didattica'] = 'NO';
                            }

                            //login aie se necessario
                            if (!isset($token_aie) || !strlen($token_aie) > 0) {
                                $token_aie = chiamata_api($host_aie . "/next-api/v1/login", $parametri_login_aie, 'POST');
                            }

                            if ($adozione_extra == 'SI'){
                                $dati_libro = [
                                    'codice_prodotto'   =>  null,
                                    'editore_id'                            =>  $adozione['editore_id'],
                                    'descrizione_editore'                   =>  $adozione['editore_descrizione'],
                                    'sottotitolo'                           =>  "",
                                    'materia_id'                            =>  $adozione['materia_id_aie'],
                                    'descrizione_materia'                   =>  $adozione['descrizione_materia_aie'],
                                    'materia_alternativa_id'                =>  null,
                                    'descrizione_materia_alternativa'       =>  "",
                                    'numero_volumi'                         =>  'DEFAULT',
                                    'progressivo_volume'                    =>  $adozione['volume'],
                                    'anno_edizione'                         =>  $adozione['anno'],
                                    'prezzo'                                =>  $adozione['prezzo']*100, //perche' sotto viene poi diviso (in aie sono senza virgola)
                                    'modalita_tipo_id'                      =>  'DEFAULT',
                                    'tipo_scuola_id'                        =>  ""
                                ];
                            } else {
                                //estrazione dati libro aie
                                $parametri_estrazione_libro['opere']['codice'] = $adozione['isbn'];
                                $dati_libro_json = chiamata_api($host_aie . "/next-api/v1/adozioni_libri/liste/opere", $parametri_estrazione_libro, 'POST', '', $token_aie);
                                $dati_libro = array_values(json_decode($dati_libro_json, true))[0];
                            }

                            if (!empty($dati_libro)) {
                                $parametri_inserimento = [];
                                $parametri_inserimento['adozione'] = [
                                    'tipo_raggruppamento'                   =>  $tipo_raggruppamento,
                                    'id_raggruppamento'                     =>  $id_raggruppamento,
                                    'id_classe'                             =>  $id_classe,
                                    'id_indirizzo'                          =>  $id_indirizzo,
                                    'id_materia_mastercom'                  =>  $adozione['id_materia'],
                                    'isbn'                                  =>  $adozione['isbn'],
                                    'codice_prodotto'                       =>  $dati_libro['codice_prodotto'],
                                    'autori'                                =>  $adozione['autori'],
                                    'editore_id'                            =>  $dati_libro['editore_id'],
                                    'descrizione_editore'                   =>  $dati_libro['descrizione_editore'],
                                    'titolo'                                =>  $adozione['titolo'],
                                    'sottotitolo'                           =>  $dati_libro['sottotitolo'],
                                    'materia_id_aie'                        =>  $dati_libro['materia_id'],
                                    'descrizione_materia_aie'               =>  $dati_libro['descrizione_materia'],
                                    'materia_alternativa_id_aie'            =>  $dati_libro['materia_alternativa_id'],
                                    'descrizione_materia_alternativa_aie'   =>  $dati_libro['descrizione_materia_alternativa'],
                                    'numero_volumi'                         =>  $dati_libro['numero_volumi'],
                                    'progressivo_volume'                    =>  $dati_libro['progressivo_volume'],
                                    'anno_edizione'                         =>  $dati_libro['anno_edizione'],
                                    'prezzo'                                =>  $dati_libro['prezzo'] / 100,
                                    'modalita_tipo_id'                      =>  $dati_libro['modalita_tipo_id'],
                                    'tipo_scuola_id'                        =>  $dati_libro['tipo_scuola_id'],
                                    'da_acquistare'                         =>  $adozione['da_acquistare'],
                                    'consigliato'                           =>  $adozione['consigliato'],
                                    'nuova_adozione'                        =>  $adozione['nuova_adozione'],
                                    'in_possesso'                           =>  $adozione['in_possesso'],
                                    'anno_prima_adozione'                   =>  $adozione['anno_prima_adozione'],
                                    'tipo_libro'                            =>  $adozione['tipo_libro'],
                                    'sito_acquisto'                         =>  $adozione['sito_acquisto'],
                                    'app_lettura'                           =>  $adozione['app_lettura'],
                                    'disponibile_acquisto_sito'             =>  $adozione['disponibile_acquisto_sito'],
                                    'dichiarazione_editore'                 =>  '',
                                    'versione_accessibile'                  =>  $adozione['versione_accessibile'],
                                    'tipo_versione_accessibile'             => ($adozione['versione_accessibile'] == 'SI') ? $adozione['tipo_versione_accessibile'] : '',
                                    'tipo_adozione'                         =>  $adozione['tipo_adozione'],
                                    'stato_adozione'                        =>  $adozione['stato_adozione'],
                                    'approvazione_segreteria'               =>  $adozione['approvazione_segreteria'],
                                    'approvazione_responsabile_didattica'   =>  $adozione['approvazione_responsabile_didattica'],
                                    'dati_json'                             =>  json_encode($dati_libro, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE),
                                    'adozione_extra'                        =>  ($adozione_extra == 'SI') ? 'SI' : 'NO'
                                ];
                            }
                            break;
                    }

                    if (isset($parametri_inserimento['adozione'])) {
                        $parametri_inserimento['anno_db'] = $db_key;
                        $id_adozione_aie = nextapi_call('adozioni_libri/inserisci_adozione', 'PUT', $parametri_inserimento, $current_key);

                        if ($id_adozione_aie > 0) {
                            $messaggio = print_label('Adozione inserita correttamente');
                        } else {
                            $messaggio = print_label('Errore inserimento');
                        }
                    } else {
                        $messaggio = print_label('Errore inserimento, nessun libro trovato con questo ISBN');
                        $stato_terziario = 'selezione_materia';
                    }

                    $stato_terziario = 'selezione_materia';
                    unset($tipo_adozione);
                } else {
                    $messaggio = print_label("Errore. Prima verificare l'ISBN");
                }
                break;
            case 'modifica_adozione_v2':
            case 'inserisci_adozione_v2':
                if ($verifica_isbn == 'SI' || $tipo_adozione == 'materiale_docente' || $tipo_adozione == 'materiale_docente_specifico') {
                    switch ($tipo_adozione) {
                        case 'materiale_docente':
                            $adozione['tipo_adozione'] = 'MATERIALE_DOCENTE';
                            $adozione['stato_adozione'] = 'PROPOSTA';                   //??
                            $adozione['approvazione_segreteria'] = 'NO';                //??
                            $adozione['approvazione_responsabile_didattica'] = 'NO';    //??

                            $parametri_inserimento = [];
                            $parametri_inserimento['adozione'] = [
                                'tipo_raggruppamento'                   =>  $tipo_raggruppamento,
                                'id_raggruppamento'                     =>  $id_raggruppamento,
                                'id_classe'                             =>  $id_classe,
                                'id_indirizzo'                          =>  $id_indirizzo,
                                'id_materia_mastercom'                  =>  $id_materia,
                                'tipo_adozione'                         =>  $adozione['tipo_adozione'],
                                'stato_adozione'                        =>  $adozione['stato_adozione'],
                                'approvazione_segreteria'               =>  $adozione['approvazione_segreteria'],
                                'approvazione_responsabile_didattica'   =>  $adozione['approvazione_responsabile_didattica'],
                                'dati_json'                             =>  json_encode($dati_libro, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE)
                            ];
                            break;
                        case 'materiale_docente_specifico':
                            $adozione['tipo_adozione'] = 'MATERIALE_DOCENTE_SPECIFICO';
                            $adozione['stato_adozione'] = 'PROPOSTA';                   //??
                            $adozione['approvazione_segreteria'] = 'NO';                //??
                            $adozione['approvazione_responsabile_didattica'] = 'NO';    //??

                            $parametri_inserimento = [];
                            $parametri_inserimento['adozione'] = [
                                'tipo_raggruppamento'                   =>  $tipo_raggruppamento,
                                'id_raggruppamento'                     =>  $id_raggruppamento,
                                'id_classe'                             =>  $id_classe,
                                'id_indirizzo'                          =>  $id_indirizzo,
                                'id_materia_mastercom'                  =>  $id_materia,
                                'tipo_adozione'                         =>  $adozione['tipo_adozione'],
                                'stato_adozione'                        =>  $adozione['stato_adozione'],
                                'approvazione_segreteria'               =>  $adozione['approvazione_segreteria'],
                                'approvazione_responsabile_didattica'   =>  $adozione['approvazione_responsabile_didattica'],
                                'dati_json'                             =>  json_encode($dati_libro, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE)
                            ];

                            $inserisci = false;

                            if (isset($adozione['titolo']) && trim($adozione['titolo']) != '') {
                                $parametri_inserimento['adozione']['titolo'] = $adozione['titolo'];
                                $inserisci = true;
                            }
                            if (isset($adozione['autori']) && trim($adozione['autori']) != '') {
                                $parametri_inserimento['adozione']['autori'] = $adozione['autori'];
                                $inserisci = true;
                            }
                            if (isset($adozione['progressivo_volume']) && trim($adozione['progressivo_volume']) != '' && is_numeric($adozione['progressivo_volume'])) {
                                $parametri_inserimento['adozione']['progressivo_volume'] = (int)$adozione['progressivo_volume'];
                                $inserisci = true;
                            }
                            if (isset($adozione['prezzo']) && is_numeric($adozione['prezzo']) && (float)$adozione['prezzo'] > 0) {
                                $parametri_inserimento['adozione']['prezzo'] = round((float)$adozione['prezzo'], 2);
                                $inserisci = true;
                            }

                            $parametri_inserimento['adozione']['descrizione_editore'] = $adozione['descrizione_editore'];
                            $parametri_inserimento['adozione']['in_possesso'] = $adozione['in_possesso'];

                            if (!$inserisci) {
                                unset($parametri_inserimento['adozione']);
                            }
                            break;
                        case 'adozione':
                        default:
                            $adozione['stato_adozione'] = 'PROPOSTA';
                            $adozione['approvazione_segreteria'] = 'NO';
                            $adozione['approvazione_responsabile_didattica'] = 'NO';

                            //login aie se necessario
                            if (!isset($token_aie) || !strlen($token_aie) > 0) {
                                $token_aie = chiamata_api($host_aie . "/next-api/v1/login", $parametri_login_aie, 'POST');
                            }

                            if ($adozione_extra == 'SI') {
                                $dati_libro = [
                                    'codice_prodotto'   =>  null,
                                    'editore_id'                            =>  $adozione['editore_id'],
                                    'descrizione_editore'                   =>  $adozione['editore_descrizione'],
                                    'sottotitolo'                           =>  "",
                                    'materia_id'                            =>  $adozione['materia_id_aie'],
                                    'descrizione_materia'                   =>  $adozione['descrizione_materia_aie'],
                                    'materia_alternativa_id'                =>  null,
                                    'descrizione_materia_alternativa'       =>  "",
                                    'numero_volumi'                         =>  'DEFAULT',
                                    'progressivo_volume'                    =>  $adozione['volume'],
                                    'anno_edizione'                         =>  $adozione['anno'],
                                    'prezzo'                                =>  $adozione['prezzo'] * 100, //perche' sotto viene poi diviso (in aie sono senza virgola)
                                    'modalita_tipo_id'                      =>  'DEFAULT',
                                    'tipo_scuola_id'                        =>  ""
                                ];
                            } else {
                                //estrazione dati libro aie
                                $parametri_estrazione_libro['opere']['codice'] = $adozione['isbn'];
                                $dati_libro_json = chiamata_api($host_aie . "/next-api/v1/adozioni_libri/liste/opere", $parametri_estrazione_libro, 'POST', '', $token_aie);
                                $dati_libro = array_values(json_decode($dati_libro_json, true))[0];
                            }

                            if (!empty($dati_libro)) {
                                $parametri_inserimento = [];
                                $parametri_inserimento['adozione'] = [
                                    'tipo_raggruppamento'                   =>  $tipo_raggruppamento,
                                    'id_raggruppamento'                     =>  $id_raggruppamento,
                                    'id_classe'                             =>  $id_classe,
                                    'id_indirizzo'                          =>  $id_indirizzo,
                                    'id_materia_mastercom'                  =>  $adozione['id_materia'],
                                    'isbn'                                  =>  $adozione['isbn'],
                                    'codice_prodotto'                       =>  $dati_libro['codice_prodotto'],
                                    'autori'                                =>  $adozione['autori'],
                                    'editore_id'                            =>  $dati_libro['editore_id'],
                                    'descrizione_editore'                   =>  $dati_libro['descrizione_editore'],
                                    'titolo'                                =>  $adozione['titolo'],
                                    'sottotitolo'                           =>  $dati_libro['sottotitolo'],
                                    'materia_id_aie'                        =>  $dati_libro['materia_id'],
                                    'descrizione_materia_aie'               =>  $dati_libro['descrizione_materia'],
                                    'materia_alternativa_id_aie'            =>  $dati_libro['materia_alternativa_id'],
                                    'descrizione_materia_alternativa_aie'   =>  $dati_libro['descrizione_materia_alternativa'],
                                    'numero_volumi'                         =>  $dati_libro['numero_volumi'],
                                    'progressivo_volume'                    =>  $dati_libro['progressivo_volume'],
                                    'anno_edizione'                         =>  $dati_libro['anno_edizione'],
                                    'prezzo'                                =>  $dati_libro['prezzo'] / 100,
                                    'modalita_tipo_id'                      =>  $dati_libro['modalita_tipo_id'],
                                    'tipo_scuola_id'                        =>  $dati_libro['tipo_scuola_id'],
                                    'da_acquistare'                         =>  $adozione['da_acquistare'],
                                    'consigliato'                           =>  trim($adozione['consigliato']),
                                    'nuova_adozione'                        =>  $adozione['nuova_adozione'],
                                    'in_possesso'                           =>  $adozione['in_possesso'],
                                    'anno_prima_adozione'                   =>  $adozione['anno_prima_adozione'],
                                    'tipo_libro'                            =>  $adozione['tipo_libro'],
                                    'sito_acquisto'                         =>  $adozione['sito_acquisto'],
                                    'app_lettura'                           =>  $adozione['app_lettura'],
                                    'disponibile_acquisto_sito'             =>  $adozione['disponibile_acquisto_sito'],
                                    'dichiarazione_editore'                 =>  '',
                                    'versione_accessibile'                  =>  $adozione['versione_accessibile'],
                                    'tipo_versione_accessibile'             => ($adozione['versione_accessibile'] == 'SI') ? $adozione['tipo_versione_accessibile'] : '',
                                    'tipo_adozione'                         =>  $adozione['tipo_adozione'],
                                    'stato_adozione'                        =>  $adozione['stato_adozione'],
                                    'approvazione_segreteria'               =>  $adozione['approvazione_segreteria'],
                                    'approvazione_responsabile_didattica'   =>  $adozione['approvazione_responsabile_didattica'],
                                    'dati_json'                             =>  json_encode($dati_libro, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE),
                                    'adozione_extra'                        => ($adozione_extra == 'SI') ? 'SI' : 'NO'
                                ];
                            }
                            break;
                    }

                    if (isset($parametri_inserimento['adozione'])) {
                        $parametri_inserimento['anno_db'] = $db_key;
                        if ($operazione == 'modifica_adozione_v2'){
                            $parametri_inserimento['adozione']['id_adozione_aie'] = $id_adozione;
                            $id_adozione_aie = nextapi_call('adozioni_libri/modifica_adozione', 'PUT', $parametri_inserimento, $current_key);
                            if ($id_adozione_aie > 0) {
                                $messaggio = print_label('Modifica avvenuta correttamente');
                            } else {
                                $messaggio = print_label('Errore modifica');
                            }
                        } else {
                            $id_adozione_aie = nextapi_call('adozioni_libri/inserisci_adozione', 'PUT', $parametri_inserimento, $current_key);
                            if ($id_adozione_aie > 0) {
                                $messaggio = print_label('Adozione inserita correttamente');
                            } else {
                                $messaggio = print_label('Errore inserimento');
                            }
                        }

                    } else {
                        $messaggio = print_label('Errore inserimento, nessun libro trovato con questo ISBN');
                        $stato_terziario = 'selezione_materia';
                    }

                    $stato_terziario = 'selezione_materia';
                    unset($tipo_adozione);
                } else {
                    $messaggio = print_label("Errore. Prima verificare l'ISBN");
                }
                break;
            case 'elimina_adozione':
                $elimina = nextapi_call('adozioni_libri/' . $id_adozione, 'DELETE', ['anno_db' => $db_key], $current_key);
                $messaggio = ($elimina) ? print_label("Eliminazione avvenuta con successo") : print_label("Errore eliminazione");
                break;
            case 'elimina_approvazioni':
                $elimina = nextapi_call('adozioni_libri/elimina_approvazioni/tutte', 'POST', ['anno_db' => $db_key], $current_key);
                $messaggio = ($elimina) ? print_label("Eliminazione avvenuta con successo") : print_label("Errore eliminazione");
                break;
            case 'elimina_adozioni_totali':
                $elimina = nextapi_call('adozioni_libri/elimina_adozioni/tutte', 'POST', ['anno_db' => $db_key], $current_key);
                $messaggio = ($elimina) ? print_label("Eliminazione avvenuta con successo") : print_label("Errore eliminazione");
                break;
            case 'salva_modifica_adozione':
                $parametri_modifica = [];

                if (isset($modifica_adozione['nuova_adozione'])){
                    $parametri_modifica['adozione'] = [
                        'id_adozione_aie'           =>  $id_adozione,
                        'da_acquistare'             =>  $modifica_adozione['da_acquistare'],
                        'consigliato'               =>  $modifica_adozione['consigliato'],
                        'nuova_adozione'            =>  $modifica_adozione['nuova_adozione'],
                        'in_possesso'               =>  $modifica_adozione['in_possesso'],
                        'anno_prima_adozione'       =>  $modifica_adozione['anno_prima_adozione'],
                        'tipo_libro'                =>  $modifica_adozione['tipo_libro'],
                        'sito_acquisto'             =>  $modifica_adozione['sito_acquisto'],
                        'app_lettura'               =>  $modifica_adozione['app_lettura'],
                        'disponibile_acquisto_sito' =>  $modifica_adozione['disponibile_acquisto_sito'],
                        'versione_accessibile'      =>  $modifica_adozione['versione_accessibile'],
                        'tipo_versione_accessibile' => ($modifica_adozione['versione_accessibile'] == 'SI') ? $modifica_adozione['tipo_versione_accessibile'] : ''
                    ];
                } elseif (isset($modifica_adozione['titolo'])){
                    $parametri_modifica['adozione'] = [
                        'id_adozione_aie'       =>  $id_adozione,
                        'titolo'                =>  $modifica_adozione['titolo'],
                        'autori'                =>  $modifica_adozione['autori'],
                        'progressivo_volume'    =>  (int)$modifica_adozione['progressivo_volume'],
                        'prezzo'                =>  round((float) $modifica_adozione['prezzo'], 2),
                        'descrizione_editore'   =>  $modifica_adozione['descrizione_editore'],
                        'in_possesso'           =>  $modifica_adozione['in_possesso']
                    ];
                }

                $parametri_modifica['anno_db'] = $db_key;

                $id_modifica = nextapi_call('adozioni_libri/modifica_adozione', 'PUT', $parametri_modifica, $current_key);

                $messaggio = ($id_modifica == $id_adozione) ? print_label("Modifica avvenuta con successo") : print_label("Errore modifica");
                break;
            case 'salva_approvazioni':
                if (count($approvazioni) > 0){
                    $tipo_utente_aie = estrai_utente($current_user)['tipo_utente_aie'];

                    foreach ($approvazioni as $id_adozione_aie => $approvazione){
                        if ($approvazione['modificato'] == 'SI'){
                            $parametri_modifica = [];

                            if ($tipo_utente_aie == 'SEGRETERIA' || $tipo_utente_aie == 'RESPONSABILE_UNICO' || $superutente_int == 'SI'){
                                $app_segreteria =  ($approvazione['approvazione_segreteria'] == 'SI') ? 'SI' : 'NO';
                                $parametri_modifica['adozione']['id_adozione_aie'] = $id_adozione_aie;
                                $parametri_modifica['adozione']['approvazione_segreteria'] = $app_segreteria;
                            }

                            if ($tipo_utente_aie == 'RESPONSABILE_DIDATTICA' || $tipo_utente_aie == 'RESPONSABILE_UNICO' || $superutente_int == 'SI'){
                                $app_responsabile_didattica =  ($approvazione['approvazione_responsabile_didattica'] == 'SI') ? 'SI' : 'NO';
                                $parametri_modifica['adozione']['id_adozione_aie'] = $id_adozione_aie;
                                $parametri_modifica['adozione']['approvazione_responsabile_didattica'] = $app_responsabile_didattica;
                            }

                            if (!empty($parametri_modifica)){
                                $parametri_modifica['anno_db'] = $db_key;
                                $id_modifica = nextapi_call('adozioni_libri/modifica_adozione', 'PUT', $parametri_modifica, $current_key);
                                $update_stato_adozione = nextapi_call('adozioni_libri/'. $id_adozione_aie.'/update_stato', 'POST', ['anno_db' => $db_key], $current_key);
                            }
                        }
                    }
                }
                break;
            case 'aggiorna_prezzi_testi':
                $id_modifica = nextapi_call('adozioni_libri/aggiorna_prezzi_adozioni', 'PUT', [], $current_key);
                $messaggio = print_label('Prezzi aggiornati');
                break;
            case 'stampa':
                switch ($stampa['tipo_stampa']) {
                    case 'stampa_adozione_libri':
                        $id_raggruppamento = $stampa['id_raggruppamento'];

                        // scuola
                        $nome_scuola = estrai_parametri_singoli('NOME_SCUOLA');

                        switch ($nome_scuola)
                        {
                            case 'OSDB Sesto S. Giovanni (MI)':
                                require_once "stampe/stampe_personalizzate/osdb_stampa_adozione_libri.php";
                                break;

                            default:
                                require_once "stampe/stampa_adozione_libri.php";
                                break;
                        }

                        if ($stampa['tipo_file_esportato'] == 'xls')
                        {
                            //cancello tutte i file temporanei fatti da più di un'ora
                            $dir = 'tmp_xls';

                            CleanFiles($dir);
                            //creo i nuovi file temporanei
                            $file=basename(tempnam($dir, 'tmp'));
                            rename($dir . '/' . $file, $dir . '/' . 'adozioni_libri' . '_' . date('Y-m-d_H-i_s') . '.xls');
                            $file = 'adozioni_libri' . '_' . date('Y-m-d_H-i_s') . '.xls';
                            //Salva il file xls come file
                            $nuovo_nome = $dir . '/' . $file;
                            $handle = fopen($nuovo_nome, 'w');
                            fwrite($handle,$testo_excel);
                            fclose($handle);

                            //Reindirizzamento JavaScript
                            echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
                        }
                        else {
                            $pdf->Output('adozioni_libri_' . str_replace(' ', '_', $classe) . '_' . str_replace(' ', '_', $indirizzo) . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
                        }
                        exit;
                        break;
                    case 'stampa_massiva':
                        $parametri_estrazione_classi['includi_adozioni'] = 'SI';
                        $elenco_classi = nextapi_call('adozioni_libri/liste/elenco_classi_adozioni', 'GET', $parametri_estrazione_classi, $current_key);

                        // scuola
                        $nome_scuola = estrai_parametri_singoli('NOME_SCUOLA');

                        if ($stampa['tipo_file_esportato'] == 'xls') {
                            $rel_dir = 'tmp_xls/';
                        } else {
                            $rel_dir = 'tmp_pdf/';
                        }
                        // creo la cartella temporanea
                        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
                        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
                        $dir = $temp_dir . '/';

                        exec('mkdir ' . $temp_dir);

                        //cancello tutte i file temporanei fatti da più di 1 ora
                        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');

                        $nome = 'exportAIE' . date('YmdHi') . '.zip';

                        foreach ($elenco_classi as $classe_tmp){
                            $id_raggruppamento = $classe_tmp['id'];

                            switch ($nome_scuola) {
                                case 'OSDB Sesto S. Giovanni (MI)':
                                    include "stampe/stampe_personalizzate/osdb_stampa_adozione_libri.php";
                                    break;

                                default:
                                    include "stampe/stampa_adozione_libri.php";
                                    break;
                            }

                            $nome_file = 'adozioni_libri_' . str_replace(' ', '_', $classe_tmp['classe']);
                            if ($classe_tmp['tipo_raggruppamento'] == 'ESPLODI_SEZIONI'){
                                $nome_file .= str_replace(' ', '_', $classe_tmp['sezione']);
                            }
                            $nome_file .= '_' . str_replace(' ', '_', $classe_tmp['indirizzo']) . '_' . date('Y-m-d_H-i');

                            if (!empty($raggruppamento_aie)){ // $raggruppamento_aie arriva dal file di stampa stampa_adozione_libri.php
                                if ($stampa['tipo_file_esportato'] == 'xls') {
                                    $handle = fopen($dir . $nome_file . '.xls', 'w');
                                    fwrite($handle, $testo_excel);
                                    fclose($handle);
                                } else {
                                    $pdf->Output($dir . $nome_file .'.pdf', 'F');
                                }
                            }
                        }

                        if ($stampa['tipo_file_esportato'] == 'xls') {
                            exec('zip -j ' . $base_dir . $nome . ' ' . $dir . 'adozioni_libri_*.xls');
                        } else {
                            exec('zip -j ' . $base_dir . $nome . ' ' . $dir . 'adozioni_libri_*.pdf');
                        }
                        exec('rm -fr ' . $dir);
                        //Reindirizzamento JavaScript
                        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
                        exit;
                        break;
                    case 'stampa_manuale':

                        exit;
                        break;
                    default:
                        break;
                }
                break;
            case 'esporta':
                // verifico che non ci siano adozioni con problemi
                $parametri_estrazione_classi['includi_adozioni'] = 'SI';
                $elenco_classi = nextapi_call('adozioni_libri/liste/elenco_classi_adozioni', 'GET', $parametri_estrazione_classi, $current_key);
                $problemi = false;
                $elenco_classi_problemi = [];

                foreach ($elenco_classi as $classe){
                    if (count($classe['materie']) > 0){
                        foreach ($classe['materie'] as $materia){
                            if (count($materia['adozioni']) < 0){
                                foreach ($materia['adozioni'] as $adozione){
                                    if ($adozione['nuova_adozione'] == 'SI' && $adozione['anno_prima_adozione'] < explode('/', $anno_scolastico)[1]) {
                                        // errore
                                        $problemi = true;
                                        $elenco_classi_problemi[$classe['id']] = $classe['classe'] . $classe['sezione'] . ' ' . $classe['indirizzo'];
                                    }
                                }
                            }
                        }
                    }
                }

                if ($problemi){
                    $messaggio_errore = print_label("ERRORE!\nAlcune adozioni, prima dell'invio ad AIE, necessitano di una modifica. Controllare le righe evidenziate nelle seguenti classi:\n");
                    $messaggio_errore .= implode("\n", $elenco_classi_problemi);
                    $template->assign('messaggio_errore', $messaggio_errore);
                    break;
                }

                // se tutto ok procedo
                $scuole = estrai_scuole();
                $meccanografici = array_unique(array_column($scuole, 'codice_meccanografico_secondario'));

                $token_aie = chiamata_api($host_aie . "/next-api/v1/login", $parametri_login_aie, 'POST');
                $parametri['dati'] = $meccanografici;
                $parametri['anno_db'] = $db_key;
                $info_classi_sedi_aie_json = chiamata_api($host_aie . "/next-api/v1/adozioni_libri/liste/info_classi_sedi", $parametri, 'POST', '', $token_aie);
                $info_classi_sedi_aie = json_decode($info_classi_sedi_aie_json, true);

                if (!empty($info_classi_sedi_aie)){
                    $parametri['dati'] = $info_classi_sedi_aie;
                    $nome_file_completo = nextapi_call('adozioni_libri/liste/file_adozioni_definitive', 'POST', $parametri, $current_key);
                    $nome_file = explode('www-source', $nome_file_completo)[1];
                    echo "<HTML><SCRIPT>window.open('".$nome_file."', '_blank');</SCRIPT></HTML>";
                } else {
                    $messaggio = print_label('ERRORE! Nessun codice meccanografico trovato su AIE');
                }
                break;
            case 'salva_studenti_previsti':
                $update_studenti_previsti = nextapi_call('adozioni_libri/studenti_previsti_classi_succ', 'PUT', ['studenti_previsti' => $studenti_previsti, 'anno_db' => $db_key], $current_key);
                $messaggio = print_label("Aggiornamento terminato");
                break;
            default:
                break;
        }

        switch ($stato_terziario) {
            case 'selezione_classi':
                $parametri_estrazione_classi['includi_adozioni'] = 'SI';
                $elenco_classi = nextapi_call('adozioni_libri/liste/elenco_classi_adozioni', 'GET', $parametri_estrazione_classi, $current_key);

                foreach ($elenco_classi as $key => $classe){
                    $cont_adozioni = $cont_confermate = 0;
                    foreach ($classe['materie'] as $id_materia => $materia){
                        if (isset($materia['adozioni'])){
                            foreach ($materia['adozioni'] as $adozione){
                                $cont_adozioni++;
                                if ($adozione['approvazione_segreteria'] == 'SI' && $adozione['approvazione_responsabile_didattica'] == 'SI'){
                                    $cont_confermate++;
                                }
                            }
                        }
                    }
                    $elenco_classi[$key]['riepilogo_adozioni_confermate'] = $cont_confermate . "/" . $cont_adozioni;
                }

                $template->assign('elenco_classi', $elenco_classi);

                if (in_array('NO', array_column($elenco_classi, 'abbinato_sede'))){
                    $template->assign('blocca_esportazione', 'SI');
                    $template->assign('messaggio_esportazione', 'Popolare prima tutte le sedi degli indirizzi in Setup B01');
                } else {
                    $template->assign('blocca_esportazione', 'NO');
                }
                break;
            case 'classi_succ_studenti_previsti':
                $classi_succ = nextapi_call('adozioni_libri/liste/classi_succ', 'GET', ['anno_db' => $db_key], $current_key);
                $template->assign('classi_succ', $classi_succ);
                break;
            case 'selezione_materia':
                $tipo_utente_aie = estrai_utente($current_user)['tipo_utente_aie'];
                $elenco_materie = [];
                $messaggio_segnala = 'NO';

                if ($id_raggruppamento != '') {
                    $parametri_estrazione_classi['id_raggruppamento'] = $id_raggruppamento;
                    $elenco_classi = nextapi_call('adozioni_libri/liste/elenco_classi_adozioni', 'GET', $parametri_estrazione_classi, $current_key);
                    $elenco_materie_tmp = $elenco_classi[$id_raggruppamento]['materie'];

                    //divisione materie in tabelle da acquistare e non
                    if (count($elenco_materie_tmp) > 0) {
                        $da_acquistare = $no_acquistare = [];
                        foreach ($elenco_materie_tmp as $materia) {
                            $materia['stato'] = print_label('Da inserire');

                            if (count($materia['adozioni']) > 0) {
                                foreach ($materia['adozioni'] as $adozione) {
                                    if ($adozione['tipo_adozione'] == 'MATERIALE_DOCENTE') {
                                        $adozione['testo_materiale'] = print_label("Il docente comunicherà agli studenti i supporti didattici prescelti nel corso dell'anno scolastico");
                                    }

                                    $tmp = $materia;
                                    unset($tmp['adozioni']);
                                    $tmp['adozione'] = $adozione;

                                    switch (strtoupper($adozione['stato_adozione'])) {
                                        case 'PROPOSTA':
                                            $tmp['stato'] = print_label('Proposto');
                                            break;
                                        case 'CONFERMATA':
                                            $tmp['stato'] = print_label('Confermato');
                                            break;
                                        case 'DA_COMPLETARE':
                                            $tmp['stato'] = print_label('Da completare');
                                            break;
                                        case 'IN_REVISIONE':
                                            $tmp['stato'] = print_label('In revisione');
                                            break;
                                        default:
                                            $tmp['stato'] = print_label('Proposto');
                                            break;
                                    }

                                    $tmp['adozione']['data_inserimento_tradotta'] = date('d/m/Y H:i', $tmp['adozione']['data_inserimento']);
                                    $tmp['adozione']['data_modifica_tradotta'] = date('d/m/Y H:i', $tmp['adozione']['data_modifica']);

                                    if ($adozione['nuova_adozione'] == 'SI' && $adozione['anno_prima_adozione'] < explode('/', $anno_scolastico)[1]){
                                        // Segnalo l'errore
                                        $tmp['segnala'] = 'SI';
                                        $messaggio_segnala = 'SI';
                                    }

                                    if ($adozione['da_acquistare'] == 'SI') {
                                        $da_acquistare[] = $tmp;
                                    } else {
                                        // materiale fornito dal docente messo nella tabella dei 'da non comprare'
                                        $no_acquistare[] = $tmp;
                                    }
                                }
                            } else {
                                $da_acquistare[] = $materia;
                            }

                            unset($materia['adozioni']);
                            $elenco_materie['elenco'][] = $materia;
                        }

                        $elenco_materie['da_acquistare'] = $da_acquistare;
                        $elenco_materie['no_acquistare'] = $no_acquistare;
                    }

                    $descrizione_classe = $elenco_classi[$id_raggruppamento]['classe'] . $elenco_classi[$id_raggruppamento]['sezione'] . ' ' . $elenco_classi[$id_raggruppamento]['indirizzo'];
                    $template->assign('descrizione_classe', $descrizione_classe);
                    $template->assign('aggiunta_libero', $elenco_classi[$id_raggruppamento]['almeno_uno_attivo']);
                }

                $template->assign('tipo_utente_aie', $tipo_utente_aie);
                $template->assign('id_raggruppamento', $id_raggruppamento);
                $template->assign('id_classe', $id_classe);
                $template->assign('id_indirizzo', $id_indirizzo);
                $template->assign('elenco_materie', $elenco_materie);
                $template->assign('messaggio_segnala', $messaggio_segnala);
                break;
            case 'maschera_adozione':
            case 'maschera_adozione_v2':
            case 'maschera_adozione_ridotta_libera':
                $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
                $template->assign('anno_scolastico', explode('/', $anno_scolastico)[1]);

                $template->assign('nome_classe_adozione', $nome_classe_adozione);
                $template->assign('nome_materia_adozione', $nome_materia_adozione);

                //login aie se necessario
                if (!isset($token_aie) || !strlen($token_aie) > 0) {
                    $token_aie = chiamata_api($host_aie . "/next-api/v1/login", $parametri_login_aie, 'POST');
                }
                $template->assign('host_aie', $host_aie);
                $template->assign('token_aie', $token_aie);

                $template->assign('tipo_adozione', $tipo_adozione);
                $template->assign('id_materia', $id_materia);
                $template->assign('id_raggruppamento', $id_raggruppamento);
                $template->assign('id_classe', $id_classe);
                $template->assign('id_indirizzo', $id_indirizzo);
                break;
            case 'maschera_modifica_adozione':
            case 'maschera_modifica_adozione_ridotta_libera':
            case 'maschera_modifica_adozione_v2':
                $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
                $template->assign('anno_scolastico', explode('/', $anno_scolastico)[1]);

                $template->assign('nome_classe_adozione', $nome_classe_adozione);
                $template->assign('nome_materia_adozione', $nome_materia_adozione);

                $adozione = nextapi_call('adozioni_libri/adozione/' . $id_adozione, 'POST', ['anno_db' => $db_key], $current_key);
                $template->assign('adozione', $adozione);

                if ($stato_terziario == "maschera_modifica_adozione_v2"){
                    //login aie se necessario
                    if (!isset($token_aie) || !strlen($token_aie) > 0) {
                        $token_aie = chiamata_api($host_aie . "/next-api/v1/login", $parametri_login_aie, 'POST');
                    }
                    $template->assign('host_aie', $host_aie);
                    $template->assign('token_aie', $token_aie);
                }

                $template->assign('id_adozione', $id_adozione);
                $template->assign('id_raggruppamento', $id_raggruppamento);
                $template->assign('id_classe', $id_classe);
                $template->assign('id_indirizzo', $id_indirizzo);
                break;

            default:
                break;
        }

        $template->assign('tipo_interazione', $tipo_interazione);
        $template->assign('messaggio', $messaggio);
        $template->assign('stato_terziario', $stato_terziario);
        break;
    case 'rappresentati_classe_genitori':
        if (!isset($stato_terziario)){
            $stato_terziario = 'elenco_elezioni';
        }

        switch ($stato_terziario) {
            case 'dettaglio_elezione':
                switch ($operazione){
                    case 'inserisci_classe':
                        $payload = [
                            "titolo"  =>  $nuova_classe_titolo,
                            "testo"  =>  $nuova_classe_testo,
                            "data_inizio"   =>  $nuova_classe_data_inizio,
                            "data_fine"   =>  $nuova_classe_data_fine,
                            "modello"   =>  $nuova_classe_modello,
                            "opzioni"   =>  [
                                "n_preferenze"  =>  $nuova_classe_preferenze,
                                "lista_preferenze"  =>  json_decode($nuova_classe_candidati, true)['candidati']
                            ]
                        ];

                        $result = nextapi_call("polls/".$id_elezione."/class/".$nuova_classe_id_classe, 'PUT', $payload, $current_key);

                        if (!$result['ok']) {
                            $messaggio = print_label($result['errors'][0]);
                        } else {
                            $messaggio = print_label("Inserimento effettuato");
                        }
                        break;
                    case 'elimina_elezione_classe':
                        $result = nextapi_call("polls/" . $id_elezione . "/class/" . $id_classe, 'DELETE', [], $current_key);
                        $messaggio = print_label("Eliminazione effettuata");
                        break;
                    case 'stampa_elezione_classe':
                        $dati_elezione = nextapi_call("polls/" . $id_elezione, 'GET', null, $current_key);
                        $elenco_classi_elezione[$id_classe] = $dati_elezione['classi'][$id_classe];
                        include 'stampe/stampa_elezioni_risultati.php';
                        exit;
                        break;
                    default:
                        break;
                }
                $template->assign('id_elezione', $id_elezione);
                break;
            case 'elenco_elezioni':
            default:
                switch ($operazione){
                    case 'inserisci_elezione':
                        $payload = [
                            "nome"  =>  $nuova_elezione_titolo,
                            "sottotitolo"  =>  $nuova_elezione_sottotitolo,
                            "data_inizio"   =>  $nuova_elezione_data_inizio,
                            "data_fine"   =>  $nuova_elezione_data_fine,
                            "destinatari"   =>  "parenti_classe"
                        ];

                        $result = nextapi_call("polls/", 'PUT', $payload, $current_key);
                        if (!$result['ok']){
                            $messaggio = print_label($result['errors'][0]);
                        } else {
                            $messaggio = print_label("Inserimento effettuato");
                        }
                        break;
                    case 'elimina_elezione':
                        $result = nextapi_call("polls/" . $id_elezione, 'DELETE', [], $current_key);
                        $messaggio = print_label("Eliminazione effettuata");
                        break;
                    case 'stampa_elezione':
                        $dati_elezione = nextapi_call("polls/" . $id_elezione, 'GET', null, $current_key);
                        $elenco_classi_elezione = $dati_elezione['classi'];
                        include 'stampe/stampa_elezioni_risultati.php';
                        exit;
                        break;
                    default:
                        break;
                }
                break;
        }

        $template->assign('messaggio', $messaggio);
        $template->assign('stato_terziario', $stato_terziario);
        break;
    case 'rappresentati_istituto_genitori':
        if (!isset($stato_terziario)){
            $stato_terziario = 'elenco_elezioni';
        }

        switch ($stato_terziario) {
            case 'dettaglio_elezione':
                switch ($operazione){
                    case 'inserisci_multiclasse':
                        $payload = [
                            "titolo"  =>  $multiclasse_titolo,
                            "testo"  =>  $multiclasse_testo,
                            "modello"   =>  $multiclasse_modello,
                            "opzioni"   =>  [
                                "n_preferenze"  =>  $multiclasse_preferenze,
                                "lista_preferenze"  =>  json_decode($multiclasse_candidati, true)['candidati']
                            ],
                            "classi"    =>  array_map('intval', explode(',', $id_classi_selezionate))
                        ];

                        $result = nextapi_call("polls/".$id_elezione."/multiclass", 'PUT', $payload, $current_key);

                        if (!$result['ok']) {
                            $messaggio = print_label($result['errors'][0]);
                        } else {
                            $messaggio = print_label("Inserimento effettuato");
                        }
                        break;
                    case 'stampa_elezione_multiclasse':
                        $dati_elezione = nextapi_call("polls/" . $id_elezione, 'GET', null, $current_key);
                        include 'stampe/stampa_elezioni_risultati_multiclasse.php';
                        exit;
                        break;
                    default:
                        break;
                }
                $template->assign('id_elezione', $id_elezione);
                break;
            case 'elenco_elezioni':
            default:
                switch ($operazione){
                    case 'inserisci_elezione':
                        $payload = [
                            "nome"  =>  $nuova_elezione_titolo,
                            "sottotitolo"  =>  $nuova_elezione_sottotitolo,
                            "data_inizio"   =>  $nuova_elezione_data_inizio,
                            "data_fine"   =>  $nuova_elezione_data_fine,
                            "destinatari"   =>  "parenti_multiclasse"
                        ];

                        $result = nextapi_call("polls/", 'PUT', $payload, $current_key);

                        if (!$result['ok']){
                            $messaggio = print_label($result['errors'][0]);
                        } else {
                            $messaggio = print_label("Inserimento effettuato");
                        }
                        break;
                    case 'elimina_elezione':
                        $result = nextapi_call("polls/" . $id_elezione, 'DELETE', [], $current_key);
                        $messaggio = print_label("Eliminazione effettuata");
                        break;
                    case 'stampa_elezione_multiclasse':
                        $dati_elezione = nextapi_call("polls/" . $id_elezione, 'GET', null, $current_key);
                        include 'stampe/stampa_elezioni_risultati_multiclasse.php';
                        exit;
                        break;
                    default:
                        break;
                }
                break;
        }

        $template->assign('messaggio', $messaggio);
        $template->assign('stato_terziario', $stato_terziario);
        break;
    case 'votazione_generica':
        if (!isset($stato_terziario)){
            $stato_terziario = 'elenco_votazioni';
        }

        switch ($stato_terziario) {
            case 'dettaglio_votazione':
                switch ($operazione){
                    case 'inserisci_dettagli_votazione':
                        $payload = [
                            "titolo"  =>  $votazione_titolo,
                            "testo"  =>  $votazione_testo,
                            "modello"   =>  $votazione_modello,
                            "opzioni"   =>  [
                                "n_preferenze"  =>  $votazione_n_preferenze,
                                "lista_preferenze"  =>  json_decode($votazione_preferenze, true)['preferenze']
                            ],
                            "parenti"    =>  array_map('intval', explode(',', $votazione_id_parenti))
                        ];

                        $result = nextapi_call("polls/".$id_votazione."/multiclass", 'PUT', $payload, $current_key);

                        if (!$result['ok']) {
                            $messaggio = print_label($result['errors'][0]);
                        } else {
                            $messaggio = print_label("Inserimento effettuato");
                        }
                        break;
                    case 'aggiorna_date':
                        $dati_votazione = nextapi_call("polls/" . $id_votazione, 'GET', null, $current_key);
                        $voti = nextapi_call("polls/" . $id_votazione . "/vote/voters", 'GET', null, $current_key);

                        if (empty($voti)){
                            $payload = [
                                "nome"  =>  $dati_votazione['nome'],
                                "sottotitolo"  =>  $dati_votazione['sottotitolo'],
                                "data_inizio"   =>  $votazione_data_inizio_mod,
                                "data_fine"   =>  $votazione_data_fine_mod,
                                "destinatari"   =>  "parenti_free"
                            ];
                            $result = nextapi_call("polls/" . $id_votazione, 'PUT', $payload, $current_key);

                            if (!$result['ok']) {
                                $messaggio = print_label($result['errors'][0]);
                            } else {
                                $poll_active = null;

                                foreach ($dati_votazione['multiclasse']['polls'] as $poll){
                                    if ($poll['status'] == 'active'){
                                        $poll_active = $poll;
                                    }
                                }

                                if (!is_null(($poll_active))){
                                    $id_parenti = [];
                                    foreach ($poll_active['votanti'] as $votante){
                                        $id_parenti[] = $votante['id_parente'];
                                    }

                                    $payload2 = [
                                        "overwrite" => true,
                                        "titolo"  =>  $poll_active['titolo'],
                                        "testo"  =>  $poll_active['testo'],
                                        "modello"   =>  'opzioni_semplici',
                                        "opzioni"   =>  [
                                            "n_preferenze"  =>  $poll_active['opzioni']['n_preferenze'],
                                            "lista_preferenze"  =>  array_column($poll_active['opzioni']['lista_preferenze'], "testo_opzione")
                                        ],
                                        "parenti"    =>  $id_parenti
                                    ];

                                    $result = nextapi_call("polls/" . $id_votazione . "/multiclass", 'PUT', $payload2, $current_key);

                                    if (!$result[0]['ok']) {
                                        $messaggio = print_label($result['errors'][0]);
                                    } else {
                                        $messaggio = print_label("Modifica effettuata");
                                    }
                                } else {
                                    $messaggio = print_label("Modifica effettuata");
                                }
                            }
                        } else {
                            $messaggio = print_label("Impossibile completare l'operazione in quanto sono stati espressi voti");
                        }
                        break;
                    case 'stampa_votazione_generica':
                        $dati_votazione = nextapi_call("polls/" . $id_votazione, 'GET', null, $current_key);
                        include 'stampe/stampa_votazione_generica.php';
                        exit;
                        break;
                    default:
                        break;
                }
                $template->assign('id_votazione', $id_votazione);
                break;
            case 'elenco_votazioni':
            default:
                switch ($operazione){
                    case 'inserisci_votazione':
                        $payload = [
                            "nome"  =>  $nuova_votazione_titolo,
                            "sottotitolo"  =>  $nuova_votazione_sottotitolo,
                            "data_inizio"   =>  $nuova_votazione_data_inizio,
                            "data_fine"   =>  $nuova_votazione_data_fine,
                            "destinatari"   =>  "parenti_free"
                        ];

                        $result = nextapi_call("polls/", 'PUT', $payload, $current_key);

                        if (!$result['ok']){
                            $messaggio = print_label($result['errors'][0]);
                        } else {
                            $messaggio = print_label("Inserimento effettuato");
                        }
                        break;
                    case 'elimina_votazione':
                        $result = nextapi_call("polls/" . $id_votazione, 'DELETE', [], $current_key);
                        $messaggio = print_label("Eliminazione effettuata");
                        break;
                    case 'stampa_votazione_generica':
                        $dati_votazione = nextapi_call("polls/" . $id_votazione, 'GET', null, $current_key);
                        include 'stampe/stampa_votazione_generica.php';
                        exit;
                        break;
                    default:
                        break;
                }
                break;
        }

        $template->assign('messaggio', $messaggio);
        $template->assign('stato_terziario', $stato_terziario);
        break;
    case 'elenchi_studenti':
        $template->assign("db_key", $db_key);
        $template->assign("ins_stud", ($db_key == $db_official) ? '1' : '0');
        $pagamento_non_necessario = false;

        if ($id_servizio_selezionato > 0) {
            $template->assign("id_servizio_selezionato", $id_servizio_selezionato);

            $parametri = [
                "db_richiesto" => $db_key,
                "id" => $id_servizio_selezionato
            ];
            $elemento_marketplace = nextapi_call('marketplace', 'GET', $parametri, $current_key);
            $elemento_marketplace = array_values($elemento_marketplace)[0];

            switch ($operazione) {
                case 'inserisci_studente':
                    $messaggio = "Errore inserimento servizio";
                    if (count($nuovi_studenti) > 0){
                        $invio_mail_amministrazione = false;
                        $elenco_studenti_inseriti = [];
                        foreach ($nuovi_studenti as $id_studente_corrente){
                            //if ($nuovo_studente['id_studente'] > 0) {
                            $payload = [
                                "id_studente"   =>  $id_studente_corrente,
                                "db_richiesto"  =>  $db_key,
                                "id_marketplace"    =>  $id_servizio_selezionato,
                                "validita_inizio"   =>  strtotime($nuovo_studente['inizio_validita'] . " 00:00:00"),
                                "validita_fine"   =>  strtotime($nuovo_studente['fine_validita'] . " 23:59:59"),
                                "contabilizzato"   =>  'SI'
                            ];
                            $acquisto_studente = nextapi_call('marketplace/acquisti/inserisci', 'POST', $payload, $current_key);

                            if ($acquisto_studente['acquisti'][0] > 0){
                                $messaggio = "Servizio inserito.";

                                $dati_studente = estrai_dati_studente($id_studente_corrente);
                                $parenti = estrai_genitori_studente($id_studente_corrente);

                                $oggetto_parenti = str_replace("\n", "<br>", $nuovo_studente['oggetto_messaggio']);
                                $testo_parenti = str_replace("\n", "<br>", $nuovo_studente['testo_messaggio']);

                                $elenco_studenti_inseriti[] = $dati_studente['cognome'] . ' ' . $dati_studente['nome'];

                                if ($nuovo_studente['invia_messaggio'] == 'SI'){
                                    // invio messaggio via Messenger
                                    foreach ($parenti as $parente) {
                                        $payload = [
                                            'oggetto' => $oggetto_parenti,
                                            'corpo' => $testo_parenti,
                                            "id_mittente" => $current_user,
                                            "tipo_mittente" => $auth_object->type,
                                            "destinatari" => [
                                                [
                                                    "id" => $parente['id_parente'],
                                                    "tipo" => 'parente'
                                                ]
                                            ]
                                        ];
                                        $inserimento = nextapi_call('messaggistica/inserisci_messaggio', 'POST', $payload, $current_key);
                                    }
                                }

                                $tipo_invio = estrai_parametri_singoli('TIPO_INVIO_EMAIL');
                                if ($tipo_invio == 'MAILER') {
                                    $oggetto_amministrazione = "ISCRIZIONI al servizio {$elemento_marketplace['descrizione']}";
                                    $testo_amministrazione = "Si conferma l'iscrizione dei seguenti studenti al servizio {$elemento_marketplace['descrizione']}:<br>";
                                    $invio_mail_amministrazione = true;

                                    // INVIO PARENTI
                                    $to = [];
                                    $cc = [];
                                    $bcc = [];
                                    if ($nuovo_studente['invia_messaggio'] == 'SI'){
                                        if ($nuovo_studente['invia_email'] == 'SI' || $nuovo_studente['invia_email'] == 'SI_STESSO_TESTO'){
                                            foreach ($parenti as $parente) {
                                                if (strlen($parente['email']) > 0) {
                                                    $to[$parente['id_parente']] = $parente['email'];
                                                }
                                            }
                                            $to = array_values($to);

                                            if ($nuovo_studente['invia_email'] == 'SI'){
                                                $testo_parenti = "Hai ricevuto un nuovo messaggio, puoi visualizzarlo nella Messaggistica";
                                            }

                                            if (!empty($to)){
                                                $message = [];
                                                $messages_api = [];
                                                $message['id'] = uniqid("", true);
                                                $message['subject'] = $oggetto_parenti;
                                                $message['content_subtype'] = "html";
                                                $message['to'] = $to;
                                                $message['cc'] = $cc;
                                                $message['bcc'] = $bcc;
                                                $message['body'] = $testo_parenti;
                                                $messages_api[] = $message;

                                                if ($nuovo_studente['invia_email'] == 'SI' || $nuovo_studente['invia_email'] == 'SI_STESSO_TESTO'){
                                                    $invio_mailer = invia_email($messages_api, $current_user);
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                $messaggio = "Errore inserimento servizio.";
                            }

                            if ($elemento_marketplace['id_tipo_movimento'] > 0){
                                $tipi_tasse = estrai_tipi_tasse();
                                foreach ($tipi_tasse as $tipo){
                                    if ($tipo['id'] == $elemento_marketplace['id_tipo_movimento']){
                                        $movimento = inserisci_tassa($id_studente_corrente, $elemento_marketplace['id_tipo_movimento'], '', $tipo['amount'], 1, strtotime("00:00:00"), $tipo['school_year'], '', $current_user, '', time(), null, false, -1, true);

                                        if ($movimento['success'] == 1){
                                            $messaggio .= " Movimento inserito correttamente";
                                        } else {
                                            $messaggio .= " Errore inserimento movimento";
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        // INVIO AMMINISTRAZIONE
                        $to = [];
                        $cc = [];
                        $bcc = [];
                        $param_indirizzi_amministrazione = estrai_parametri_singoli('INDIRIZZI_MAIL_AMMINISTRAZIONE');
                        sort($elenco_studenti_inseriti);

                        $testo_amministrazione .= "<ul><li>".implode("</li><li>", $elenco_studenti_inseriti)."</li></ul>";

                        $to = explode('###', $param_indirizzi_amministrazione);

                        if (!empty($to)) {
                            $message = [];
                            $messages_api = [];
                            $message['id'] = uniqid("", true);
                            $message['subject'] = $oggetto_amministrazione;
                            $message['content_subtype'] = "html";
                            $message['to'] = $to;
                            $message['cc'] = $cc;
                            $message['bcc'] = $bcc;
                            $message['body'] = $testo_amministrazione;
                            $messages_api[] = $message;

                            $invio_mailer = invia_email($messages_api, $current_user);
                        }
                    } else {
                        $messaggio = "Errore inserimento servizio, nessuno studente selezionato";
                    }
                    break;
                case "allinea_validita":
                    $parametri = [
                        "db_richiesto" => $db_key
                    ];
                    $dati_servizio = nextapi_call('marketplace/'.$id_servizio_selezionato, 'GET', $parametri, $current_key);

                    $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

                    $parametri = [
                        "db_richiesto" => $db_key,
                        "id_marketplace" => $id_servizio_selezionato,
                        "validita_inizio" => ["end" => strtotime(explode('/', $anno_scolastico)[1] . '-08-30')],
                        "validita_fine" => ["start" => strtotime(explode('/', $anno_scolastico)[0] . '-08-31')]
                    ];
                    $acquisti_studenti = nextapi_call('marketplace/acquisti', 'GET', $parametri, $current_key);

                    foreach ($acquisti_studenti as $acquisto) {
                        $payload = [
                            "id_acquisto" => $acquisto['id_acquisto'],
                            "validita_inizio" => $dati_servizio[$id_servizio_selezionato]['validita_inizio'],
                            "validita_fine" => $dati_servizio[$id_servizio_selezionato]['validita_fine']
                        ];
                        $acquisto_studente = nextapi_call('marketplace/acquisti/modifica', 'PUT', $payload, $current_key);
                    }
                    $messaggio = "Allineamento effettuato";
                    break;
            }

            $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

            $studenti_totali = estrai_studenti_istituto('compatta');

            $parametri = [
                "db_richiesto" => $db_key,
                "id_marketplace" => $id_servizio_selezionato,
                "validita_inizio" => ["end" => strtotime(explode('/', $anno_scolastico)[1] . '-08-30')],
                "validita_fine" => ["start" => strtotime(explode('/', $anno_scolastico)[0] . '-08-31')]
            ];
            $acquisti_studenti = nextapi_call('marketplace/acquisti', 'GET', $parametri, $current_key);


            $id_studenti_acquisti = array_unique(array_column($acquisti_studenti, 'id_studente'));
            if ($elemento_marketplace['id_tipo_movimento'] != "") {
                $pagamenti_studenti = estrai_pagamenti_studenti_tipo_movimento($elemento_marketplace['id_tipo_movimento'], $id_studenti_acquisti);
            } else {
                $pagamento_non_necessario = true;
                $template->assign("pagamento_non_necessario", $pagamento_non_necessario);
            }

            $array_acquisti_finale = [];
            foreach ($acquisti_studenti as $acquisto) {
                $chiave = $acquisto['cognome'] . $acquisto['nome'] . $acquisto['data_inserimento'] . $acquisto['id_acquisto'];

                unset($studenti_totali[$acquisto['id_studente']]);

                if ($pagamento_non_necessario) {
                    $pagamenti_studente[0]['valore_da_visualizzare'] = 'Non necessario';
                } else {
                    $pagamenti_studente = array_filter($pagamenti_studenti, function($pagamento) use ($acquisto) {
                        if ($acquisto['id_studente'] == $pagamento['id_studente']) {
                            //Filtro aggiuntivo in base alla data che deve essere interna a quella della validita' dell'acquisto marketplace
                            //$ts_operazione = strtotime($pagamento['operation_date']);
                            $ts_operazione = $pagamento['expiration_date'];
                            $anno_scolastico_movimento = $pagamento['school_year'];
                            $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

                            if ((date('Y-m-d',$acquisto['data_inserimento']) <= date('Y-m-d',strtotime($pagamento['operation_date']))) && $pagamento['operation_date'] != ""){
                                return $pagamento;
                            }

                            //if ($anno_scolastico == $anno_scolastico_movimento) {
                                //if ($ts_operazione >= $acquisto['validita_inizio'] && $ts_operazione <= $acquisto['validita_fine']){
                            //    return $pagamento;
                            //}
                        }
                    });

                    foreach ($pagamenti_studente as $key => $pagamento) {
                        $pagamenti_studente[$key]['valore_da_visualizzare'] = ($pagamento['total'] != "") ? $pagamento['total'] : "";
                        $pagamenti_studente[$key]['data_operazione'] = ($pagamento['operation_date'] != "") ? date('Y-m-d', strtotime($pagamento['operation_date'])) : "";
                    }

                    if (count($pagamenti_studente) == 0) {
                        $pagamenti_studente[0]['valore_da_visualizzare'] = 'Nessuno';
                    }
                }
                $acquisto['pagamenti'] = $pagamenti_studente;

                $acquisto['data_inserimento_tradotta'] = date('Y-m-d', $acquisto['data_inserimento']);
                $acquisto['validita_inizio'] = date('Y-m-d', $acquisto['validita_inizio']);
                $acquisto['validita_fine'] = date('Y-m-d', $acquisto['validita_fine']);
                $array_acquisti_finale[$chiave] = $acquisto;
            }

            $classi_principali = estrai_classi('base', 'classi_principali');
            $indirizzi_principali = [];
            foreach ($classi_principali as $classe){
                $indirizzi_principali[$classe['id_indirizzo']]['id_indirizzo'] = $classe['id_indirizzo'];
                $indirizzi_principali[$classe['id_indirizzo']]['descrizione_indirizzi'] = $classe['descrizione_indirizzi'];
                $indirizzi_principali[$classe['id_indirizzo']]['codice_indirizzi'] = $classe['codice_indirizzi'];
            }

            $template->assign("classi_principali", $classi_principali);
            $template->assign("indirizzi_principali", $indirizzi_principali);

            $template->assign("elemento_marketplace", $elemento_marketplace);
            $template->assign("studenti_totali", $studenti_totali);
            $template->assign("array_acquisti_finale", $array_acquisti_finale);
            $template->assign("messaggio", $messaggio);

            if ($operazione == 'esporta_csv') {
                $csv = "";
                $csv .= "<p>Servizio: " . $elemento_marketplace['descrizione'] . "</p>";

                $csv .= "<table>";
                $csv .= "<tr>";
                $csv .= "<td><b>Studente</b></td>";
                $csv .= "<td><b>Classe</b></td>";
                $csv .= "<td><b>Indirizzo</b></td>";
                $csv .= "<td><b>Data inserimento</b></td>";
                $csv .= "<td><b>" . encode("Validità") . " dal</b></td>";
                $csv .= "<td><b>" . encode("Validità") . " al</b></td>";
                if ($esporta == 'con_pagamenti'){
                    if (!$pagamento_non_necessario) {
                        $csv .= "<td><b>Data pagamento</b></td>";
                    }
                    $csv .= "<td><b>Pagamento</b></td>";
                }
                $csv .= "</tr>";


                foreach ($array_acquisti_finale as $acquisti_studente) {
                    $i = 0;
                    $csv .= "<tr>";
                    if ($i == 0) {
                        $rowspan = "";
                        if ($esporta == 'con_pagamenti') {
                            $rowspan = " rowspan='" . count($acquisti_studente['pagamenti']) . "' ";
                        }

                        $csv .= "<td {$rowspan}>" . encode($acquisti_studente['cognome'] . ' ' . $acquisti_studente['nome']) . "</td>";
                        $csv .= "<td {$rowspan}>" . $acquisti_studente['classe'] . $acquisti_studente['sezione'] . "</td>";
                        $csv .= "<td {$rowspan}>" . $acquisti_studente['codice_indirizzi'] . "</td>";
                        $csv .= "<td {$rowspan}>" . $acquisti_studente['data_inserimento_tradotta'] . "</td>";
                        $csv .= "<td {$rowspan}>" . $acquisti_studente['validita_inizio'] . "</td>";
                        $csv .= "<td {$rowspan}>" . $acquisti_studente['validita_fine'] . "</td>";
                    }

                    if ($esporta == 'con_pagamenti') {
                        foreach ($acquisti_studente['pagamenti'] as $pagamento) {
                            if ($i > 0) {
                                $csv .= "</tr><tr>";
                            }

                            if (!$pagamento_non_necessario) {
                                $csv .= "<td>" . $pagamento['data_operazione'] . "</td>";
                            }
                            $csv .= "<td>" . $pagamento['valore_da_visualizzare'] . "</td>";

                            $csv .= "</tr>";

                            $i++;
                        }

                        if (count($acquisti_studente['pagamenti']) == 0) {
                            $csv .= "</tr>";
                        }
                    } else {
                        $csv .= "</tr>";
                    }
                }

                $csv .= "</table>";

                //cancello tutte i file temporanei fatti da più di un'ora
                $dir = 'tmp_xls';
                $tipo_stampa = 'elenchi_studenti_' . date('Y-m-d_Hi');

                CleanFiles($dir);
                //creo i nuovi file temporanei
                $file = basename(tempnam($dir, 'tmp'));
                rename($dir . '/' . $file, $dir . '/' . $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls');
                $file = $tipo_stampa . '_' . date('Y-m-d_H-i_s') . '.xls';
                //Salva il file xls come file
                $nuovo_nome = $dir . '/' . $file;
                $handle = fopen($nuovo_nome, 'w');
                fwrite($handle, $csv);
                fclose($handle);

                //Reindirizzamento JavaScript
                echo "<HTML><SCRIPT>document.location='{$nuovo_nome}';</SCRIPT></HTML>";
            }
        }
        break;
    case 'gestione_marketplace':
        $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

        switch ($operazione) {
            case 'inserisci_oggetto_marketplace':
                $validita_inizio = ($nuovo_marketplace['validita_inizio'] != "") ? strtotime($nuovo_marketplace['validita_inizio'] . ' 01:00') : 0;
                $validita_fine = ($nuovo_marketplace['validita_fine'] != "") ? strtotime($nuovo_marketplace['validita_fine'] . ' 23:00') : 0;

                $payload = [
                    "descrizione"           =>  $nuovo_marketplace['descrizione'],
                    "db_richiesto"          =>  $db_key,
                    "tipo"                  =>  $nuovo_marketplace['tipo'],
                    "id_tipo_movimento"     =>  $nuovo_marketplace['id_tipo_movimento'],
                    //"codice"                =>  $nuovo_marketplace['codice'],
                    "ordinamento"           =>  $nuovo_marketplace['ordinamento'],
                    "nome_sitoapp"          =>  $nuovo_marketplace['nome_sitoapp'],
                    "descrizione_sitoapp"   =>  $nuovo_marketplace['descrizione_sitoapp'],
                    "validita_inizio"       =>  $validita_inizio,
                    "validita_fine"         =>  $validita_fine,
                    "pubblica_sitoapp"      =>  $nuovo_marketplace['pubblica_sitoapp'],
                    "categoria"             =>  $nuovo_marketplace['categoria']
                ];

                switch ($nuovo_marketplace['caratteristiche']) {
                    case 'adesione_giornaliera':
                        $orario_limite_inserimento = ($nuovo_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_inserimento'] != '') ? $nuovo_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_inserimento'] : '14:00';
                        $orario_limite_cancellazione = ($nuovo_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_cancellazione'] != '') ? $nuovo_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_cancellazione'] : '12:00';

                        $payload['caratteristiche'] = [
                            'adesione_giornaliera' => 'SI',
                            'orario_limite_inserimento' => $orario_limite_inserimento,
                            'orario_limite_cancellazione' => $orario_limite_cancellazione,
                            'giustificazione_attiva' => $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giustificazione_attiva'],
                            'notifica_assenze' => $nuovo_marketplace['caratteristiche_adesione_giornaliera']['notifica_assenze'],
                            'giornate_attive' => [
                                "lun"   => (in_array('lun', $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "mar"   => (in_array('mar', $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "mer"   => (in_array('mer', $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "gio"   => (in_array('gio', $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "ven"   => (in_array('ven', $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "sab"   => (in_array('sab', $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "dom"   => (in_array('dom', $nuovo_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                            ],
                            'costo_unitario_prenotazione' => $nuovo_marketplace['caratteristiche_adesione_giornaliera']['costo_unitario_prenotazione'],
                            'cosa_contabilizzare' => $nuovo_marketplace['caratteristiche_adesione_giornaliera']['cosa_contabilizzare']
                        ];
                        break;
                    default:
                        break;
                }

                $inserimento = nextapi_call('marketplace/inserisci', 'POST', $payload, $current_key);

                if ($inserimento > 0) {
                    $messaggio = print_label("Inserimento avvenuto con successo");
                } else {
                    $messaggio = print_label("Errore inserimento") . '. ' . implode(', ', $inserimento['errori']);
                }
                break;
            case 'elimina_oggetto_marketplace':
                $payload = [
                    "db_richiesto"      =>  $db_key,
                    "id_marketplace"    =>  $id_marketplace
                ];

                $elimina = nextapi_call('marketplace/elimina', 'DELETE', $payload, $current_key);

                if ($elimina['esito'] == 'OK') {
                    $messaggio = print_label("Eliminazione avvenuto con successo");
                } else {
                    $messaggio = print_label("Errore eliminazione") . '. ' . $elimina['errori'];
                }
                break;
            case 'modifica_oggetto_marketplace':
                $validita_inizio = ($modifica_marketplace['validita_inizio'] != "") ? strtotime($modifica_marketplace['validita_inizio'] . ' 01:00') : 0;
                $validita_fine = ($modifica_marketplace['validita_fine'] != "") ? strtotime($modifica_marketplace['validita_fine'] . ' 23:00') : 0;

                $payload = [
                    "descrizione"           =>  $modifica_marketplace['descrizione'],
                    "db_richiesto"          =>  $db_key,
                    "tipo"                  =>  $modifica_marketplace['tipo'],
                    "id_tipo_movimento"     =>  $modifica_marketplace['id_tipo_movimento'],
                    //"codice"                =>  $modifica_marketplace['codice'],
                    "ordinamento"           =>  $modifica_marketplace['ordinamento'],
                    "nome_sitoapp"          =>  $modifica_marketplace['nome_sitoapp'],
                    "descrizione_sitoapp"   =>  $modifica_marketplace['descrizione_sitoapp'],
                    "validita_inizio"       =>  $validita_inizio,
                    "validita_fine"         =>  $validita_fine,
                    "pubblica_sitoapp"      =>  $modifica_marketplace['pubblica_sitoapp'],
                    "categoria"             =>  $modifica_marketplace['categoria']
                ];

                switch ($modifica_marketplace['caratteristiche']) {
                    case 'adesione_giornaliera':
                        $orario_limite_inserimento = ($modifica_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_inserimento'] != '') ? $modifica_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_inserimento'] : '14:00';
                        $orario_limite_cancellazione = ($modifica_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_cancellazione'] != '') ? $modifica_marketplace['caratteristiche_adesione_giornaliera']['orario_limite_cancellazione'] : '12:00';

                        $payload['caratteristiche'] = [
                            'adesione_giornaliera' => 'SI',
                            'orario_limite_inserimento' => $orario_limite_inserimento,
                            'orario_limite_cancellazione' => $orario_limite_cancellazione,
                            'giustificazione_attiva' => $modifica_marketplace['caratteristiche_adesione_giornaliera']['giustificazione_attiva'],
                            'notifica_assenze' => $modifica_marketplace['caratteristiche_adesione_giornaliera']['notifica_assenze'],
                            'giornate_attive' => [
                                "lun"   => (in_array('lun', $modifica_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "mar"   => (in_array('mar', $modifica_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "mer"   => (in_array('mer', $modifica_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "gio"   => (in_array('gio', $modifica_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "ven"   => (in_array('ven', $modifica_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "sab"   => (in_array('sab', $modifica_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                                "dom"   => (in_array('dom', $modifica_marketplace['caratteristiche_adesione_giornaliera']['giornate_attive'])) ? 'SI' : 'NO',
                            ],
                            "costo_unitario_prenotazione"   =>  $modifica_marketplace['caratteristiche_adesione_giornaliera']['costo_unitario_prenotazione'],
                            "cosa_contabilizzare"           =>  $modifica_marketplace['caratteristiche_adesione_giornaliera']['cosa_contabilizzare']
                        ];
                        break;
                    default:
                        break;
                }

                $modifica = nextapi_call('marketplace/modifica/'.$id_marketplace, 'POST', $payload, $current_key);

                if ($modifica == $id_marketplace) {
                    $messaggio = print_label("Modifica avvenuto con successo");
                } else {
                    $messaggio = print_label("Errore modifica") . '. ' . $modifica['errori'];
                }
                break;
            case 'inserisci_adesioni':
            case 'elimina_adesioni':
                if ($id_marketplace_adesioni > 0){
                    // estraggo gli studenti iscritti al servizio
                    $parametri = [
                        "db_richiesto" => $db_key,
                        "id_marketplace" => $id_marketplace_adesioni,
                        "validita_inizio" => ["end" => strtotime(explode('/', $anno_scolastico)[1] . '-08-30')],
                        "validita_fine" => ["start" => strtotime(explode('/', $anno_scolastico)[0] . '-08-31')]
                    ];
                    $acquisti_studenti = nextapi_call('marketplace/acquisti', 'GET', $parametri, $current_key);

                    if (count($acquisti_studenti > 0)){
                        foreach ($acquisti_studenti as $acquisto){
                            $payload = [
                                "id_marketplace"    =>  $id_marketplace_adesioni,
                                "id_studente"       =>  $acquisto['id_studente'],
                                "data_inizio"       =>  $data_inizio_adesioni,
                                "data_fine"         =>  $data_fine_adesioni
                            ];
                            if ($operazione == 'inserisci_adesioni'){
                                $payload['tipo_operazione'] = 'INSERISCI';
                            } elseif ($operazione == 'elimina_adesioni'){
                                $payload['tipo_operazione'] = 'ELIMINA';
                            }

                            $aggiornamento = nextapi_call('marketplace/servizi_giornalieri/aggiorna_adesioni_studente', 'PUT', $payload, $current_key);
                        }
                        $messaggio = print_label("Operazione terminata");
                    } else {
                        $messaggio = print_label("Nessuno studente iscritto al servizio");
                    }
                } else {
                    $messaggio = print_label("Nessun servizio selezionato");
                }
                break;

            default:
                break;
        }

        $tipi_tasse = [];
        $tipi_tasse_totale = estrai_tipi_tasse();
        foreach ($tipi_tasse_totale as $tipo) {
            if ($tipo['anno_scolastico_riferimento'] == $anno_scolastico || $tipo['anno_scolastico_riferimento'] == 'TUTTI') {
                $tipi_tasse[] = $tipo;
            }
        }
        $template->assign("tipi_tasse_totale", $tipi_tasse_totale);
        $template->assign("tipi_tasse", $tipi_tasse);

        $elenco_tipi = [];
        $elenco_oggetti_marketplace = nextapi_call('marketplace', 'GET', ["db_richiesto" => $db_key], $current_key);
        foreach ($elenco_oggetti_marketplace as $key => $oggetto) {
            if ($oggetto['caratteristiche']['adesione_giornaliera'] == 'SI') {
                $elenco_oggetti_marketplace[$key]['caratteristiche_codici'] = 'Adesione giornaliera';
            }

            foreach ($tipi_tasse_totale as $tipo) {
                if ($tipo['id_tipo_tassa'] == $oggetto['id_tipo_movimento']) {
                    $elenco_oggetti_marketplace[$key]['tipo_movimento_mc2'] = $tipo['descrizione'];
                }
            }

            $elenco_tipi[$oggetto['tipo']] = $oggetto['tipo'];
        }
        $template->assign("elenco_oggetti_marketplace", $elenco_oggetti_marketplace);

        ksort($elenco_tipi);
        $template->assign("elenco_tipi", $elenco_tipi);
        $template->assign("db_key", $db_key);
        break;

    case 'abbinamento_scansioni_verifiche':
        if (!isset($data_verifica)){
            $data_verifica = date('Y-m-d');
        }
        $template->assign("data_verifica", $data_verifica);

        // $file_location = '/mnt/scanner/MX-2651_20220524_164859.tif';
        // $file = file_get_contents($file_location);
        // $document = new Imagick($file_location);
        // $document->setImageFormat("pdf");
        // $document->writeImages('/tmp/MX-2651_20220524_164859.pdf', false);  // false = un file per pagina - true = un file multipagina
        $cartella_pdf = 'tmp_pdf/';
        $percorso_cartella = estrai_parametri_singoli('CARTELLA_SCANSIONI_VERIFICHE');
        $risoluzione = estrai_parametri_singoli('RISOLUZIONE_VERIFICHE');
        if ($percorso_cartella == ''){
            print '<pre>';
            print_r("Nessuna cartella per le scansioni impostata");
            print '</pre>';
            break;
        }
        if (substr($percorso_cartella, -1) != '/'){
            $percorso_cartella .= '/';
        }
        $contenuto_cartella = scandir($percorso_cartella);
        $elenco_scansioni = [];
        foreach ($contenuto_cartella as $file) {
            if (is_file($percorso_cartella . $file)) {
                $elenco_scansioni[] = $file;
            }
        }
        $template->assign("elenco_scansioni", $elenco_scansioni);

        switch ($operazione) {
            case 'carica_pdf':
                $array_file_studenti = [];
                $cartella_salvataggio = "/tmp/" . time() . '_' . $id_classe . '_' . $id_materia . '/';
                mkdir($cartella_salvataggio);
                $dati_materia = estrai_dati_materia($id_materia);
                $dati_classe = estrai_classe($id_classe);
                $anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');

                $server_id = messengerGetParameter('SERVER_ID')[0]['value'];
                $user_id = messengerGetUserID($current_user);
                $channels = messengerGetChannelsClassSubject($id_classe, $id_materia, $current_user);

                if ($data_verifica == ''){
                    $data_verifica = date('Y-m-d');
                }

                foreach ($caricamenti as $nome_file => $dati){
                    if ($dati['id_studente'] > 0 && $dati['carica'] == 'SI'){
                        $array_file_studenti[$dati['id_studente']][] = $nome_file;
                    }
                }

                foreach ($array_file_studenti as $id_studente => $files){
                    $im = new Imagick();
                    $im->setResolution($risoluzione, $risoluzione);
                    //$nome_file = str_replace(' ', '_', ucwords(strtolower($dati_materia['nome_materia_breve']))).'_'.$id_studente.'.pdf';
                    $nome_materia_bonificato = substr(preg_replace("/[^a-zA-Z0-9_]/", "", str_replace(' ', '_', ucwords(strtolower($dati_materia['nome_materia_breve'])))), 0, 10); //10 caratteri max senza carateri speciali
                    $nome_classe = str_replace(' ', '_', $dati_classe['classe'].$dati_classe['sezione'].'_'.$dati_classe['codice_indirizzi']);
                    $nome_file = str_replace('-', '_', $data_verifica) . '_' . $nome_materia_bonificato . '_' . $nome_classe . '_' . $id_studente . '.pdf';

                    foreach ($files as $key => $file){
                        $im->readimage($file);
                    }

                    // $im = new Imagick($files[0]);
                    // $im->readimage($files[0]);
                    // $im->readimage($files[1]);
                    $im->setImageFormat('pdf');
                    $im->writeImages($cartella_salvataggio . $nome_file, true);
                    $im->clear();
                    $im->destroy();


                    // salvataggio messenger
                    $token = '?_dc=' . round(microtime(true) * 1000) . '&_authId=' . $user_id . '&_token=' . md5($server_id . $user_id . date('Y-m-d') . time());
                    $url = 'localhost/messenger/1.0/messages' . $token;
                    $tags = 'VERIFICHE';
                    $file = [
                        'name'      =>  $nome_file,
                        'type'      =>  mime_content_type($cartella_salvataggio . $nome_file),
                        'tmp_name'  =>  $cartella_salvataggio . $nome_file,
                        'error'     =>  0,
                        'size'      =>  filesize($cartella_salvataggio . $nome_file),
                        'content'   =>  base64_encode(file_get_contents($cartella_salvataggio . $nome_file))
                    ];
                    $parametri = [
                        'file'          =>  $file,
                        'channels'      =>  [$channels['uuids']],
                        'properties'    =>  [
                            'classId'       =>  $id_classe,
                            'subjectId'     =>  $id_materia,
                            'year'          =>  $anno_scolastico_attuale,
                            'userId'        =>  $id_studente,
                            'testDate'      =>  $data_verifica,
                            'scansioneOrigine'  =>  $nome_scansione
                        ],
                        'tags'          =>  [$tags],
                        // 'acl'           =>  [
                        //     'user::'  => 'rwx',
                        //     'group::' => 'r--',
                        //     'other::' => '---',
                        // ]
                    ];

                    // if (count($channels['uuida']) > 0) {
                    //     foreach ($channels['uuida'] as $channel) {
                    //         $parametri['acl']['group:' . $channel . ':'] = "r--";
                    //     }
                    // }

                    $response = chiamata_api($url, $parametri, 'POST');

                    unlink($cartella_salvataggio . $nome_file);
                }

                rmdir($cartella_salvataggio);

                $messaggio = print_label('Operazione completata');
                break;
            default:
                break;
        }

        // pulizia cartelle piu' vecchie di un giorno
        $contenuto_cartella_pdf = scandir($cartella_pdf);
        foreach ($contenuto_cartella_pdf as $cartella_o_file){
            if (!is_file($cartella_pdf . $cartella_o_file)){
                if (!(in_array($cartella_o_file, ['.', '..']))){
                    $ts_ultima_modifica = filemtime($cartella_pdf . $cartella_o_file);

                    if ((time() - $ts_ultima_modifica) >= (60*60)){
                        exec('rm -r ' .$cartella_pdf . $cartella_o_file);
                    }
                }
            }
        }

        $elenco_classi = estrai_classi();
        $template->assign("elenco_classi", $elenco_classi);

        if ($id_classe > 0){
            $elenco_materie = estrai_materie_classe($id_classe);
            $template->assign("elenco_materie", $elenco_materie);

            if ($nome_scansione != ""){
                if (file_exists($percorso_cartella . $nome_scansione)){
                    $elenco_studenti = estrai_studenti_classe($id_classe);
                    $template->assign("elenco_studenti", $elenco_studenti);

                    $nome_scansione_senza_estensione = substr($nome_scansione, 0, strlen($nome_scansione) - strlen(end(explode('.', $nome_scansione))) - 1);
                    if (!file_exists($cartella_pdf . $nome_scansione_senza_estensione)){
                        mkdir($cartella_pdf . $nome_scansione_senza_estensione);
                    }
                    $document = new Imagick($percorso_cartella . $nome_scansione);
                    $document->setImageFormat("pdf");
                    $document->writeImages($cartella_pdf . $nome_scansione_senza_estensione . '/' . $nome_scansione_senza_estensione . '.pdf', false);  // false = un file per pagina - true = un file multipagina

                    // rinomino i file per non far precedere i -2 ai -15 per esempio formattando i numeri con '000'
                    $elenco_file_pdf_tmp = scandir($cartella_pdf . $nome_scansione_senza_estensione . '/');
                    foreach ($elenco_file_pdf_tmp as $file) {
                        if (is_file($cartella_pdf . $nome_scansione_senza_estensione . '/' . $file)) {
                            $file_tmp = substr($file, 0, -strlen('.pdf'));
                            $numero_file = substr($file_tmp, strlen($nome_scansione_senza_estensione) + 1);

                            $nuovo_numero = sprintf('%03d', $numero_file);
                            rename($cartella_pdf . $nome_scansione_senza_estensione . '/' . $file,
                                $cartella_pdf . $nome_scansione_senza_estensione . '/' . $nome_scansione_senza_estensione . '-' . $nuovo_numero . '.pdf');
                        }
                    }

                    $elenco_file_pdf_tmp = scandir($cartella_pdf . $nome_scansione_senza_estensione . '/');
                    $elenco_file_pdf = [];
                    foreach ($elenco_file_pdf_tmp as $file) {
                        if (is_file($cartella_pdf . $nome_scansione_senza_estensione . '/' . $file)) {
                            $file_pdf['nome_file'] = $file;
                            $file_pdf['percorso_file_pdf'] = $cartella_pdf . $nome_scansione_senza_estensione . '/' . $file;
                            $elenco_file_pdf[] = $file_pdf;
                        }
                    }
                    $template->assign("elenco_file_pdf", $elenco_file_pdf);
                } else {
                    // file non trovato
                }
            }
        }

        $template->assign("id_classe", $id_classe);
        $template->assign("id_materia", $id_materia);
        $template->assign("nome_scansione", $nome_scansione);
        $template->assign("messaggio", $messaggio);
        break;

    case 'gestione_verifiche':
        switch ($operazione) {
            case 'elimina_verifiche':
                $del = messengerDeleteDoc($id_file);
                if ($del){
                    $messaggio = print_label("Operazione terminata");
                } else {
                    $messaggio = print_label("Errore eliminazione");
                }
                break;
            default:
                break;
        }
        $elenco_classi = estrai_classi();
        $template->assign("elenco_classi", $elenco_classi);

        if ($id_classe > 0) {
            $elenco_materie = estrai_materie_classe($id_classe);
            $template->assign("elenco_materie", $elenco_materie);
        }

        $elenco_studenti = estrai_studenti_istituto('rapida');
        $template->assign("elenco_studenti", $elenco_studenti);

        if ($id_classe > 0 && $id_materia > 0){
            $elenco_verifiche = messengerFindDocs(null, null, 0, ['VERIFICHE'], $id_classe, $id_materia);
            $studenti_classe = estrai_studenti_classe_registro($id_classe, null, false, 'base');
        }

        if ($id_studente > 0){
            $elenco_verifiche = messengerFindDocs(null, null, (int) $id_studente, ['VERIFICHE']);
            $studenti_classe = [estrai_dati_studente($id_studente)];
        }

        if (count($elenco_verifiche) > 0){
            $elenco_materie_tmp = [];

            $id_files_per_link = [];
            foreach ($elenco_verifiche as $key => $file){
                $id_files_per_link[$file['id']] = $file['id'];
            }
            $payload = [
                "id_files"   =>  $id_files_per_link
            ];
            $link = nextapi_call("/documents/scarica_verifiche", 'GET', $payload, $current_key);

            foreach ($elenco_verifiche as $key => $file){
                $hstore = $file['properties'];
                $properties_decoded = json_decode('{' . str_replace('"=>"', '":"', $hstore) . '}', true);
                $elenco_verifiche[$key]['properties_decoded'] = $properties_decoded;

                if (isset($link[$file['id']]) && $link[$file['id']] != '') {
                    $elenco_verifiche[$key]['link'] = $link[$file['id']];
                }

                foreach ($elenco_classi as $classe){
                    if ($classe['id_classe'] == $properties_decoded['classId']){
                        $elenco_verifiche[$key]['dati_classe'] = $classe['classe'] . $classe['sezione'] . ' ' . $classe['codice_indirizzi'];
                        break;
                    }
                }

                $materia_trovata = false;
                foreach ($elenco_materie as $materia){
                    if ($materia['id_materia'] == $properties_decoded['subjectId']){
                        $elenco_verifiche[$key]['dati_materia'] = $materia['nome_materia_breve'];
                        $materia_trovata = true;
                        break;
                    }
                }

                if (!($materia_trovata) && $properties_decoded['subjectId'] > 0){
                    if (isset($elenco_materie_tmp[$properties_decoded['subjectId']])){
                        $dati_materia_non_trovata = $elenco_materie_tmp[$properties_decoded['subjectId']];
                    } else {
                        $dati_materia_non_trovata = estrai_dati_materia($properties_decoded['subjectId']);
                        $elenco_materie_tmp[$dati_materia_non_trovata] = $dati_materia_non_trovata;
                    }

                    $elenco_verifiche[$key]['dati_materia'] = $dati_materia_non_trovata['nome_materia_breve'];
                }

                $studente_trovato = false;
                foreach ($studenti_classe as $studente){
                    if ($studente['id_studente'] == $properties_decoded['userId']){
                        $elenco_verifiche[$key]['dati_studente'] = $studente['cognome'] . ' ' . $studente['nome'];
                        $studente_trovato = true;
                        break;
                    }
                }

                if (!($studente_trovato) && $properties_decoded['userId'] > 0){
                    foreach ($elenco_studenti as $studente) {
                        if ($studente['id_studente'] == $properties_decoded['userId']) {
                            $elenco_verifiche[$key]['dati_studente'] = $studente['cognome'] . ' ' . $studente['nome'];
                            break;
                        }
                    }
                }
            }
        }
        $template->assign("elenco_verifiche", $elenco_verifiche);

        $template->assign("id_classe", $id_classe);
        $template->assign("id_materia", $id_materia);
        $template->assign("id_studente", $id_studente);
        $template->assign("messaggio", $messaggio);
        break;

    case 'gestione_servizi_giornalieri':
        include 'impostazioni/servizi_giornalieri.php';
        break;

    case 'report_moduli':
        $elenco_moduli = nextapi_call('modules/templates/indirizzo/tutti', 'GET', ['archiviati' => 'NO'], $current_key);
        $template->assign("elenco_moduli", $elenco_moduli);
        break;

    case 'contabilita_servizi_giornalieri':
        include 'impostazioni/contabilita_servizi_giornalieri.php';
        break;

    case 'modelli_moduli':
        if (!isset($archiviati)){
            $archiviati = 'NO';
        }
        $template->assign("archiviati", $archiviati);
        break;

    case 'gestione_prompt_ai':
        $template->assign('db_key', $db_key);
        break;

    case 'statistiche_ai':
        $template->assign('db_key', $db_key);
        break;

    default:
    break;
}

$template->assign('stato_secondario', $stato_secondario);
