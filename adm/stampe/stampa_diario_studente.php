<?php
$pdf->SetAutoPageBreak("off", 1);
//unico parametro necessario per la stampa: $id_stud

include "adm/stampe/stampa_dati_anagrafici_singolo_studente.php";

//{{{ <editor-fold defaultstate="collapsed" desc="parametri per stampa riepilogo assenze">
$ctrl_assenze = 1;	//se considerare le assenze (sì)
$ctrl_entrate = 1;	//se considerare le entrate (sì)
$ctrl_uscite = 1;	//se considerare le uscite (sì)
$data_inizio = 0;	//data da cui considerare le assenze (tutte)
$data_fine = 0;		//data fino a cui considerare le assenze (tutte)
$stampa_intestazione_scuola_riepilogo_assenze = 'NO';
//}}} </editor-fold>
include "adm/stampe/stampa_riepilogo_assenze_singolo_studente.php";

//{{{ <editor-fold defaultstate="collapsed" desc="parametri per stampa riepilogo monteore">
$id_studente = $id_stud;
$anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
$arr_as = explode('/', $anno_scolastico_attuale);
$dati_classe_stud = estrai_classe_principale_studente($id_studente);
$inizio = estrai_parametri_singoli('DATA_INIZIO_LEZIONI', $dati_classe_stud['id_classe'], 'classe');
$inizio = mktime(0, 0, 0, date('m', $inizio), date('d', $inizio), date('Y', $inizio));
$arr_inizio = getdate($inizio);
$inizio_Day = $arr_inizio['mday'];
$inizio_Month_int = $arr_inizio['mon'];
$inizio_Year = $arr_inizio['year'];
$fine = estrai_parametri_singoli('DATA_FINE_LEZIONI', $dati_classe_stud['id_classe'], 'classe');
$arr_fine = getdate($fine);
$fine_Day = $arr_fine['mday'];
$fine_Month_int = $arr_fine['mon'];
$fine_Year = $arr_fine['year'];

$stampa_materie = 'SI';
$stampa_ritardi = 'SI';
//}}} </editor-fold>
include 'adm/stampe/stampa_riepilogo_assenze_monteore_singolo_studente.php';

if ($corsi_abilitati == 'SI')
{
    //{{{ <editor-fold defaultstate="collapsed" desc="parametri per stampa monteore corsi">
    $inizio_Day = 1;
    $inizio_Month_int = 9;
    $inizio_Year = $anno_inizio;
    $fine_Day = 31;
    $fine_Month_int = 8;
    $fine_Year = $anno_fine;
    $inizio = mktime(0, 0, 0, $inizio_Month_int, $inizio_Day, $inizio_Year);
    $fine = mktime(0, 0, 0, $fine_Month_int, $fine_Day, $fine_Year)  + 24 * 3600;
    $data_stampa = date('d/m/Y');
    $ora_stampa = date('H:i');
    $stampa_lezioni = 'SI';
    //}}} </editor-fold>
    include 'adm/stampe/stampa_monteore_corsi_studente.php';

    if ($coordinatore == 'SI' || $form_stato == 'amministratore')
    {
        $file_stampa = estrai_modello_stampa_corso();
        $elenco_corsi = estrai_corsi_per_studente($id_studente);

        // Ciclo i corsi
        if (count($elenco_corsi) > 0)
        {
            foreach ($elenco_corsi as $corso_singolo)
            {
                $id_classe = $corso_singolo['id_classe'];
                include "adm/stampe/stampe_corsi/" . $file_stampa;
            }
        }
    }
}

include 'adm/stampe/stampa_note_disciplinari_singolo_studente.php';

include 'adm/stampe/stampa_annotazioni_singolo_studente.php';

//{{{ <editor-fold defaultstate="collapsed" desc="parametri per stampa riepilogo voti">
$studente = estrai_dati_studente((int) $id_stud);
$dati_classe = estrai_classi_studente((int) $id_stud);
$id_classe = $dati_classe[0]["id_classe"];


$start_interval = mktime(0, 0, 0, 9, 1, $anno_inizio);
$end_interval = mktime(0, 0, 0, 9, 1, $anno_fine);
$anno_scolastico_sel = $anno_inizio . '/' . $anno_fine;

$max_voti = 24;
$larghezza = 5;

$classi = 'TUTTE';
$materie_scelte = 'TUTTE';
$intestazione_personalizzata = 'Riepilogo voti';
$pdf->AddPage('P');

// reimposto i parametri data inizio e fine per la stampa del riepilogo voti
$inizio_lezioni = estrai_parametri_singoli('DATA_INIZIO_LEZIONI', $dati_classe_stud['id_classe'], 'classe');
$inizio_Day = date('d', $inizio_lezioni);
$inizio_Month = date('m', $inizio_lezioni);
$inizio_Year = date('Y',$inizio_lezioni);
$fine_Day = date('d');
$fine_Month = date('m');
$fine_Year = date('Y');

include "adm/stampe/stampa_riepilogo_voti_studente_definitivo.php";
//}}} </editor-fold>

//statistiche andamento voti studente
include "adm/stampe/stampa_statistiche_singolo_studente.php";

//stampa competenze
$parametro_competenze_sup = estrai_parametri_singoli("COMPETENZE_SUPERIORI_ABILITATE");
$parametro_competenze_med = estrai_parametri_singoli("COMPETENZE_MEDIE_ABILITATE");
$parametro_competenze_ele = estrai_parametri_singoli("COMPETENZE_ELEMENTARI_ABILITATE");

if (
        (
        $parametro_competenze_sup == 'SI' && $dati_classe["tipo_indirizzo"] != 4 && $dati_classe["tipo_indirizzo"] != 6 && $dati_classe["tipo_indirizzo"] != 7 && ($form_stato == 'amministratore' || $coordinatore == 'SI')
        ) ||
        (
        $parametro_competenze_med == 'SI' && $dati_classe["tipo_indirizzo"] == 4 && ($form_stato == 'amministratore' || $coordinatore == 'SI')
        ) ||
        (
        $parametro_competenze_ele == 'SI' && $dati_classe["tipo_indirizzo"] == 6 && ($form_stato == 'amministratore' || $coordinatore == 'SI')
        )
)
{
    $id_studente_stampa = $id_studente;
    include 'adm/stampe/stampa_competenze_couch.php';
}
