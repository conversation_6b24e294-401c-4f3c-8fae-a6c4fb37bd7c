<?php

$tipo_primo_periodo = estrai_parametri_singoli('PRIMO_PERIODO_SCOLASTICO',(int) $id_classe, 'classe');

//stampa pagina materie 2 (pag3)
$x_base = $pdf->GetX();
$pdf->SetX($x_base);

if($valle_aosta_abilitata == 'SI')
{
	//{{{ <editor-fold defaultstate="collapsed" desc="pagella bilingue per Val d'Aosta">
	$pdf->SetFont('Times', 'B', 10);
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['cognome'], 'LT', 0, 'C');
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['nome'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_studenti[$cont]['codice_fiscale'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_sede["codice_meccanografico"], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $anno_scolastico_attuale, 'RT', 1, 'C');

	$pdf->SetX($x_base);
	$pdf->SetFont('Times', '', 6);
	$pdf->CellFitScale(50, 3, 'COGNOME / NOM', 'L', 0, 'C');
	$pdf->CellFitScale(50, 3, 'NOME / PRÉNOM', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODICE FISCALE', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODICE ISTITUTO', 0, 0, 'C');
	$pdf->CellFitScale(30, 3, 'ANNO SCOLASTICO', 'R', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale(50, 3, '', 'LB', 0, 'C');
	$pdf->CellFitScale(50, 3, '', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODE FISCAL', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'CODE DE L’ÉTABLISSEMENT', 'B', 0, 'C');
	$pdf->CellFitScale(30, 3, 'ANNÉE SCOLAIRE', 'RB', 1, 'C');
	$pdf->ln(10);

	$pdf->SetFont('Times', 'B', 10);
	$pdf->SetX($x_base);
	$pdf->CellFitScale($larghezza_max_cella , $altezza_cella_pag2/2, 'VALUTAZIONI PERIODICHE', 'LRT', 1, 'C');
	$pdf->SetX($x_base);
	$pdf->CellFitScale($larghezza_max_cella , $altezza_cella_pag2/2, 'ÉVALUATIONS PERIODIQUES', 'LRB', 1, 'C');

	for($cont_array=$materie_pagina2; $cont_array<count($array_finale_voti); $cont_array++)
	{
		$pdf->SetFont('Times', '', 10);
		$pdf->SetX($x_base);
		$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, $array_finale_voti[$cont_array][0], 'LRT', 0, 'L');
		$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, '1° frazione temporale', 'LRT', 0, 'C');
		$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, '2° frazione temporale', 'LRT', 1, 'C');

		$pdf->SetX($x_base);
		$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, $array_finale_voti[$cont_array]['descrizione_materia_straniera'], 'LRB', 0, 'L');
		$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, "1ère partie de l'année", 'LRB', 0, 'C');
		$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, "2e partie de l'année", 'LRB', 1, 'C');

		$pdf->SetX($x_base);
        if ($array_finale_voti[$cont_array]['tipo_materia'] == 'CONDOTTA')
        {
            $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, 'GIUDIZIO (2)', 'LRT', 0, 'L');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][1] . '/' . $array_finale_voti[$cont_array]['valore_pagella_lingua_voto_unico_primo_quadr'], 1, 0, 'C');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][6] . '/' . $array_finale_voti[$cont_array]['valore_pagella_lingua_voto_unico_fine_anno'], 1, 1, 'C');
                $y_temp = $pdf->GetY();

            $pdf->SetXY($x_base, $y_temp - ($altezza_cella_pag2/2));
            $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, 'Jujement', 'LRB', 1, 'L');
        }
        else
        {
            $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, 'Voto (in cifre e in lettere)', 'LRT', 0, 'L');
            $pdf->SetFont('Times', 'B', 10);
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, $array_finale_voti[$cont_array]['voto_numerico_primo_quadr'] . '/10', 'LRT', 0, 'C');
            $pdf->SetFont('Times', '', 10);
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, $array_finale_voti[$cont_array][1] . '/' . $array_finale_voti[$cont_array]['valore_pagella_lingua_voto_unico_primo_quadr'], 'LRT', 0, 'C');
            $pdf->SetFont('Times', 'B', 10);
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, $array_finale_voti[$cont_array]['voto_numerico_fine_anno'] . '/10', 'LRT', 0, 'C');
            $pdf->SetFont('Times', '', 10);
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, $array_finale_voti[$cont_array][6] . '/' . $array_finale_voti[$cont_array]['valore_pagella_lingua_voto_unico_fine_anno'], 'LRT', 1, 'C');

            $pdf->SetX($x_base);
            $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, 'Note (en chiffres et en lettres)', 'LRB', 0, 'L');
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, '', 'LRB', 0, 'C');
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, $testo_voto_decimi_francese, 'LRB', 0, 'C');
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, '', 'LRB', 0, 'C');
            $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2/2, $testo_voto_decimi_francese, 'LRB', 1, 'C');
        }

		if($stampa_campi_liberi == 'SI_RIGA_NUOVA')
		{
			//{{{ <editor-fold defaultstate="collapsed" desc="riga per campi liberi">
			if(is_array($elenco_campi_medie['fine_anno']))
			{
				foreach($elenco_campi_medie['fine_anno'] as $key => $singolo_campo)
				{
					if(
						$singolo_campo["tipo_stampa"] == "singola"
						and
						(
							$elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
							or
							$elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
						)
						and
						(
							$singolo_campo["id_materia"] == 0
							 or
							$singolo_campo["id_materia"] == $array_finale_voti[$cont_array]["id_materia"]
						)
						and
						$singolo_campo["visibile"] == "SI"
					)
					{
						$righe_primo_quadr = $pdf->MultiCellNbLines($larghezza_max_cella/3, $elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
						$righe_fine_anno = $pdf->MultiCellNbLines($larghezza_max_cella/3, $elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
						$tot_righe_cl = max($righe_primo_quadr, $righe_fine_anno);

						$pdf->SetX($x_base);
						if($singolo_campo["stampa_descrizione_campo"] == 'SI')
						{
							$divisore = 3;
							$pdf->CellFitScale($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['fine_anno'][$key]["nome"] . $singolo_campo["separatore_descrizione_valore"], 1, 0, 'L');
						}
						else
						{
							$divisore = 2;
						}
						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L', 0, 0);
						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L');
					}
				}
			}
			//}}} </editor-fold>
		}
	}
    if($stampa_firme == 'SI')
    {
        $y_rel = $pdf->GetY() + 5;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetFont('Times', '', 10);
        $pdf->CellFitScale($larghezza_max_cella/3, 0, $citta . ', ' . $data_stampa, 0, 0, 'L');
        $pdf->SetFont('Times', '', 6);
        $y = $pdf->getY();
        $x = $pdf->getX();
        $pdf->SetY($y + 1);
        $pdf->SetX($x);
        $pdf->CellFitScale($larghezza_max_cella/3, 0, '', 0, 0, 'L');
        if ($stampa_note == 'SI')
        {
            $pdf->CellFitScale($larghezza_max_cella/3, 0, 'IL DIRIGENTE SCOLASTICO (1)', 0, 1, 'C');
        }
        else
        {
            $pdf->CellFitScale($larghezza_max_cella/3, 0, 'IL DIRIGENTE SCOLASTICO', 0, 1, 'C');
        }

        $pdf->SetFont('Times', '', 6);
        $pdf->SetX($x_base);
        $pdf->CellFitScale($larghezza_max_cella/3, 0, 'LUOGO E DATA/LIEU ET DATE', 0, 0, 'L');
        $pdf->CellFitScale($larghezza_max_cella/3, 0, '', 0, 0, 'L');
        if ($stampa_note == 'SI')
        {
            $pdf->CellFitScale($larghezza_max_cella/3, 0, 'LE DIRIGEANT SCOLAIRE    (1)', 0, 1, 'C');
        }
        else
        {
            $pdf->CellFitScale($larghezza_max_cella/3, 0, 'LE DIRIGEANT SCOLAIRE', 0, 1, 'C');
        }

        $pdf->SetX($x_base);
        $pdf->CellFitScale($larghezza_max_cella/3, 0, '', 0, 0, 'L');
        $pdf->CellFitScale($larghezza_max_cella/3, 0, '', 0, 0, 'L');
        $pdf->SetFont('Times', '', 8);
        $pdf->CellFitScale($larghezza_max_cella/3, 0, '(' . $dirigente_scolastico . ')', 0, 1, 'C');

        if ($stampa_firma_digitale == 'SI') {
            $pdf->SetX($x_base);
            $pdf->CellFitScale($larghezza_max_cella/3, 0, '', 0, 0, 'L');
            $pdf->CellFitScale($larghezza_max_cella/3, 0, '', 0, 0, 'L');
            $pdf->SetFont('Times', '', 7);
            $pdf->CellFitScale($larghezza_max_cella/3, 0, '(Documento firmato digitalmente)', 0, 1, 'C');
            $pdf->SetFont('Times', '', 8);
        }
    }
	//}}} </editor-fold>
}
else
{
	//{{{ <editor-fold defaultstate="collapsed" desc="pagella ministeriale">
	$pdf->SetFont('Times', 'B', 10);
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['cognome'], 'LT', 0, 'C');
	$pdf->CellFitScale(50, 4, $dati_studenti[$cont]['nome'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_studenti[$cont]['codice_fiscale'], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $dati_sede["codice_meccanografico"], 'T', 0, 'C');
	$pdf->CellFitScale(30, 4, $anno_scolastico_attuale, 'RT', 1, 'C');

	$pdf->SetX($x_base);
	$pdf->SetFont('Times', '', 6);
	$pdf->CellFitScale(50, 4, 'COGNOME', 'LB', 0, 'C');
	$pdf->CellFitScale(50, 4, 'NOME', 'B', 0, 'C');
	$pdf->CellFitScale(30, 4, 'CODICE FISCALE', 'B', 0, 'C');
	$pdf->CellFitScale(30, 4, 'CODICE ISTITUTO', 'B', 0, 'C');
	$pdf->CellFitScale(30, 4, 'ANNO SCOLASTICO', 'RB', 1, 'C');
	$pdf->ln(5);

	$pdf->SetX($x_base);
	$pdf->SetFont('Times', 'B', 10);
	$pdf->CellFitScale($larghezza_max_cella , $altezza_cella_pag2, 'VALUTAZIONI PERIODICHE', 1, 1, 'C');

    $primo_periodo_stampa = '1° Quadrimestre';
    if ($tipo_primo_periodo == 'pentamestre') {
        $primo_periodo_stampa = '1° Trimestre';
    }

	for($cont_array=$materie_pagina2; $cont_array<count($array_finale_voti); $cont_array++)
	{
		if($array_finale_voti[$cont_array]['tipo_materia'] != 'CONDOTTA')
		{
            $pdf->SetFont('Times', '', 10);

            if($stampa_obiettivi_materia == 'SI')
            {
                $pdf->SetX($x_base);
                $y_base = $pdf->GetY();
                $x1_base = $pdf->GetX();

                $pdf->CellFitScale($larghezza_max_cella/3, 2, ' ', 0, 0, 'L');
                $x_base = $pdf->GetX();

                $pdf->MultiCell($larghezza_max_cella/3*2, $altezza_cella_pag2/2, $array_finale_voti[$cont_array][0] . ": " .$array_finale_voti[$cont_array]['descrizione_scuola_media'] , 1, 'L', false, 1);
                $pdf->SetX($x_base);
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, $primo_periodo_stampa, 1, 0, 'C');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, 'Fine Anno', 1, 1, 'C');
                $y2_base = $pdf->GetY();

                $pdf->SetXY($x1_base, $y_base);
                $pdf->CellFitScale($larghezza_max_cella/3, ($y2_base - $y_base), $array_finale_voti[$cont_array][0], 1, 1, 'L');
                $pdf->SetX($x1_base);
                $x_base = $pdf->GetX();

                if ($tipo_voto_IQ != 'PERSONALIZZATO')
                {
                    $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Voto (in cifre e in lettere)', 1, 0, 'L');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array]['voto_numerico_primo_quadr'] . '/10', 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array][1] . $testo_voto_decimi, 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array]['voto_numerico_fine_anno'] . '/10', 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array][6] . $testo_voto_decimi, 1, 1, 'C');
                }
                else
                {
                    $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Voto', 1, 0, 'L');
                    $pdf->CellFitScale($larghezza_max_cella/9, $altezza_cella_pag2, 'S: '.$array_finale_voti[$cont_array]['voto_scritto_primo_quadr'], 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/9, $altezza_cella_pag2, 'O: '.$array_finale_voti[$cont_array]['voto_orale_primo_quadr'], 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/9, $altezza_cella_pag2, 'P: '.$array_finale_voti[$cont_array]['voto_pratico_primo_quadr'], 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array]['voto_numerico_fine_anno'] . '/10', 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array][6] . $testo_voto_decimi, 1, 1, 'C');
                }
            }
            else
            {
                $pdf->SetX($x_base);
                $pdf->SetFont('Times', '', 10);
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][0], 1, 0, 'L');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $primo_periodo_stampa, 1, 0, 'C');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Fine Anno', 1, 1, 'C');

                $pdf->SetX($x_base);

                if ($tipo_voto_IQ != 'PERSONALIZZATO')
                {
                    $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Voto (in cifre e in lettere)', 1, 0, 'L');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array]['voto_numerico_primo_quadr'] . '/10', 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array][1] . $testo_voto_decimi, 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array]['voto_numerico_fine_anno'] . '/10', 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array][6] . $testo_voto_decimi, 1, 1, 'C');
                }
                else
                {
                    $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Voto', 1, 0, 'L');
                    $pdf->CellFitScale($larghezza_max_cella/9, $altezza_cella_pag2, 'S: '.$array_finale_voti[$cont_array]['voto_scritto_primo_quadr'], 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/9, $altezza_cella_pag2, 'O: '.$array_finale_voti[$cont_array]['voto_orale_primo_quadr'], 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/9, $altezza_cella_pag2, 'P: '.$array_finale_voti[$cont_array]['voto_pratico_primo_quadr'], 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array]['voto_numerico_fine_anno'] . '/10', 1, 0, 'C');
                    $pdf->CellFitScale($larghezza_max_cella/6, $altezza_cella_pag2, $array_finale_voti[$cont_array][6] . $testo_voto_decimi, 1, 1, 'C');
                }
            }
        }
		elseif ($stampa_tab_condotta != 'NO')
		{
            if($stampa_obiettivi_materia == 'SI')
            {
                $pdf->SetX($x_base);
                $y_base = $pdf->GetY();
                $x1_base = $pdf->GetX();

                $pdf->CellFitScale($larghezza_max_cella/3, 2, ' ', 0, 0, 'L');
                $x_base = $pdf->GetX();

                $pdf->MultiCell($larghezza_max_cella/3*2, $altezza_cella_pag2/2, $array_finale_voti[$cont_array][0] . ": " .$array_finale_voti[$cont_array]['descrizione_scuola_media'] , 1, 'L', false, 1);
                $pdf->SetX($x_base);
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, $primo_periodo_stampa, 1, 0, 'C');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2/2, 'Fine Anno', 1, 1, 'C');
                $y2_base = $pdf->GetY();

                $pdf->SetXY($x1_base, $y_base);
                $pdf->CellFitScale($larghezza_max_cella/3, ($y2_base - $y_base), $array_finale_voti[$cont_array][0], 1, 1, 'L');
                $pdf->SetX($x1_base);
                $x_base = $pdf->GetX();
				if($stampa_note)
				{
					$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Giudizio (2)', 1, 0, 'L');
				}
				else
				{
					$pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Giudizio', 1, 0, 'L');
				}
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][1], 1, 0, 'C');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][6], 1, 1, 'C');
            }
            else
            {
                $pdf->SetX($x_base);
                $pdf->SetFont('Times', '', 10);
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][0], 1, 0, 'L');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $primo_periodo_stampa, 1, 0, 'C');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Fine Anno', 1, 1, 'C');

                $pdf->SetX($x_base);
				if($stampa_note)
				{
	                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Giudizio (2)', 1, 0, 'L');
				}
				else
				{
	                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, 'Giudizio', 1, 0, 'L');
				}
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][1], 1, 0, 'C');
                $pdf->CellFitScale($larghezza_max_cella/3, $altezza_cella_pag2, $array_finale_voti[$cont_array][6], 1, 1, 'C');
            }
		}

		if($stampa_campi_liberi == 'SI_RIGA_NUOVA')
		{
  			//{{{ <editor-fold defaultstate="collapsed" desc="riga per campi liberi (vecchia versione non indicizzata per nome)">
            //$campi_liberi_scritti = [];
//            if(is_array($elenco_campi_medie['primo_quadr']))
//			{
//				foreach($elenco_campi_medie['primo_quadr'] as $key => $singolo_campo)
//				{
//					if(
//						$singolo_campo["tipo_stampa"] == "singola"
//						and
//						(
//							$elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
//							or
//							$elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
//						)
//						and
//						(
//							$singolo_campo["id_materia"] == 0
//							or
//							$singolo_campo["id_materia"] == $array_finale_voti[$cont_array]["id_materia"]
//						)
//						and
//						$singolo_campo["visibile"] == "SI"
//					)
//					{
//						$righe_primo_quadr = $pdf->MultiCellNbLines($larghezza_max_cella/3, $elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
//						$righe_fine_anno = $pdf->MultiCellNbLines($larghezza_max_cella/3, $elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
//						$tot_righe_cl = max($righe_primo_quadr , $righe_fine_anno);
//
//                       	$pdf->SetX($x_base);
//
//                        if($singolo_campo['stampa_descrizione_campo'] == 'SI')
//						{
//							$divisore = 3;
//							$pdf->CellFitScale($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['primo_quadr'][$key]['nome'] . ':', 1, 0, 'L');
//						}
//						else
//						{
//							$divisore = 2;
//						}
//
//						$y_temp = $pdf->GetY();
//						$x_temp = $pdf->GetX();
//						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L', 0, 0);
//						$pdf->setXY($x_temp + $larghezza_max_cella/$divisore, $y_temp);
//						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L');
//                        $campi_liberi_scritti[] = $key;
//					}
//				}
//			}
//
//			if(is_array($elenco_campi_medie['fine_anno']))
//			{
//				foreach($elenco_campi_medie['fine_anno'] as $key => $singolo_campo)
//				{
//					if(
//						$singolo_campo["tipo_stampa"] == "singola"
//						and
//						(
//							$elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
//							or
//							$elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
//						)
//						and
//						(
//							$singolo_campo["id_materia"] == 0
//							or
//							$singolo_campo["id_materia"] == $array_finale_voti[$cont_array]["id_materia"]
//						)
//						and
//						$singolo_campo["visibile"] == "SI"
//                        and
//                        ! in_array($key, $campi_liberi_scritti)
//					)
//					{
//						$righe_primo_quadr = $pdf->MultiCellNbLines($larghezza_max_cella/3, $elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
//						$righe_fine_anno = $pdf->MultiCellNbLines($larghezza_max_cella/3, $elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
//						$tot_righe_cl = max($righe_primo_quadr , $righe_fine_anno);
//
//                       	$pdf->SetX($x_base);
//
//                        if($singolo_campo['stampa_descrizione_campo'] == 'SI')
//						{
//							$divisore = 3;
//							$pdf->CellFitScale($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['fine_anno'][$key]['nome'] . ':', 1, 0, 'L');
//						}
//						else
//						{
//							$divisore = 2;
//						}
//
//						$y_temp = $pdf->GetY();
//						$x_temp = $pdf->GetX();
//						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['primo_quadr'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L', 0, 0);
//						$pdf->setXY($x_temp + $larghezza_max_cella/$divisore, $y_temp);
//						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $elenco_campi_medie['fine_anno'][$key]["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L');
//					}
//				}
//			}
			//}}} </editor-fold>
            //{{{ <editor-fold defaultstate="collapsed" desc="riga per campi liberi">
            if(is_array($elenco_campi_stampa_singola) && !empty($elenco_campi_stampa_singola))
			{
				foreach($elenco_campi_stampa_singola as $singolo_campo)
				{
					if(
//						$singolo_campo["tipo_stampa"] == "singola"    ----> l'array è già creato sulla base di questa condizione
//						and
						(
							$singolo_campo['primo_quadr']["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
							or
							$singolo_campo['fine_anno']["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"] != null
						)
						and
						(
							$singolo_campo['primo_quadr']["id_materia"] == 0
							or
							$singolo_campo['primo_quadr']["id_materia"] == $array_finale_voti[$cont_array]["id_materia"]
                            or
                            $singolo_campo['fine_anno']["id_materia"] == 0
							or
							$singolo_campo['fine_anno']["id_materia"] == $array_finale_voti[$cont_array]["id_materia"]
                            or 
                            in_array($array_finale_voti[$cont_array]["id_materia"], array_keys($singolo_campo['primo_quadr']["materia"]))
                            or
                            in_array($array_finale_voti[$cont_array]["id_materia"], array_keys($singolo_campo['fine_anno']["materia"]))
						)
						and
                        (
                            $singolo_campo['primo_quadr']["visibile"] == "SI"
                            or
                            $singolo_campo['fine_anno']["visibile"] == "SI"
                        )
					)
					{
                        if( $singolo_campo['primo_quadr']['stampa_descrizione_campo'] == 'SI' || $singolo_campo['fine_anno']['stampa_descrizione_campo'] == 'SI') {
							$divisore = 3;
                        } else {
							$divisore = 2;
                        }
						$righe_primo_quadr = $pdf->MultiCellNbLines($larghezza_max_cella/$divisore, $singolo_campo['primo_quadr']["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
						$righe_fine_anno = $pdf->MultiCellNbLines($larghezza_max_cella/$divisore, $singolo_campo['fine_anno']["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"]);
						$tot_righe_cl = max($righe_primo_quadr , $righe_fine_anno);

                       	$pdf->SetX($x_base);

                        if(
                            $singolo_campo['primo_quadr']['stampa_descrizione_campo'] == 'SI'
                            ||
                            $singolo_campo['fine_anno']['stampa_descrizione_campo'] == 'SI'
                            )
						{
                            if ($singolo_campo['primo_quadr']['nome'])
                            {
                                $pdf->CellFitScale($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $singolo_campo['primo_quadr']['nome'] . $singolo_campo['primo_quadr']['separatore_descrizione_valore'], 1, 0, 'L');
                            }
                            else
                            {
                                $pdf->CellFitScale($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $singolo_campo['fine_anno']['nome'] . $singolo_campo['fine_anno']['separatore_descrizione_valore'], 1, 0, 'L');
                            }
						}

						$y_temp = $pdf->GetY();
						$x_temp = $pdf->GetX();
						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $singolo_campo['primo_quadr']["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L', 0, 0, $x='', $y='', $reseth=true, $stretch=1);
						$pdf->setXY($x_temp + $larghezza_max_cella/$divisore, $y_temp);
						$pdf->MultiCell($larghezza_max_cella/$divisore, $tot_righe_cl*4.45, $singolo_campo['fine_anno']["materia"][$array_finale_voti[$cont_array]["id_materia"]]["valore"], 1, 'L', $fill=0, $ln=1, $x='', $y='', $reseth=true, $stretch=1);
					}
				}
			}
			//}}} </editor-fold>
		}
	}
    if($stampa_firme == 'SI')
    {
        $y_rel = $pdf->GetY() + 5;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->CellFitScale(55, 5, $citta . ', lì ' . $data_stampa, 0, 0, 'L');

        $y_rel = $pdf->GetY() + 5;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->CellFitScale(55, 5, '______________________________', 0, 0, 'L');
        $x_rel = $x_base + 135;
        $pdf->SetX($x_rel);
        $pdf->CellFitScale(55, 5, $riga_firma_dirigente, 0, 0, 'C');

        $y_rel = $pdf->GetY() + 5;
        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetFont('Times', '', 7);
        $pdf->CellFitScale(55, 5, 'Il (i) genitore (i) o chi ne fa le veci', 0, 0, 'L');
        $x_rel = $x_base + 135;
        $pdf->SetX($x_rel);
		if($stampa_note == 'SI')
		{
		    $pdf->CellFitScale(55, 3, $definizione_dirigente . ' (1)', 0, 0, 'C');
		}
		else
		{
		    $pdf->CellFitScale(55, 3, $definizione_dirigente, 0, 0, 'C');
		}

        $x_rel = $x_base + 135;
        $y_rel = $y_rel + 3;
        $pdf->SetXY($x_rel, $y_rel);
        $pdf->CellFitScale(55, 3, $dirigente_scolastico, 0, 1, 'C');
        if ($stampa_firma_digitale == 'SI') {
            $pdf->SetX($x_rel);
            $pdf->CellFitScale(55, 0, '(Documento firmato digitalmente)', 0, 0, 'C');
        }
    }
	//}}} </editor-fold>
}