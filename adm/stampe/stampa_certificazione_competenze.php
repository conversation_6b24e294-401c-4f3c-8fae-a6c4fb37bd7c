<?php

// Estrago i dati generici per tutti gli studenti
$dati_classe = estrai_classe($id_classe_certificazione);
$elenco_studenti = estrai_studenti_classe($id_classe_certificazione);

$anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
$anno = explode("/", $anno_scolastico);

switch ($dati_classe['tipo_indirizzo'])
{
    case 4:
        $ordine = 'MM';
        break;
    case 6:
        $ordine = 'EE';
        break;
    case 7:
        $ordine = 'AA';
        break;
    default:
        $ordine = 'SS';
        break;
}

// Nome del file creato
switch ($dati_classe['tipo_indirizzo'])
{
    //tolti gli spazi dalla sezione perchè dava errore nella generazione dello zip
    case 6:
        $tipo_stampa = "certificazione_competenze_elementari_" . $dati_classe['classe'] . str_replace(" ", "", $dati_classe['sezione']);
        break;
    case 4:
        $tipo_stampa = "certificazione_competenze_medie_" . $dati_classe['classe'] . str_replace(" ", "", $dati_classe['sezione']);
        break;
    default:
        $tipo_stampa = "certificazione_competenze_superiori_" . $dati_classe['classe'] . str_replace(" ", "", $dati_classe['sezione']);
        break;
}

$parametri_stampa = [
    "id_classe_certificazione"  => $id_classe_certificazione,
    "ordine"                    => $ordine,
    "data_Day"                  => $data_Day,
    "data_Month"                => $data_Month,
    "data_Year"                 => $data_Year,
    "anno_scolastico"           => $anno_scolastico,
    "trentino_abilitato"        => $trentino_abilitato,
    "ore_settimanali"           => $ore_settimanali,
    "intestazione"              => $intestazione,
    "testo_riga_1"              => $testo_riga_1,
    "testo_riga_2"              => $testo_riga_2,
    "desc_dirigente"            => $desc_dirigente,
    "stampa_firma_omessa"       => $stampa_firma_omessa,
    "stampa_nota_documento"     => $stampa_nota_documento,
    "valle_aosta_abilitata"     => $valle_aosta_abilitata,
    "dirigente_trentino"        => $dirigente_trentino,
    "stampa_compatta"           => $stampa_compatta,
    "solo_giudizi_sospesi"      => $solo_giudizi_sospesi
];


function genera_stampa(&$pdf, $studente, $parametri_stampa)
{
    $id_studente = $studente['id_studente'];
    $id_classe_certificazione = $parametri_stampa['id_classe_certificazione'];
    $ordine = $parametri_stampa['ordine'];
    $data_Day = $parametri_stampa['data_Day'];
    $data_Month = $parametri_stampa['data_Month'];
    $data_Year = $parametri_stampa['data_Year'];
    $anno_scolastico = $parametri_stampa['anno_scolastico'];
    $trentino_abilitato = $parametri_stampa['trentino_abilitato'];
    $valle_aosta_abilitata = $parametri_stampa['valle_aosta_abilitata'];
    $ore_settimanali = $parametri_stampa['ore_settimanali'];
    $intestazione = $parametri_stampa['intestazione'];
    $testo_riga_1 = $parametri_stampa['testo_riga_1'];
    $testo_riga_2 = $parametri_stampa['testo_riga_2'];
    $desc_dirigente = $parametri_stampa['desc_dirigente'];
    $stampa_firma_omessa = $parametri_stampa['stampa_firma_omessa'];
    $stampa_nota_documento = $parametri_stampa['stampa_nota_documento'];
    $dirigente_trentino = $parametri_stampa['dirigente_trentino'];
    $stampa_compatta = $parametri_stampa['stampa_compatta'];
    $solo_giudizi_sospesi = $parametri_stampa['solo_giudizi_sospesi'];
    $da_giudizio_sospeso = 'NO';
    $recuperi_inseriti = 'NO';
    $anno_inizio = explode("/", $anno_scolastico)[0];

    $dati_classe = estrai_classe($id_classe_certificazione);

    $periodo_finale = 9;
    $dati_classe['tipo_indirizzo'] == 4 ? $periodo_finale = 29 : '' ; // medie

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe_certificazione, $periodo_finale, $id_studente);

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        // recuperi inseriti
        if ($materia['in_media_pagelle'] != 'NV')
        {
            if ($voti_pagella[$id_materia]['tipo_recupero']!=''
                    ||
                $voti_pagella[$id_materia]['esito_recupero']!='')
            {
                $recuperi_inseriti = 'SI';
            }
        }
    }

    if($recuperi_inseriti == 'SI')
    {
        $da_giudizio_sospeso = 'SI';
    }
    if (
           ($solo_giudizi_sospesi == 'SI' && $da_giudizio_sospeso != 'SI')
                ||
           ($solo_giudizi_sospesi == 'NI' && $da_giudizio_sospeso == 'SI')

        )
    {
        return 'NO';
    }

    $definizione_dirigente = estrai_parametri_singoli('DEFINIZIONE_DIRIGENTE');
    $definizione_dirigente = ($desc_dirigente!=''?$desc_dirigente:$definizione_dirigente);

//    $nomi_dirigente = explode(' ', strtoupper($studente['nome_dirigente']));
//    if (in_array($nomi_dirigente[0], ['DINA', 'EMILIA', 'PROF.SSA'])){
//        $definizione_dirigente = str_replace('Il coordinatore', 'La coordinatrice', $definizione_dirigente);
//        $definizione_dirigente = str_replace('Il Coordinatore Didattico', 'La Coordinatrice Didattica', $definizione_dirigente);
//    }

    $anno = explode("/", $anno_scolastico);
    $data = $data_Day . " " . traduci_mese_in_lettere($data_Month, 'esteso') . " " . $data_Year;

    $orario_settimanale = estrai_tempo_funzionamento("'" . $dati_classe['tempo_funzionamento'] . "'");
    $competenze = estrai_competenze_scolastiche_studente($studente['id_studente'], $ordine, $anno[0]);

    $luogo_nascita = $provincia_nascita = '';
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $studente_nascita = $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $studente_nascita .=  ' (' . $stato['descrizione'] . ')';
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $studente_nascita = $studente['descrizione_comune_nascita'];
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="dizionario e testi">
    $labels = [
        "ministero_istruzione"  => "Ministero dell'istruzione e del merito",
        // ---- ELEMENTARI E MEDIE ----
        //PAG 1
        "allegato"              => "ALLEGATO ",
        "titolo1"               => "CERTIFICAZIONE DELLE COMPETENZE",
        "titolo1_ao"               => "CERTIFICAT DE COMPÉTENCES",
        "titolo2"               => "AL TERMINE ",
        "dirigente"             => $definizione_dirigente,
        "dirigente_trentino"    => "IL DIRIGENTE SCOLASTICO",
        "dirigente_ao"          => "LE DIRIGEANT SCOLAIRE",
        "decreto1"              => "Visto il decreto legislativo 13 aprile 2017, n. 62 e, in particolare, l’articolo 9;\n",
        "decreto2"              => "Visto il decreto ministeriale 3 ottobre 2017, n. 742, concernente l’adozione del modello nazionale di certificazione delle competenze per le scuole del primo ciclo di istruzione;\n",
        "decreto1_tn"           => "- Visto il Decreto del Presidente della Provincia 17 giugno 2010 16-48/Leg con cui è stato approvato il Regolamento stralcio per la definizione dei Piani di Studio Provinciali relativi al percorso del primo ciclo di istruzione (articolo 55 della legge provinciale 7 agosto 2006 n. 5);\n",
        "decreto2_tn"           => "- Visto il Decreto del Presidente della Provincia 7 ottobre 2010 22-54/ Leg. con cui è stato approvato il Regolamento sulla valutazione periodica ed annuale degli apprendimenti e della capacità relazionale degli studenti, nonché sui passaggi tra percorsi del secondo ciclo di istruzione (articoli 59 e 60, comma 1, della legge  provinciale 7 agosto 2006. n. 5);\n",
        "decreto3_tn"           => "- Visti gli atti d’ufficio relativi alle valutazioni espresse dagli insegnanti del consiglio di classe e l’esito dello scrutinio finale con l’ammissione all’esame;\n",
        "certifica"             => "CERTIFICA",
        "alunno"                => "che l'alunn||min_oa||",
        "studente"              => "che l||min_oa|| student||min_eessa||",
        "nato"                  => "nat||min_oa||",
        "nato_ao"               => "né(e) le",
        "anno_scolastico"       => "ha frequentato l'anno scolastico",
        "orario1"               => "con orario di ",
        "orario2"               => "con orario settimanale di ",
        "livello_c"             => "e ha raggiunto i livelli di competenza di seguito illustrati.",
        "livello_c_tn"          => "ha acquisito le competenze di seguito descritte e valutate sulla  base dei seguenti tre livelli:",
        //PAG 2
        "titolo1_tab1"           => "Competenze chiave europee",
        "titolo2_tab1"           => "Competenze dal Profilo dello studente\nal termine del primo ciclo di istruzione",
        "titolo3_tab1"           => "Livello (1)",
        "titolo1_tab1_tn"        => "COMPETENZE EUROPEE PER L'APPRENDIMENTO PERMANENTE",
        "titolo2_tab1_tn"        => "DESCRITTORI DI RIFERIMENTO\nAL TERMINE DEL I CICLO\nDI ISTRUZIONE",
        "titolo3_tab1_tn"        => "           LIVELLO\n\nA - BASE\nB - INTERMEDIO\nC - AVANZATO",

        // ---- SUPERIORI ----
        //PAG 1
        "ministero1"            => "MINISTERO DELL'ISTRUZIONE E DEL MERITO",
        "ministero2"            => "DELL'UNIVERSITÀ E DELLA RICERCA",
        "titolo3"               => "CERTIFICAZIONE delle COMPETENZE DI BASE",
        "titolo3_ao"            => "CERTIFICAT DE COMP&Eacute;TENCES DE BASE",
        "titolo4"               => "acquisite nell'assolvimento dell'OBBLIGO DI ISTRUZIONE",
        "titolo4_ao"            => "acquises dans le cadre de la scolarit&eacute; obligatoire",
        "decreto3"              => "Visto il regolamento emanato dal Ministero dell'Istruzione, Università e Ricerca (Ministero della Pubblica Istruzione) con decreto 22 agosto 2007, n. 139;",
        "decreto3_ao"           => "Vu le r&egrave;glement promulgu&eacute; par le ministre de l&rsquo;&Eacute;ducation, de l&rsquo;Universit&eacute; et de la Recherche (ancien ministre de l&rsquo;Instruction publique) par le d&eacute;cret n&deg; 139 du 22 ao&ucirc;t 2007 ;",
        "decreto4"              => "Visti gli atti di ufficio;",
        "decreto4_ao"           => "Vu les actes officiels,",
        "decreto5"              => "Visto il decreto del Presidente della Provincia 7 ottobre 2010, n. 22-54/Leg con il quale è stato approvato il Regolamento sulla valutazione periodica e annuale degli apprendimenti e della capacità relazionale degli studenti, nonché sui passaggi tra percorsi del secondo ciclo (articoli 59 60, comma 1, della legge provinciale 7 agosto 2006, n. 5);",
        "certifica2"            => "CERTIFICA (1)",
        "certifica2_ao"         => "CERTIFIE",
        "studente"              => "che l||min_oa|| student||min_eessa||",
        "studente_ao"              => "que l&rsquo;&eacute;l&egrave;ve",
        "stato"                 => "Stato",
        "stato_ao"              => "Etat",
        "iscritto"              => "iscritt||min_oa|| presso questo Istituto nella classe",
        "iscritto_ao"              => "inscrit/e dans cet &eacute;tablissement en classe de",
        "indirizzo_studio"      => "indirizzo di studio:",
        "indirizzo_studio_ao"              => "fili&egrave;re",
        "anno_scolastico2"      => "nell'anno scolastico",
        "anno_scolastico2_ao"              => "ann&eacute;e scolaire",
        "obbligo_istruzione"    => "nell'assolvimento dell'obbligo di istruzione della durata di 10 anni,",
        "obbligo_istruzione_ao" => "dans le cadre de la scolarit&eacute; obligatoire, d&rsquo;une dur&eacute;e de 10 ans,",
        "acquisito"             => "ha acquisito",
        "acquisito_ao"             => "a acquis",
        "competenze"            => "le competenze di base di seguito indicate.",
        "competenze_ao"            => "les comp&eacute;tences de base ci-dessous.",
        "p1_nota"           => " (*)	Nel caso di percorsi di Istruzione e Formazione Professionale occorre sostituire “indirizzo di studio” con “percorso di qualifica o diploma professionale”.",
        "p1_nota_ao"           => "Pour les parcours de formation professionnelle, remplacer &laquo; fili&egrave;re &raquo; par &laquo; parcours de qualification ou dipl&ocirc;me professionnel &raquo;",

        //PAG 2
        "titolo1_tab2"          => "COMPETENZE DI BASE E RELATIVI LIVELLI RAGGIUNTI (2)",
        "titolo1_tab2_ao"       => "COMPETENCES DE BASE ET NIVEAU DE L&rsquo;&Eacute;L&Egrave;VE&nbsp; (2)",
        "titolo1_tab2_trentino" => "COMPETENZE DI BASE E RELATIVI LIVELLI RAGGIUNTI",
        "titolo2_tab2"          => "COMPETENZE DI CITTADINANZA",
        "livelli"               => "LIVELLI",
        "livelli_ao"               => "NIVEAU",
        "cittadinanza"          => "Le competenze di base relative agli assi culturali sopra richiamati sono state acquisite dallo studente con riferimento alle competenze chiave di cittadinanza di cui all'allegato 2 del regolamento citato in premessa "
                                    . "(1. imparare ad imparare; 2. progettare; 3. comunicare; 4. collaborare e partecipare; 5. agire in modo autonomo e responsabile; 6. risolvere problemi, 7. individuare collegamenti e relazioni; "
                                    . "8. acquisire e interpretare l'informazione).",
        "cittadinanza_ao"       => "Les comp&eacute;tences de base relatives aux diff&eacute;rents domaines culturels ci-dessus ont &eacute;t&eacute; acquises par l&rsquo;&eacute;l&egrave;ve par rapport aux comp&eacute;tences cl&eacute; n&eacute;cessaires &agrave; l&rsquo;&eacute;panouissement de la citoyennet&eacute; vis&eacute;es &agrave; l&rsquo;annexe 2 du r&egrave;glement susmentionn&eacute; (1. Apprendre &agrave; apprendre ; 2. Concevoir un projet ; 3. Communiquer ; 4. Collaborer et participer ; 5. Agir de fa&ccedil;on autonome e responsable ; 6. R&eacute;soudre les probl&egrave;mes ; 7. D&eacute;terminer les connexions et les relations ; 8. Acqu&eacute;rir et interpr&eacute;ter l&rsquo;information).",
        "cittadinanza_trentino" => "\n\n\n\n• Imparare ad imparare \n\n\n\n• Progettare \n\n\n\n• Comunicare \n\n\n\n• Collaborare e partecipare \n\n\n\n• Agire in modo autonomo e responsabile \n\n\n\n• Risolvere problemi \n\n\n\n• Individuare collegamenti e relazioni \n\n\n\n• Acquisire e interpretare l’informazione\n\n\n\n",
        "nota_documento_digitale"   => "(*) il documento è firmato digitalmente ai sensi del D.Lgs. 82/2005 s.m.i. e norme collegate e sostituisce il documento cartaceo e la firma autografa.",

        'legenda_livello' => "(1) Livello",
    ];


    // imposto i testi in base alla scuola scelta
    if ($dati_classe['tipo_indirizzo'] == 6)
    {
        $labels['allegato'] .= "A";
        $labels['titolo2'] .= "DELLA SCUOLA PRIMARIA";
        $labels['valutazione_insegnanti'] = "Visti gli atti d’ufficio relativi alle valutazioni espresse in sede di scrutinio finale dagli insegnanti di classe al termine del quinto anno di corso della scuola primaria;\n";
        $labels['persorso_scolastico'] = "tenuto conto del percorso scolastico quinquennale;\n";
    }
    else if ($dati_classe['tipo_indirizzo'] == 4)
    {
        $labels['allegato'] .= "B";
        $labels['titolo2'] .= "DEL PRIMO CICLO DI ISTRUZIONE";
        $labels['valutazione_insegnanti'] = "Visti gli atti d’ufficio relativi alle valutazioni espresse in sede di scrutinio finale dal Consiglio di classe del terzo anno di corso della scuola secondaria di primo grado;\n";
        $labels['persorso_scolastico'] = "tenuto conto del percorso scolastico ed in riferimento al Profilo dello studente al termine del primo ciclo di istruzione;\n";
    }

    if ($anno_inizio >= 2023) {
        $labels['titolo1_tab1'] = "COMPETENZA CHIAVE";
        $labels['titolo2_tab1'] = "COMPETENZE AL TERMINE DELLA SCUOLA PRIMARIA";
        $labels['titolo3_tab1'] = "LIVELLO*";
        $labels['legenda_livello'] = "(*) Livello";
        $labels['nota_legenda'] = "Per le istituzioni scolastiche paritarie, la certificazione è rilasciata dal Coordinatore delle attività educative e didattiche.";
        $labels['nota_legenda_ss'] = "<sup>1</sup>Per le istituzioni scolastiche paritarie, la certificazione è rilasciata dal Coordinatore delle attività educative e didattiche.
Nel caso di percorsi di IeFP realizzati da Strutture formative accreditate dalle Regioni occorre sostituire ‘Il Dirigente scolastico” con ‘Il Direttore/Legale Rappresentante della Struttura formativa accreditata”.";
        $labels['livello_c'] = "ha raggiunto, al termine della scuola primaria, i livelli di competenza di seguito illustrati.";
        $labels['alunno'] = 'Si certifica che ';
        $labels['provincia'] = "(prov. <b>$provincia_nascita</b>)";



        // French labels
        $labels['titolo1_tab1_fra'] = "COMPÉTENCES CLÉS";
        $labels['titolo2_tab1_fra'] = "COMPÉTENCES À LA FIN DE L'ÉCOLE PRIMAIRE";
        $labels['titolo3_tab1_fra'] = "NIVEAU*";
        $labels['legenda_livello_fra'] = "(*) Niveau";
        $labels['nota_legenda_fra'] = "Pour les établissements scolaires privés, la certification est délivrée par le Coordinateur des activités éducatives et didactiques.";
        $labels['nota_legenda_ss_fra'] = "<sup>1</sup>Pour les établissements scolaires privés, la certification est délivrée par le Coordinateur des activités éducatives et didactiques.
    En cas de parcours de formation professionnelle réalisés par des structures accréditées par les Régions, il convient de remplacer « Le Chef d'établissement » par « Le Directeur/Représentant légal de la structure de formation accréditée ».";
        $labels['livello_c_fra'] = "a atteint, à la fin de l'école primaire, les niveaux de compétence illustrés ci-dessous.";
        $labels['alunno_fra'] = 'Il est certifié que ';
        $labels['provincia_fra'] = "(prov. <b>$provincia_nascita</b>)";

        if ($dati_classe['tipo_indirizzo'] == 4) {
            $labels['livello_c'] = "ha raggiunto, al termine del primo ciclo di istruzione, i livelli di competenza di seguito illustrati.";
            $labels['titolo2_tab1'] = "COMPETENZE AL TERMINE DEL PRIMO CICLO DI ISTRUZIONE";
            // French labels
            $labels['livello_c_fra'] = "a atteint au terme du premier cycle de l’enseignement, les niveaux de compétences illustrés ci- après.";
            $labels['titolo2_tab1_fra'] = "COMPÉTENCES AU TERME DU PREMIER CYCLE D’ÉDUCATION";
            $labels['titolo2_ao'] = "AU TERME DU PREMIER CYCLE DE L’ENSEIGNEMENT";
            $labels['titolo0_indirizzo'] = "SCUOLA SECONDARIA DI PRIMO GRADO";
            $labels['titolo0_indirizzo_ao'] = "ÉCOLE SECONDAIRE DU PREMIER DEGRÉ";
        } elseif ($dati_classe['tipo_indirizzo'] != 6) {
            $labels['ministero_istruzione'] = "Ministero dell'istruzione e del merito";
            $labels['titolo2'] = "IN ASSOLVIMENTO DELL’OBBLIGO DI ISTRUZIONE";
            $labels['livello_c'] = "ha raggiunto, in assolvimento dell’obbligo di istruzione, i livelli di competenza di seguito illustrati.";
            $labels['titolo2_tab1'] = "COMPETENZE IN ASSOLVIMENTO DELL’OBBLIGO DI ISTRUZIONE";
            // French labels
            $labels['ministero_istruzione_fra'] = "Ministère de l'éducation et du mérite";
            $labels['livello_c_fra'] = "a atteint, dans l'accomplissement de l'obligation scolaire, les niveaux de compétence illustrés ci-dessous.";
            $labels['titolo2_tab1_fra'] = "COMPÉTENCES DANS L'ACCOMPLISSEMENT DE L'OBLIGATION SCOLAIRE";
        }
    }

    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
    } else {
        $min_oa = "o";
        $min_eessa = "e";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
    }

    // classe in romano
    $classe_romana = '';
    switch ($dati_classe['classe'])
    {
        case '1':
            $classe_romana = 'I';
            break;
        case '2':
            $classe_romana = 'II';
            break;
        case '3':
            $classe_romana = 'III';
            break;
        case '4':
            $classe_romana = 'IV';
            break;
        case '5':
            $classe_romana = 'V';
            break;
    }
    //}}} </editor-fold>

    if (in_array($dati_classe['tipo_indirizzo'],[4, 6]))
    {
        //{{{ <editor-fold defaultstate="collapsed" desc="----ELEMENTARI e MEDIE----">

        if ($trentino_abilitato != 'SI' 
        && $valle_aosta_abilitata != 'SI' 
        ) {
            //{{{ <editor-fold defaultstate="collapsed" desc="NORMALI">

            //{{{ <editor-fold defaultstate="collapsed" desc="legenda livelli di valutazione">
            $livelli_em = [
                "A - Avanzato"      => "L’alunno/a svolge compiti e risolve problemi complessi, mostrando padronanza nell’uso delle conoscenze e delle abilità; propone e sostiene le proprie opinioni e assume in modo responsabile decisioni consapevoli.",
                "B - Intermedio"    => "L’alunno/a svolge compiti e risolve problemi in situazioni nuove, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.",
                "C - Base"          => "L’alunno/a svolge compiti semplici anche in situazioni nuove, mostrando di possedere conoscenze e abilità fondamentali e di saper applicare basilari regole e procedure apprese.",
                "D - Iniziale"      => "L’alunno/a, se opportunamente guidato/a, svolge compiti semplici in situazioni note."
            ];
            //}}} </editor-fold>

            if ($anno_inizio >= 2023) {
            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
            $pdf->AddPage('P');
            $pdf->SetAutoPageBreak("off", 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->CellFitScale(160, 0, '', 0, 0, "C");

            // Logo
            if ($intestazione == "logo")
            {
//                    inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione, 50, 20);
                inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione);
            }
            elseif ($intestazione == "testo_personalizzato")
            {
                $pdf->Image('immagini_scuola/logo_repubblica.jpg', "C", 25, 25, '', 'JPG', false, 'C', false, 300, 'C', false, false, 0, false, false, false);
                $pdf->SetFont('helvetica', 'I', 17);
                $pdf->setY(55);
                $pdf->CellFitScale(190, 0, $labels['ministero_istruzione'], 0, 1, "C");
                $pdf->ln(15);
                $pdf->SetFont('helvetica', '', 13);
                $pdf->CellFitScale(190, 0, 'Istituzione scolastica', 0, 1, "C");
                if ($testo_riga_1 != '' || $testo_riga_2 != '') {
                    $pdf->ln(5);
                    $pdf->CellFitScale(190, 0, $testo_riga_1, 0, 1, "C");
                    $pdf->ln(3);
                    $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
                } else {
                    $pdf->ln(5);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_scuola'], 0, 1, "C");
                    $pdf->ln(3);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_comuni']." ({$studente['provincia_comuni']})", 0, 1, "C");
                }
            }

            // Titoli
            $pdf->setY(130);
            $pdf->SetFont('helvetica', 'B', 19);
            $pdf->CellFitScale(190, 0, $labels['titolo1'], 0, 1, "C");
            $pdf->CellFitScale(190, 0, $labels['titolo2'], 0, 1, "C");
            $pdf->ln(30);

            // Dati alunno
            //nome cognome
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['alunno'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(160, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
            $pdf->ln(5);
            //data luogo nascita
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['nato'] . " a", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(60, 0, $luogo_nascita, 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            $pdf->writeHTMLCell(50, 0, '', '', $labels['provincia'], 0, 0, false, true, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(10, 0, "il", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(40, 0, date("d/m/Y", $studente['data_nascita']), 0, 1, "L");
            $pdf->ln(5);
            //livelli competenza
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(190, 0, $labels['livello_c'], 0, 0, "L");
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2">
            $pdf->AddPage('P');
            $x_base = $pdf->getX();

            // Intestazione tabella
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->MultiCell(50, 12, $labels['titolo1_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(115, 12, $labels['titolo2_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(25, 12, $labels['titolo3_tab1'], "LTRB", "C", false, 1, "", "", true, 0, false, true, 12, "M");
            $pdf->SetFont('helvetica', '', 9);

            // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
            $competenze_livello = [];
            $competenze_testo = [];
            foreach ($competenze as $competenza)
            {
                if ($competenza['competenze_chiave'] == "")
                {
                    $competenze_testo[] = $competenza;
                }
                else
                {
                    $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
                }
            }

            $cont = 1;

            // Scrivo le competenze con i livelli
            foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
            {
                // scansione lingue
                foreach ($raggruppamento_chiave as $key => $competenza_singola)
                {
                    $desc_tmp = $competenza_singola['descrizione'];
                    if (stripos($desc_tmp, '###id_lingua_') !== false ) {
                        $lingua_varsrc = explode('###', $desc_tmp)[1];
                        if (!empty($competenza_singola['testo'])) {
                            $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", $competenza_singola['testo'], $desc_tmp);
                        } else {
                            $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", '______________________________', $desc_tmp);
                        }
                    }
                }

                $h_competenze = 0;
                foreach ($raggruppamento_chiave as $k => $competenza_singola)
                {
                    $raggruppamento_chiave[$k]['h_riga'] = max($pdf->getStringHeight(115, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                        $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                        5);
                    $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
                }
                $h_chiave = max($h_competenze, $pdf->getStringHeight(50, decode($chiave),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1));

                if ($pdf->GetY()+$h_chiave>$pdf->GetPageHeight()) {
                    $pdf->AddPage('P');
                }
                $pdf->SetFont('helvetica', 'B', 9);
                $pdf->MultiCell(50, $h_chiave, decode($chiave), 1, "L", false, 0, "", "", true, 0, false, true, $h_chiave, "M");
                $pdf->SetFont('helvetica', '', 9);
                $x_rel = $pdf->getX();
                foreach ($raggruppamento_chiave as $competenza_singola)
                {
                    $pdf->MultiCell(115, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                    $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
                    $pdf->setX($x_rel);
                }

                $pdf->setX($x_base);
                $cont++;
            }

            // Scrivo le competenze con il testo
            foreach ($competenze_testo as $competenza_singola)
            {
                $riga_controllo_altezza = decode(str_replace("alunno/a", "alunn$min_oa",$competenza_singola['descrizione'])) . "\n " . decode($competenza_singola['testo']);
                $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;
                $pdf->MultiCell(190, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
                $cont++;
            }
//
//            $consiglio_orientativo = '';
//            //aggiungo nel ciclo il controllo per il consiglio orientativo
//            if($studente['id_consiglio_orientativo'] > 0){
//                global $current_key;
//                $param_consigli = [];
//                $elenco_consigli_orientativi = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
//                $consigli_orientativi_array = [];
//                foreach($elenco_consigli_orientativi as $consiglio){
//                    $consigli_orientativi_array[$consiglio['id_consiglio_orientativo_template']] = $consiglio;
//                }
//                if((strlen($studente['id_consiglio_orientativo']) > 0)){
//                    $elenco_consigli_scelti = explode(',', $studente['id_consiglio_orientativo']);
//                    $consiglio_orientativo_descrizione = "";
//                    foreach($elenco_consigli_scelti as $singolo_consiglio_scelto){
//                        $consiglio_orientativo_descrizione .= $consigli_orientativi_array[$singolo_consiglio_scelto]['descrizione'];
//                    }
//                    $consiglio_orientativo = $consiglio_orientativo_descrizione;
//                }
//            }
//            if ($consiglio_orientativo != '')
//            {
//                $pdf->ln(1);
//                $pdf->writeHTMLCell( 0, 0, '', '', 'Sulla base dei livelli raggiunti dall’alunno nelle competenze considerate, il Consiglio di Classe propone la prosecuzione degli studi nel seguente percorso: '.$consiglio_orientativo, 0, 1);
//                $pdf->ln(1);
//            }
            // Data e firma
            $pdf->ln(0);
//            $pdf->CellFitScale(50, 7, $studente['descrizione_comuni'] . ", " . $data, 0, 0, "C");
            $pdf->CellFitScale(50, 7, "Data $data", 0, 0, "L");
            $pdf->CellFitScale(80, 7, "", 0, 0, "C");

            if ($stampa_nota_documento == 'SI') {
                $pdf->CellFitScale(50, 7, $definizione_dirigente ."(1)(*)", 0, 1, "C");
            }
            else {
                $pdf->CellFitScale(50, 7, $definizione_dirigente." (1)", 0, 1, "C");
            }
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->CellFitScale(50, 0, '', 0, 0, "C");
            $pdf->CellFitScale(80, 0, "", 0, 0, "C");
            $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");

            // Firma omessa
            if ($stampa_firma_omessa == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->CellFitScale(125, 0, "", 0, 0, "C");
                $pdf->CellFitScale(55, 0, "Firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
            }
            if ($stampa_nota_documento == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
            }
            // Legenda livelli di valutazione
            if ($pdf->GetY()>245) {
                $pdf->AddPage('P');
            } else {
                $pdf->setY(246);
            }
            $pdf->SetFont('helvetica', 'B', 8);
            $pdf->CellFitScale(30, 0, $labels['legenda_livello'], "B", 0, "L");
            $pdf->CellFitScale(5, 0, "", "B", 0, "C");
            $pdf->CellFitScale(155, 0, "Indicatori esplicativi", "B", 1, "L");
            foreach ($livelli_em as $valore => $spiegazione)
            {
                $riga = $pdf->MultiCellNbLines(155, $spiegazione) * 4.7;
                $pdf->SetFont('helvetica', 'IB', 8);
                $pdf->MultiCell(30, $riga, $valore, 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->SetFont('helvetica', '', 8);
                $pdf->MultiCell(5, $riga, '', 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(155, $riga, $spiegazione, 0, "L", false, 1, "", "", true, 0, false, true, $riga, "M");
            }
            $pdf->CellFitScale(0, 2, "", "B", 1, "C");
            $pdf->ln(1);
            $pdf->writeHTMLCell( 0, 0, '', '', '<sup>1</sup> Per le istituzioni scolastiche paritarie, la certificazione è rilasciata dal Coordinatore delle attività educative e didattiche.', 0, 0);
            //}}} </editor-fold>
            }
            else {
            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
            $pdf->AddPage('P');
            $pdf->SetAutoPageBreak("off", 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->CellFitScale(160, 0, '', 0, 0, "C");
            //$pdf->CellFitScale(30, 10, $labels['allegato'], 1, 1, "C");

            // Logo
            if ($intestazione == "logo")
            {
//                    inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione, 50, 20);
                inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione);
            }
            elseif ($intestazione == "testo_personalizzato")
            {
                $pdf->Image('immagini_scuola/logo_repubblica.jpg', "C", 17, 16, '', 'JPG', false, 'C', false, 300, 'C', false, false, 0, false, false, false);
                $pdf->SetFont('helvetica', 'B', 14);
                $pdf->setY(45);
                $pdf->CellFitScale(190, 0, $testo_riga_1, 0, 1, "C");
                $pdf->ln(3);
                $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
            }

            // Titoli
            $pdf->setY(75);
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->CellFitScale(190, 0, $labels['titolo1'], 0, 1, "C");
            $pdf->CellFitScale(190, 0, $labels['titolo2'], 0, 1, "C");
            $pdf->ln(15);
            $pdf->SetFont('helvetica', 'B', 14);
            $pdf->CellFitScale(190, 0, $labels['dirigente'], 0, 1, "C");
            $pdf->ln(15);

            // Decreti
            $pdf->SetFont('helvetica', '', 12);
            $pdf->MultiCell(190, 0, $labels['decreto1'] . "\n" . $labels['decreto2'] . "\n" . $labels['valutazione_insegnanti'] . "\n" . $labels['persorso_scolastico'], 0, 'L', false, 0);
            $y_rel = $pdf->getY();
            $pdf->setY($y_rel + 65);
            $pdf->SetFont('helvetica', 'B', 14);
            $pdf->CellFitScale(190, 0, $labels['certifica'], 0, 1, "C");
            $pdf->ln(14);

            // Dati alunno
            //nome cognome
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['alunno'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(160, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
            $pdf->ln(3);
            //data luogo nascita
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['nato'] . " a", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(110, 0, $studente_nascita, 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(10, 0, "il", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(40, 0, date("d/m/Y", $studente['data_nascita']), 0, 1, "L");
            $pdf->ln(3);
            // anno scolastico classe sezione
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(65, 0, $labels['anno_scolastico'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(35, 0, $anno_scolastico, 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(20, 0, "la classe", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(10, 0, $classe_romana, 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(10, 0, "sez.", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(20, 0, $studente['sezione'], 0, 1, "L");
            $pdf->ln(3);
            //orario settimanale
            if ($ore_settimanali == "")
            {
                $pdf->SetFont('helvetica', '', 12);
                $pdf->CellFitScale(30, 0, $labels['orario1'], 0, 0, "L");
                $pdf->SetFont('helvetica', 'B', 12);
                $pdf->CellFitScale(150, 0, $orario_settimanale['descrizione'], 0, 1, "L");
            }
            else
            {
                $pdf->SetFont('helvetica', '', 12);
                $pdf->CellFitScale(55, 0, $labels['orario2'], 0, 0, "L");
                $pdf->SetFont('helvetica', 'B', 12);
                $pdf->CellFitScale(10, 0, $ore_settimanali, 0, 0, "L");
                $pdf->SetFont('helvetica', '', 12);
                $pdf->CellFitScale(40, 0, "ore", 0, 1, "L");
            }
            $pdf->ln(3);
            //livelli competenza
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(190, 0, $labels['livello_c'], 0, 0, "L");
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2">
            $pdf->AddPage('P');
            $x_base = $pdf->getX();

            // Intestazione tabella
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->MultiCell(10, 15, "", "LTB", "C", false, 0, "", "", true, 0, false, true, 15, "M");
            $pdf->MultiCell(55, 15, $labels['titolo1_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 15, "M");
            $pdf->MultiCell(110, 15, $labels['titolo2_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 15, "M");
            $pdf->MultiCell(15, 15, $labels['titolo3_tab1'], "LTRB", "C", false, 1, "", "", true, 0, false, true, 15, "M");
            $pdf->SetFont('helvetica', '', 9);

            // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
            $competenze_livello = [];
            $competenze_testo = [];
            foreach ($competenze as $competenza)
            {
                if ($competenza['competenze_chiave'] == "")
                {
                    $competenze_testo[] = $competenza;
                }
                else
                {
                    $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
                }
            }

            $cont = 1;

            // Scrivo le competenze con i livelli
            foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
            {
                $array_riga_controllo = [];
                foreach ($raggruppamento_chiave as $competenza_singola)
                {
                    $array_riga_controllo[] = $competenza_singola['descrizione'];
                }
                $riga_controllo_altezza = implode("\n", $array_riga_controllo);
                $riga_controllo_altezza = decode($riga_controllo_altezza);

                $riga_chiave = $pdf->MultiCellNbLines(55, decode($chiave)) * 4.5;
                $riga_descrizione = $pdf->MultiCellNbLines(110, $riga_controllo_altezza) * 4.5;
                if ($riga_descrizione > $riga_chiave)
                {
                    $riga = $riga_descrizione;
                    $riga_chiave_autilizzata = false;
                }
                else
                {
                    $riga = $riga_chiave;
                    $riga_chiave_autilizzata = true;
                }

                $pdf->MultiCell(10, $riga, $cont, "LB", "C", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(55, $riga, decode($chiave), "LB", "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $x_rel = $pdf->getX();
                foreach ($raggruppamento_chiave as $competenza_singola)
                {
                    if ($riga_chiave_autilizzata)
                    {
                        $h_riga_competenza_singola = $riga_chiave;
                    }
                    else
                    {
                        $h_riga_competenza_singola = $pdf->MultiCellNbLines(110, decode($competenza_singola['descrizione'])) * 4.5;
                    }
                    $pdf->MultiCell(110, $h_riga_competenza_singola, decode($competenza_singola['descrizione']), "LB", "L", false, 0, "", "", true, 0, false, true, $h_riga_competenza_singola, "M");
                    $pdf->MultiCell(15, $h_riga_competenza_singola, $competenza_singola['valore'], "LRB", "C", false, 1, "", "", true, 0, false, true, $h_riga_competenza_singola, "M");
                    $pdf->setX($x_rel);
                }
                $pdf->setX($x_base);


                $cont++;
            }

            // Scrivo le competenze con il testo
            foreach ($competenze_testo as $competenza_singola)
            {
                $riga_controllo_altezza = decode($competenza_singola['descrizione']) . " " . decode($competenza_singola['testo']);
                $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;

                $pdf->MultiCell(10, $riga, $cont, "LB", "C", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(180, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
                $cont++;
            }

            // Appunto
            $pdf->setY($pdf->getY() - 3);
            $pdf->SetFont('helvetica', 'I', 9);
            $pdf->CellFitScale(65, 10, "* Sense of initiative and entrepreneurship", 0, 0, "R");
            $pdf->SetFont('helvetica', '', 9);
            $pdf->CellFitScale(115, 10, "nella Raccomandazione europea e del Consiglio del 18 dicembre 2006", 0, 1, "L");

            // san giuseppe consiglio orientativo
            $voti=estrai_voti_pagellina_studente_multi_classe($id_classe_certificazione, 29, $studente['id_studente']);
            $consiglio_orientativo = '';
            foreach ($voti as $voto)
            {
                if ($voto['tipo_materia'] == 'CONDOTTA')
                {
                    foreach ($voto['campi_liberi'] as $campo_libero)
                    {


                        if (
                                (strpos(strtoupper($campo_libero['nome']), 'PROSECUZIONE DEGLI STUDI') !== false)
                            )
                        {
                            $consiglio_orientativo .= estrai_valore_campo_libero_selezionato($campo_libero);
                        }
                    }
                }
            }
            if ($consiglio_orientativo != '')
            {
                $pdf->ln(1);
                $pdf->writeHTMLCell( 0, 0, '', '', 'Sulla base dei livelli raggiunti dall’alunno nelle competenze considerate, il Consiglio di Classe propone la prosecuzione degli studi nel seguente percorso: '.$consiglio_orientativo, 0, 1);
                $pdf->ln(2);
            }

            // Data e firma
            $pdf->ln(0);
            $pdf->CellFitScale(50, 7, "Luogo e data", 0, 0, "C");
            $pdf->CellFitScale(80, 7, "", 0, 0, "C");

            if ($stampa_nota_documento == 'SI') {
                $pdf->CellFitScale(50, 7, $definizione_dirigente ."(*)", 0, 1, "C");
            }
            else {
                $pdf->CellFitScale(50, 7, $definizione_dirigente, 0, 1, "C");
            }
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->CellFitScale(50, 0, $studente['descrizione_comuni'] . ", " . $data, 0, 0, "C");
            $pdf->CellFitScale(80, 0, "", 0, 0, "C");
            $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");

            // Firma omessa
            if ($stampa_firma_omessa == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->CellFitScale(125, 0, "", 0, 0, "C");
                $pdf->CellFitScale(55, 0, "Firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
            }

            if ($stampa_nota_documento == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->setY(225);
                $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
            }

            // Legenda livelli di valutazione
            if ($pdf->GetY()>234) {
                $pdf->AddPage('P');
            } else {
                $pdf->setY(235);
            }
            $pdf->CellFitScale(30, 0, $labels['legenda_livello'], "B", 0, "L");
            $pdf->CellFitScale(5, 0, "", "B", 0, "C");
            $pdf->CellFitScale(155, 0, "Indicatori esplicativi", "B", 1, "L");
            foreach ($livelli_em as $valore => $spiegazione)
            {
                $riga = $pdf->MultiCellNbLines(155, $spiegazione) * 4.7;
                $pdf->SetFont('helvetica', 'B', 9);
                $pdf->MultiCell(30, $riga, $valore, 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->SetFont('helvetica', '', 9);
                $pdf->MultiCell(5, $riga, '', 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(155, $riga, $spiegazione, 0, "L", false, 1, "", "", true, 0, false, true, $riga, "M");
            }
            //}}} </editor-fold>
            }

            //}}} </editor-fold>
        }
        elseif ($valle_aosta_abilitata == 'SI'
        ) {
            //{{{ <editor-fold defaultstate="collapsed" desc="valle d'aosta">
            if (filter_input(INPUT_SERVER, 'SERVER_NAME') == 'salesianichatillon.registroelettronico.com') {
                $labels['titolo0_indirizzo'] .= " “DON BOSCO” -  CHÂTILLON";
                $labels['titolo0_indirizzo_ao'] .= " “DON BOSCO” -  CHÂTILLON";
            }
            
            $livelli_em = [
                "A - Avanzato"      => "L’alunno/a svolge compiti e risolve problemi complessi, mostrando padronanza nell’uso delle conoscenze e delle abilità; propone e sostiene le proprie opinioni e assume in modo responsabile decisioni consapevoli.",
                "B - Intermedio"    => "L’alunno/a svolge compiti e risolve problemi in situazioni nuove, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.",
                "C - Base"          => "L’alunno/a svolge compiti semplici anche in situazioni nuove, mostrando di possedere conoscenze e abilità fondamentali e di saper applicare basilari regole e procedure apprese.",
                "D - Iniziale"      => "L’alunno/a, se opportunamente guidato/a, svolge compiti semplici in situazioni note."
            ];
            $livelli_em_fra = [
                "A - Niveau avancé"      => "L’élève effectue des tâches et résout des problèmes complexes et démontre qu’il sait utiliser ses connaissances et ses savoir-faire. Il exprime et défend ses propres opinions et prend des décisions de façon responsable.",
                "B - Niveau intermédiaire" => "L’élève effectue des tâches, il résout des problèmes dans un contexte nouveau, fait des choix responsables et démontre qu’il sait utiliser ses connaissances et ses savoir- faire.",
                "C - Niveau de base"      => "L’élève effectue des tâches simples même dans un contexte nouveau, démontre qu’il possède les connaissances fondamentales et maîtrise les habiletés de base et qu’il sait appliquer les règles et les procédures apprises.",
                "D - Niveau de départ"    => "L’élève, guidé par l’enseignant, effectue des tâches simples dans un contexte connu."
            ];

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
            $pdf->AddPage('P');
            $pdf->SetAutoPageBreak("off", 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->CellFitScale(160, 0, '', 0, 0, "C");

            // Logo
            if ($intestazione == "logo")
            {
                inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione);
            }
            elseif ($intestazione == "testo_personalizzato")
            {
                // $pdf->Image('immagini_scuola/logo_repubblica.jpg', "C", 25, 25, '', 'JPG', false, 'C', false, 300, 'C', false, false, 0, false, false, false);
                $pdf->SetFont('helvetica', 'I', 17);
                $pdf->setY(50);
                // $pdf->CellFitScale(190, 0, $labels['ministero_istruzione'], 0, 1, "C");
                // $pdf->ln(15);
                $pdf->SetFont('helvetica', '', 13);
                // $pdf->CellFitScale(190, 0, 'Istituzione scolastica', 0, 1, "C");
                if ($testo_riga_1 != '' || $testo_riga_2 != '') {
                    $pdf->ln(2);
                    $pdf->CellFitScale(190, 0, $testo_riga_1, 0, 1, "C");
                    $pdf->ln(2);
                    $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
                } else {
                    $pdf->ln(2);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_scuola'], 0, 1, "C");
                    $pdf->ln(2);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_comuni']." ({$studente['provincia_comuni']})", 0, 1, "C");
                }
            }

            // indirizzo
            $pdf->setY(70);
            $pdf->SetFont('helvetica', 'B', 19);
            $pdf->CellFitScale(190, 0, $labels['titolo0_indirizzo'], 0, 1, "C");
            // Titoli
            $pdf->setY(130);
            $pdf->SetFont('helvetica', 'B', 19);
            $pdf->CellFitScale(190, 0, $labels['titolo1'], 0, 1, "C");
            $pdf->CellFitScale(190, 0, $labels['titolo2'], 0, 1, "C");
            $pdf->ln(30);

            // Dati alunno
            //nome cognome
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['alunno'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(160, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
            $pdf->ln(5);
            //data luogo nascita
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['nato'] . " a", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(60, 0, $luogo_nascita, 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            $pdf->writeHTMLCell(50, 0, '', '', $labels['provincia'], 0, 0, false, true, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(10, 0, "il", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(40, 0, date("d/m/Y", $studente['data_nascita']).',', 0, 1, "L");
            $pdf->ln(5);
            //livelli competenza
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(190, 0, $labels['livello_c'], 0, 0, "L");
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2">
            $pdf->AddPage('P');
            $x_base = $pdf->getX();

            // Intestazione tabella
            $pdf->SetFont('helvetica', 'B', 8);
            $pdf->MultiCell(40, 12, $labels['titolo1_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(125, 12, $labels['titolo2_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(25, 12, $labels['titolo3_tab1'], "LTRB", "C", false, 1, "", "", true, 0, false, true, 12, "M");
            $pdf->SetFont('helvetica', '', 8);

            // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
            $competenze_livello = [];
            $competenze_testo = [];
            foreach ($competenze as $competenza)
            {
                if ($competenza['competenze_chiave'] == "")
                {
                    $competenze_testo[] = $competenza;
                }
                else
                {
                    if ($competenza['competenze_chiave'] == 'AO - Competenza alfabetica funzionale - Lingua italiana' ||
                        $competenza['competenze_chiave'] == 'AO - Competenza alfabetica funzionale - Lingua francese')
                    {
                        $chiave = 'Competenza alfabetica funzionale';
                    } else {
                        $chiave = $competenza['competenze_chiave'];
                    }

                    if ($competenza['competenze_chiave'] == 'Competenza multilinguistica') {
                        $competenza['descrizione'] = str_replace("Riconoscere le pi&ugrave; evidenti somiglianze e differenze tra le lingue e le culture oggetto di studio", '', $competenza['descrizione']);

                        if (strpos(trim($competenza['descrizione']), 'Utilizzare una seconda lingua comunitaria*') === 0) {
                            $competenza['descrizione'] = "Utilizzare una seconda lingua comunitaria* a livello elementare in forma orale e scritta (comprensione orale e scritta, produzione scritta e produzione/interazione orale) in semplici situazioni di vita quotidiana relative ad ambiti di immediata rilevanza e su argomenti familiari e abituali, compresi contenuti di studio di altre discipline (Livello A2 del Quadro Comune Europeo di Riferimento per le lingue)\n\n* limitatamente alle scuole site nei Comuni della valle del Lys, individuati dalla legge regionale 19 agosto 1998, n. 47";
                        } 
                    }
                    $competenze_livello[$chiave][$competenza['id_competenza_scolastica']] = $competenza;
                }
            }
            $cont = 1;

            // Scrivo le competenze con i livelli
            foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
            {
                if ($chiave == "Competenza alfabetica funzionale") {
                    $h_competenze = 0; $h_desc = 0;
                    $desc_comp =  '';
                    foreach ($raggruppamento_chiave as $k => $competenza_singola)
                    {
                        $desc_comp = decode($competenza_singola['descrizione']);
                        $h_desc = $pdf->getStringHeight(140, $desc_comp,  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 );

                        $last_element = '';
                        $parts = explode(' - ', $competenza_singola['competenze_chiave']);
                        if (!empty($parts)) {
                            $last_element = end($parts);
                        }

                        $raggruppamento_chiave[$k]['h_riga'] = max(
                            $pdf->getStringHeight(125, decode($last_element),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            5);
                        $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
                    }
                    $h_competenze = $h_competenze + $h_desc;
                    
                    $pdf->SetFont('helvetica', 'B', 8);
                    $pdf->MultiCell(40, $h_competenze, decode($chiave), 1, "L", false, 0, "", "", true, 0, false, true, $h_competenze, "M");
                    $pdf->SetFont('helvetica', '', 8);
                    $x_rel = $pdf->GetX();
                    $pdf->MultiCell(150, $h_desc, $desc_comp, 1, "L", false, 1, "", "", true, 1, false, true, $h_desc, "M");
                    $pdf->setX($x_rel);
                    foreach ($raggruppamento_chiave as $competenza_singola)
                    {
                        $last_element = '';
                        $parts = explode(' - ', $competenza_singola['competenze_chiave']);
                        if (!empty($parts)) {
                            $last_element = end($parts);
                        }
                        if (!isset($competenza_singola['valore'])) {
                            $competenza_singola['valore'] = '';
                        }
                        $pdf->MultiCell(125, $competenza_singola['h_riga'], decode($last_element), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->setX($x_rel);
                    }

                    $pdf->setX($x_base);
                    $cont++;

                } else {
                    // scansione lingue
                    foreach ($raggruppamento_chiave as $key => $competenza_singola)
                    {
                        $desc_tmp = $competenza_singola['descrizione'];
                        if (stripos($desc_tmp, '###id_lingua_') !== false ) {
                            $lingua_varsrc = explode('###', $desc_tmp)[1];
                            if (!empty($competenza_singola['testo'])) {
                                $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", $competenza_singola['testo'], $desc_tmp);
                            } else {
                                $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", '______________________________', $desc_tmp);
                            }
                        }
                    }

                    $h_competenze = 0;
                    foreach ($raggruppamento_chiave as $k => $competenza_singola)
                    {
                        $raggruppamento_chiave[$k]['h_riga'] = max($pdf->getStringHeight(115, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            5);
                        $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
                    }
                    $h_chiave = max($h_competenze, $pdf->getStringHeight(50, decode($chiave),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1));

                    if ($pdf->GetY()+$h_chiave>$pdf->GetPageHeight()) {
                        $pdf->AddPage('P');
                    }
                    $pdf->SetFont('helvetica', 'B', 8);
                    $pdf->MultiCell(40, $h_chiave, decode($chiave), 1, "L", false, 0, "", "", true, 0, false, true, $h_chiave, "M");
                    $pdf->SetFont('helvetica', '', 8);
                    $x_rel = $pdf->getX();
                    foreach ($raggruppamento_chiave as $competenza_singola)
                    {
                        $pdf->MultiCell(125, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->setX($x_rel);
                    }

                    $pdf->setX($x_base);
                    $cont++;
                }
            }

            // Scrivo le competenze con il testo
            foreach ($competenze_testo as $competenza_singola)
            {
                $riga_controllo_altezza = decode(str_replace("alunno/a", "alunn$min_oa",$competenza_singola['descrizione'])) . "\n " . decode($competenza_singola['testo']);
                $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;
                $pdf->MultiCell(190, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
                $cont++;
            }
            // Data e firma
            $pdf->ln(0);
            $pdf->CellFitScale(50, 7, "Data $data", 0, 0, "L");
            $pdf->CellFitScale(80, 7, "", 0, 0, "C");

            if ($stampa_nota_documento == 'SI') {
                $pdf->CellFitScale(50, 7, $definizione_dirigente ."(1)(*)", 0, 1, "C");
            }
            else {
                $pdf->CellFitScale(50, 7, $definizione_dirigente." (1)", 0, 1, "C");
            }
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->CellFitScale(50, 0, '', 0, 0, "C");
            $pdf->CellFitScale(80, 0, "", 0, 0, "C");
            $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");

            // Firma omessa
            if ($stampa_firma_omessa == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->CellFitScale(125, 0, "", 0, 0, "C");
                $pdf->CellFitScale(55, 0, "Firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
            }
            if ($stampa_nota_documento == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
            }
            // Legenda livelli di valutazione
            if ($pdf->GetY()>245) {
                $pdf->AddPage('P');
            } else {
                $pdf->setY(246);
            }
            $pdf->SetFont('helvetica', 'B', 8);
            $pdf->CellFitScale(30, 0, $labels['legenda_livello'], "B", 0, "L");
            $pdf->CellFitScale(5, 0, "", "B", 0, "C");
            $pdf->CellFitScale(155, 0, "Indicatori esplicativi", "B", 1, "L");
            foreach ($livelli_em as $valore => $spiegazione)
            {
                $riga = $pdf->MultiCellNbLines(155, $spiegazione) * 4.7;
                $pdf->SetFont('helvetica', 'IB', 8);
                $pdf->MultiCell(30, $riga, $valore, 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->SetFont('helvetica', '', 8);
                $pdf->MultiCell(5, $riga, '', 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(155, $riga, $spiegazione, 0, "L", false, 1, "", "", true, 0, false, true, $riga, "M");
            }
            $pdf->CellFitScale(0, 2, "", "B", 1, "C");
            $pdf->ln(1);
            $pdf->writeHTMLCell( 0, 0, '', '', '<sup>1</sup> Per le istituzioni scolastiche paritarie, la certificazione è rilasciata dal Coordinatore delle attività educative e didattiche.', 0, 0);
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagine francesi">
            // Pagina 1 - Francese
            $pdf->AddPage('P');
            $pdf->SetAutoPageBreak("off", 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->CellFitScale(160, 0, '', 0, 0, "C");

            // Logo
            if ($intestazione == "logo")
            {
                inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione);
            }
            elseif ($intestazione == "testo_personalizzato")
            {
                $pdf->Image('immagini_scuola/logo_repubblica.jpg', "C", 25, 25, '', 'JPG', false, 'C', false, 300, 'C', false, false, 0, false, false, false);
                $pdf->SetFont('helvetica', 'I', 17);
                $pdf->setY(55);
                $pdf->CellFitScale(190, 0, $labels['ministero_istruzione_fra'], 0, 1, "C");
                $pdf->ln(15);
                $pdf->SetFont('helvetica', '', 13);
                $pdf->CellFitScale(190, 0, 'Établissement scolaire', 0, 1, "C");
                if ($testo_riga_1 != '' || $testo_riga_2 != '') {
                    $pdf->ln(5);
                    $pdf->CellFitScale(190, 0, $testo_riga_1, 0, 1, "C");
                    $pdf->ln(3);
                    $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
                } else {
                    $pdf->ln(5);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_scuola'], 0, 1, "C");
                    $pdf->ln(3);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_comuni']." ({$studente['provincia_comuni']})", 0, 1, "C");
                }
            }

            // indirizzo
            $pdf->setY(70);
            $pdf->SetFont('helvetica', 'B', 19);
            $pdf->CellFitScale(190, 0, $labels['titolo0_indirizzo_ao'], 0, 1, "C");

            // Titoli
            $pdf->setY(130);
            $pdf->SetFont('helvetica', 'B', 19);
            $pdf->CellFitScale(190, 0, $labels['titolo1_ao'], 0, 1, "C");
            $pdf->CellFitScale(190, 0, $labels['titolo2_ao'], 0, 1, "C");
            $pdf->ln(30);

            // Dati alunno
            //nome cognome
            $pdf->SetFont('helvetica', '', 12); 
            $pdf->CellFitScale(30, 0, $labels['alunno_fra'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(160, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
            $pdf->ln(5);
            //data luogo nascita
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, "né/née ", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(60, 0, $luogo_nascita, 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            $pdf->writeHTMLCell(50, 0, '', '', $labels['provincia_fra'], 0, 0, false, true, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(10, 0, "le", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(40, 0, date("d/m/Y", $studente['data_nascita']).',', 0, 1, "L");
            $pdf->ln(5);
            //livelli competenza
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(190, 0, $labels['livello_c_fra'], 0, 0, "L");
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2 FRA">
            $pdf->AddPage('P');
            $x_base = $pdf->getX();

            // Intestazione tabella
            $pdf->SetFont('helvetica', 'B', 8);
            $pdf->MultiCell(40, 12, $labels['titolo1_tab1_fra'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(125, 12, $labels['titolo2_tab1_fra'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(25, 12, $labels['titolo3_tab1_fra'], "LTRB", "C", false, 1, "", "", true, 0, false, true, 12, "M");
            $pdf->SetFont('helvetica', '', 8);

            // @@@@@@ francese competenze
            $labels_fra = [
                "Competenza alfabetica funzionale"  => [
                    "descrizione" => "Comp&eacute;tences alphab&eacute;tiques",
                    "testo"       => "L&rsquo;&eacute;l&egrave;ve ma&icirc;trise les langues de l&rsquo;&eacute;cole : il comprend des &eacute;nonc&eacute;s, raconte ses exp&eacute;riences et sait choisir un registre de langue appropri&eacute; &agrave; chaque situation."
                ],
                "Competenza multilinguistica" => [
                    "descrizione" => "Comp&eacute;tences multilingues",
                ],
                // Compétences multilingues
                "Competenza multilinguistica eng" => [
                    "descrizione" => "Comp&eacute;tences multilingues (anglais)",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve utilise la langue anglaise &agrave; un niveau &eacute;l&eacute;mentaire, &agrave; l&rsquo;oral comme &agrave; l&rsquo;&eacute;crit (compr&eacute;hension orale et &eacute;crite, interaction ou production orale et &eacute;crite) dans des situations simples de la vie quotidienne et des domaines relatifs &agrave; ses besoins imm&eacute;diats ou &agrave; des sujets familiers et habituels, y compris des sujets &eacute;tudi&eacute;s dans d&rsquo;autres disciplines (Niveau A2 du Cadre Europ&eacute;en  Commun de R&eacute;f&eacute;rence pour les langues)."
                ],
                "Competenza multilinguistica oth" => [
                    "descrizione" => "Comp&eacute;tences multilingues (allemand)*",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve utilise une deuxi&egrave;me langue communautaire* &agrave; un niveau &eacute;l&eacute;mentaire, &agrave; l&rsquo;oral comme &agrave; l&rsquo;&eacute;crit (compr&eacute;hension orale et &eacute;crite, interaction ou production orale et &eacute;crite) dans des situations simples de la vie quotidienne et des domaines relatifs &agrave; ses besoins imm&eacute;diats ou &agrave; des sujets familiers et habituels, y compris des sujets &eacute;tudi&eacute;s dans d&rsquo;autres disciplines (Niveau A2 du Cadre Europ&eacute;en  Commun de R&eacute;f&eacute;rence pour les langues).\n\n* uniquement pour les &eacute;coles situ&eacute;es dans les communes de la vall&eacute;e du Lys identifi&eacute;es par la loi r&eacute;gionale n&deg; 47 du 19 ao&ucirc;t 1998."
                ],
                // Compétences mathématiques, scientifiques et technologiques
                "Competenza matematica e competenza in scienze, tecnologie e ingegneria" => [
                    "descrizione" => "Comp&eacute;tences math&eacute;matiques, scientifiques et technologiques",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve utilise ses connaissances math&eacute;matiques, scientifiques et technologiques pour analyser des donn&eacute;es et des faits de la r&eacute;alit&eacute; et v&eacute;rifier la justesse des analyses quantitatives propos&eacute;es par  les autres. Il utilise la pens&eacute;e logique et scientifique pour r&eacute;gler des probl&egrave;mes et des situations sur la base d&rsquo;&eacute;l&eacute;ments certains.\nIl a conscience des limites des affirmations relatives &agrave; des situations complexes."
                ],
                // Compétences numériques
                "Competenza digitale" => [
                    "descrizione" => "Comp&eacute;tences num&eacute;riques",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve utilise de fa&ccedil;on responsable les technologies num&eacute;riques pour rechercher, produire et &eacute;laborer des donn&eacute;es et des informations, pour interagir avec les autres, d&eacute;velopper sa cr&eacute;ativit&eacute; et trouver la solution &agrave; des probl&egrave;mes."
                ],
                // Compétences personnelles et sociales - Apprendre à apprendre
                "Competenza personale, sociale e capacit&agrave; di imparare a imparare" => [
                    "descrizione" => "Comp&eacute;tences personnelles et sociales - Apprendre &agrave; apprendre",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve prend soin de lui-m&ecirc;me, des autres et de l&rsquo;environnement et comprend que cela constitue la base d&rsquo;un mode de vie sain et correct.\nIl utilise connaissances et notions de base de fa&ccedil;on pratique pour rechercher et organiser de nouvelles informations.\nIl acc&egrave;de aux nouveaux apprentissages de fa&ccedil;on autonome et m&egrave;ne &agrave; bien le travail qu&rsquo;il commence, seul ou avec les autres."
                ],
                // Compétences civiques
                "Competenza in materia di cittadinanza" => [
                    "descrizione" => "Comp&eacute;tences civiques",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve comprend la n&eacute;cessit&eacute; d&rsquo;une cohabitation civile, pacifique et solidaire pour construire le bien commun et agir de fa&ccedil;on coh&eacute;rente. Il exprime ses propres opinions et est sensible &agrave; la notion de respect de soi et d&rsquo;autrui.\nIl participe aux diff&eacute;rentes fonctions publiques &agrave; sa port&eacute;e pour appliquer les principes constitutionnels.\nIl reconna&icirc;t et appr&eacute;cie les diff&eacute;rentes identit&eacute;s, les traditions culturelles et religieuses, dans une optique de dialogue et de respect r&eacute;ciproque.\nIl adopte des attitudes et des comportements respectueux de l&rsquo;environnement, des biens communs, de la durabilit&eacute; environnementale, &eacute;conomique et sociale, conform&eacute;ment &agrave; l&rsquo;Agenda 2030 pour le d&eacute;veloppement durable."
                ],
                // Esprit d’initiative et d’entreprise
                "Competenza imprenditoriale" => [
                    "descrizione" => "Esprit d&rsquo;initiative et d&rsquo;entreprise",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve fait preuve d&rsquo;esprit d&rsquo;initiative, il produit des id&eacute;es et des projets cr&eacute;atifs. Il prend ses responsabilit&eacute;s, sait demander de l&rsquo;aide ou aider les autres, le cas &eacute;ch&eacute;ant.\nIl est capable d&rsquo;auto-critique et sait faire face aux nouveaut&eacute;s et aux impr&eacute;vus. Il d&eacute;cide de ses choix de fa&ccedil;on responsable."
                ],
                // Sensibilité et expression culturelles
                "Competenza in materia di consapevolezza ed espressione culturali" => [
                    "descrizione" => "Sensibilit&eacute; et expression culturelles",
                    "testo" => "L&rsquo;&eacute;l&egrave;ve sait s&rsquo;orienter dans l&rsquo;espace ou dans le temps et interpr&eacute;ter les syst&egrave;mes symboliques et culturels de la soci&eacute;t&eacute; ; il exprime sa curiosit&eacute; et sa recherche de sens.\nEn relation avec ses propres capacit&eacute;s et &agrave; son propre talent, il s&rsquo;exprime dans les domaines qui lui correspondent le mieux : activit&eacute;s de motricit&eacute;, artistiques ou musicales."
                ],
            ];
// Langue italienne
// Langue française

// L’élève a, en outre, fait preuve de compétences particulières lors d’activités scolaires et/ou extra-scolaires telles que :

            // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
            $competenze_livello = [];
            $competenze_testo = [];
            foreach ($competenze as $competenza)
            {
                if ($competenza['competenze_chiave'] == "")
                {
                    $competenza['descrizione'] = "L&rsquo;&eacute;l&egrave;ve a, en outre, fait preuve de comp&eacute;tences particuli&egrave;res lors d&rsquo;activit&eacute;s scolaires et/ou extra-scolaires telles que&nbsp;:";
                    $competenze_testo[] = $competenza;
                }
                else
                {
                    if ($competenza['competenze_chiave'] == 'AO - Competenza alfabetica funzionale - Lingua italiana' ||
                        $competenza['competenze_chiave'] == 'AO - Competenza alfabetica funzionale - Lingua francese')
                    {
                        $chiave = 'Competenza alfabetica funzionale';
                    } else {
                        $chiave = $competenza['competenze_chiave'];
                    }

                    if ($competenza['competenze_chiave'] == 'Competenza multilinguistica') {
                        $competenza['descrizione'] = str_replace("Riconoscere le pi&ugrave; evidenti somiglianze e differenze tra le lingue e le culture oggetto di studio", '', $competenza['descrizione']);

                        if (strpos(trim($competenza['descrizione']), 'Utilizzare una seconda lingua comunitaria*') === 0) {
                            $competenza['descrizione'] = $labels_fra["Competenza multilinguistica oth"]['testo'];
                        }  else {
                            $competenza['descrizione'] = $labels_fra["Competenza multilinguistica eng"]['testo'];
                        }
                    }
                    
                    $comp_chiave_ao = $labels_fra[$chiave]['descrizione'] ?  $labels_fra[$chiave]['descrizione'] : $chiave;
                    $competenza['descrizione'] = $labels_fra[$chiave]['testo'] ? $labels_fra[$chiave]['testo'] : $competenza['descrizione'];
                    $competenze_livello[$comp_chiave_ao][$competenza['id_competenza_scolastica']] = $competenza;
                }
            }
            $cont = 1;
            // ppre($competenze_livello);

            // Scrivo le competenze con i livelli
            foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
            {
                if ($chiave == "Comp&eacute;tences alphab&eacute;tiques") {
                    $h_competenze = 0; $h_desc = 0;
                    $desc_comp =  '';
                    foreach ($raggruppamento_chiave as $k => $competenza_singola)
                    {
                        $desc_comp = decode($competenza_singola['descrizione']);
                        $h_desc = $pdf->getStringHeight(140, $desc_comp,  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 );

                        $last_element = '';
                        $parts = explode(' - ', $competenza_singola['competenze_chiave']);
                        if (!empty($parts)) {
                            $last_element = end($parts);
                        }

                        $raggruppamento_chiave[$k]['h_riga'] = max(
                            $pdf->getStringHeight(125, decode($last_element),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            5);
                        $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
                    }
                    $h_competenze = $h_competenze + $h_desc;
                    
                    $pdf->SetFont('helvetica', 'B', 8);
                    $pdf->MultiCell(40, $h_competenze, decode($chiave), 1, "L", false, 0, "", "", true, 0, false, true, $h_competenze, "M");
                    $pdf->SetFont('helvetica', '', 8);
                    $x_rel = $pdf->GetX();
                    $pdf->MultiCell(150, $h_desc, $desc_comp, 1, "L", false, 1, "", "", true, 1, false, true, $h_desc, "M");
                    $pdf->setX($x_rel);
                    foreach ($raggruppamento_chiave as $competenza_singola)
                    {
                        $last_element = '';
                        $parts = explode(' - ', $competenza_singola['competenze_chiave']);
                        if (!empty($parts)) {
                            $last_element = end($parts);
                        }
                        if ($last_element == 'Lingua italiana') {
                            $last_element = 'Langue italienne';
                        } elseif ($last_element == 'Lingua francese') {
                            $last_element = 'Langue fran&ccedil;aise';
                        }
                        if (!isset($competenza_singola['valore'])) {
                            $competenza_singola['valore'] = '';
                        }
                        $pdf->MultiCell(125, $competenza_singola['h_riga'], decode($last_element), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->setX($x_rel);
                    }

                    $pdf->setX($x_base);
                    $cont++;

                } else {
                    // scansione lingue
                    foreach ($raggruppamento_chiave as $key => $competenza_singola)
                    {
                        $desc_tmp = $competenza_singola['descrizione'];
                        if (stripos($desc_tmp, '###id_lingua_') !== false ) {
                            $lingua_varsrc = explode('###', $desc_tmp)[1];
                            if (!empty($competenza_singola['testo'])) {
                                $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", $competenza_singola['testo'], $desc_tmp);
                            } else {
                                $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", '______________________________', $desc_tmp);
                            }
                        }
                    }

                    $h_competenze = 0;
                    foreach ($raggruppamento_chiave as $k => $competenza_singola)
                    {
                        $raggruppamento_chiave[$k]['h_riga'] = max($pdf->getStringHeight(115, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            5);
                        $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
                    }
                    $h_chiave = max($h_competenze, $pdf->getStringHeight(50, decode($chiave),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1));

                    if ($pdf->GetY()+$h_chiave>$pdf->GetPageHeight()) {
                        $pdf->AddPage('P');
                    }
                    $pdf->SetFont('helvetica', 'B', 8);
                    $pdf->MultiCell(40, $h_chiave, decode($chiave), 1, "L", false, 0, "", "", true, 0, false, true, $h_chiave, "M");
                    $pdf->SetFont('helvetica', '', 8);
                    $x_rel = $pdf->getX();
                    foreach ($raggruppamento_chiave as $competenza_singola)
                    {
                        $pdf->MultiCell(125, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->setX($x_rel);
                    }

                    $pdf->setX($x_base);
                    $cont++;
                }
            }
            // Scrivo le competenze con il testo
            foreach ($competenze_testo as $competenza_singola)
            {
                $riga_controllo_altezza = decode(str_replace("alunno/a", "élève",$competenza_singola['descrizione'])) . "\n " . decode($competenza_singola['testo']);
                $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;
                $pdf->MultiCell(190, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
                $cont++;
            }
            // @@@ fine trad francese
            // Data e firma
            $pdf->ln(0);
            $pdf->CellFitScale(50, 7, "Date $data", 0, 0, "L");
            $pdf->CellFitScale(80, 7, "", 0, 0, "C");

            if ($stampa_nota_documento == 'SI') {
                $pdf->CellFitScale(50, 7, "La coordinatrice des activités éducatives et didactiques(1)(*)", 0, 1, "C");
            }
            else {
                $pdf->CellFitScale(50, 7, "La coordinatrice des activités éducatives et didactiques (1)", 0, 1, "C");
            }
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->CellFitScale(50, 0, '', 0, 0, "C");
            $pdf->CellFitScale(80, 0, "", 0, 0, "C");
            $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");

            // Firma omessa
            if ($stampa_firma_omessa == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->CellFitScale(125, 0, "", 0, 0, "C");
                $pdf->CellFitScale(55, 0, "Signature omise conformément à l'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
            }
            if ($stampa_nota_documento == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
            }
            // Legenda livelli di valutazione
            if ($pdf->GetY()>245) {
                $pdf->AddPage('P');
            } else {
                $pdf->setY(246);
            }
            $pdf->SetFont('helvetica', 'B', 8);
            $pdf->CellFitScale(30, 0, $labels['legenda_livello_fra'], "B", 0, "L");
            $pdf->CellFitScale(5, 0, "", "B", 0, "C");
            $pdf->CellFitScale(155, 0, "Indicateurs explicatifs", "B", 1, "L");
            foreach ($livelli_em_fra as $valore => $spiegazione)
            {
                $riga = $pdf->MultiCellNbLines(155, $spiegazione) * 4.7;
                $pdf->SetFont('helvetica', 'IB', 8);
                $pdf->MultiCell(30, $riga, $valore, 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->SetFont('helvetica', '', 8);
                $pdf->MultiCell(5, $riga, '', 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(155, $riga, $spiegazione, 0, "L", false, 1, "", "", true, 0, false, true, $riga, "M");
            }
            $pdf->CellFitScale(0, 2, "", "B", 1, "C");
            $pdf->ln(1);
            $pdf->writeHTMLCell( 0, 0, '', '', $labels['nota_legenda_fra'], 0, 0);
            //}}} </editor-fold>            
            
            //}}} </editor-fold>
        }
        else
        {
            //{{{ <editor-fold defaultstate="collapsed" desc="TRENTINO">

                //{{{ <editor-fold defaultstate="collapsed" desc="legenda livelli di valutazione">
                $livelli_em = [
                    "A - Livello base"          => "lo studente svolge compiti semplici in situazioni note, mostrando di possedere conoscenze ed abilità essenziali e di saper applicare regole e procedure fondamentali. È in grado di relazionarsi con gli altri e di esporre le proprie opinioni.\n"
                                                . "(Nel caso in cui non sia stato raggiunto il livello base, la casella viene barrata).",
                    "B - Livello intermedio"    => "lo studente svolge compiti e risolve problemi in situazioni note, mostrando di saper utilizzare le conoscenze e le abilità acquisite. È in grado di relazionarsi con gli altri, di proporre le proprie opinioni e sostenerle.",
                    "C - Livello avanzato"      => "lo studente svolge compiti e problemi in situazioni anche nuove, mostrando padronanza nell’uso delle conoscenze e delle abilità. È in grado di relazionarsi con gli altri, di proporre le proprie opinioni e sostenerle con adeguate argomentazioni.",
                ];
                //}}} </editor-fold>

                //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
                $pdf->AddPage('P');

                // Logo
                if ($intestazione == "logo")
                {
                    inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione, 50, 10);
                }
                elseif ($intestazione == "testo_personalizzato")
                {
                    $pdf->Image('immagini_scuola/logo_repubblica.jpg', 21, 15, 16);
                    $pdf->Image('immagini_scuola/logo_trento.jpg', 170, 11, 16);
                    $pdf->setXY(10, 38);
                    $pdf->SetFont('helvetica', 'B', 8);
                    $pdf->CellFitScale(40, 0, "REPUBBLICA ITALIANA", 0, 1, "C");
                    $pdf->setXY(157, 38);
                    $pdf->MultiCell(40, 10, "PROVINCIA\nAUTONOMA DI TRENTO", 0, 'C', false, 0);
                    $pdf->setY(37);
                    $pdf->SetFont('helvetica', 'B', 14);
                    $pdf->CellFitScale(42, 0, '', 0, 0, "C");
                    $pdf->CellFitScale(106, 0, $testo_riga_1, 0, 1, "C");
                    $pdf->ln(1);
                    $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
                }

                // Titoli
                $pdf->setY(60);
                $pdf->SetFont('helvetica', 'B', 16);
                $pdf->CellFitScale(190, 0, $labels['titolo1'], 0, 1, "C");
                $pdf->CellFitScale(190, 0, $labels['titolo2'], 0, 1, "C");
                $pdf->ln(8);
                $pdf->SetFont('helvetica', 'B', 14);
                $pdf->CellFitScale(190, 0, $dirigente_trentino, 0, 1, "C");
                $pdf->ln(8);

                // Decreti
                $pdf->SetFont('helvetica', '', 11);
                $pdf->MultiCell(190, 0, $labels['decreto1_tn'] . "\n" . $labels['decreto2_tn'] . "\n" . $labels['decreto3_tn'], 0, 'L', false, 1);
                $pdf->ln(8);
//                    $y_rel = $pdf->getY();
//                    $pdf->setY($y_rel + 75);
                $pdf->SetFont('helvetica', 'B', 14);
                $pdf->CellFitScale(190, 0, $labels['certifica'], 0, 1, "C");
                $pdf->ln(8);

                // Dati alunno
                //nome cognome
                $pdf->SetFont('helvetica', '', 11);
                $pdf->CellFitScale(40, 0, $labels['studente'], 0, 0, "L");
                $pdf->SetFont('helvetica', 'B', 11);
                $pdf->CellFitScale(150, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
                $pdf->ln(3);
                //data luogo nascita
                $pdf->SetFont('helvetica', '', 11);
                $pdf->CellFitScale(30, 0, $labels['nato'] . " a", 0, 0, "L");
                $pdf->SetFont('helvetica', 'B', 11);
                $pdf->CellFitScale(110, 0, $studente_nascita, 0, 0, "L");
                $pdf->SetFont('helvetica', '', 11);
                $pdf->CellFitScale(10, 0, "il", 0, 0, "L");
                $pdf->SetFont('helvetica', 'B', 11);
                $pdf->CellFitScale(40, 0, date("d/m/Y", $studente['data_nascita']), 0, 1, "L");
                $pdf->ln(8);
                //livelli competenza
                $pdf->SetFont('helvetica', '', 11);
                $pdf->CellFitScale(190, 0, $labels['livello_c_tn'], 0, 1, "L");
                //Legenda livelli di valutazione
                $pdf->ln(8);
                $html = "";
                foreach ($livelli_em as $valore => $spiegazione)
                {
                    $html .= "<p><b>" . $valore . ": </b>" . $spiegazione . "\n";
//                        $riga = $pdf->MultiCellNbLines(155, $spiegazione) * 4.7;
//                        $pdf->SetFont('helvetica', 'B', 11);
//                        $pdf->MultiCell(30, $riga, $valore, 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
//                        $pdf->SetFont('helvetica', '', 11);
//                        $pdf->MultiCell(5, $riga, '', 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
//                        $pdf->MultiCell(155, $riga, $spiegazione, 0, "L", false, 1, "", "", true, 0, false, true, $riga, "M");
                }
                $pdf->writeHTML($html);
                //}}} </editor-fold>

                //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2">
                $pdf->AddPage('P');
                $x_base = $pdf->getX();

                // Intestazione tabella
                $pdf->SetFont('helvetica', 'B', 8);
                $pdf->MultiCell(45, 21, $labels['titolo1_tab1_tn'], "LTB", "C", false, 0, "", "", true, 0, false, true, 21, "M");
                $pdf->MultiCell(110, 21, $labels['titolo2_tab1_tn'], "LTB", "C", false, 0, "", "", true, 0, false, true, 21, "M");
                $pdf->MultiCell(35, 21, $labels['titolo3_tab1_tn'], "LTRB", "L", false, 1, "", "", true, 0, false, true, 21, "M");
                $pdf->SetFont('helvetica', '', 9);

                // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
                $competenze_livello = [];
                $competenze_testo = [];
                foreach ($competenze as $competenza)
                {
                    if ($competenza['competenze_chiave'] == "")
                    {
                        $competenze_testo[] = $competenza;
                    }
                    else
                    {
                        $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
                    }
                }

                $cont = 1;

                // Scrivo le competenze con i livelli
                foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
                {
                    $array_riga_controllo = [];
                    foreach ($raggruppamento_chiave as $competenza_singola)
                    {
                        $array_riga_controllo[] = $competenza_singola['descrizione'];
                    }
                    $riga_controllo_altezza = implode("\n", $array_riga_controllo);
                    $riga_controllo_altezza = decode($riga_controllo_altezza);

                    $riga_chiave = $pdf->MultiCellNbLines(45, decode($chiave)) * 4.5;
                    $riga_descrizione = $pdf->MultiCellNbLines(110, $riga_controllo_altezza) * 4.5;
                    if ($riga_descrizione > $riga_chiave)
                    {
                        $riga = $riga_descrizione;
                        $riga_chiave_autilizzata = false;
                    }
                    else
                    {
                        $riga = $riga_chiave;
                        $riga_chiave_autilizzata = true;
                    }

                    $pdf->MultiCell(45, $riga, decode($chiave), "LB", "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                    $x_rel = $pdf->getX();
                    foreach ($raggruppamento_chiave as $competenza_singola)
                    {
                        if ($riga_chiave_autilizzata)
                        {
                            $h_riga_competenza_singola = $riga_chiave;
                        }
                        else
                        {
                            $h_riga_competenza_singola = $pdf->MultiCellNbLines(110, decode($competenza_singola['descrizione'])) * 4.5;
                        }
                        $pdf->MultiCell(110, $h_riga_competenza_singola, decode($competenza_singola['descrizione']), "LB", "L", false, 0, "", "", true, 0, false, true, $h_riga_competenza_singola, "M");
                        $pdf->MultiCell(35, $h_riga_competenza_singola, $competenza_singola['valore'], "LRB", "C", false, 1, "", "", true, 0, false, true, $h_riga_competenza_singola, "M");
                        $pdf->setX($x_rel);
                    }
                    $pdf->setX($x_base);


                    $cont++;
                }

                // Scrivo le competenze con il testo
                foreach ($competenze_testo as $competenza_singola)
                {
                    $riga_controllo_altezza = decode($competenza_singola['descrizione']) . "\n" . decode($competenza_singola['testo']);
                    $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;

                    $pdf->MultiCell(10, $riga, $cont, "LB", "C", false, 0, "", "", true, 0, false, true, $riga, "M");
                    $pdf->MultiCell(180, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
                    $cont++;
                }

                // Appunto
                $pdf->SetFont('helvetica', '', 9);
                $pdf->MultiCell(190, 11, "(*) Negli istituti scolastici in cui è previsto l’insegnamento di lingue minoritarie ai sensi l. p. 5/2006, art. 3, comma 2, la certificazione tiene conto anche della competenza comunicativa in tali lingue.", 0, "L", false, 1, "", "", true, 0, false, true, 11, "M");

                // Data e firma
                $pdf->CellFitScale(50, 0, "", 0, 0, "C");
                $pdf->CellFitScale(80, 0, "", 0, 0, "C");
                if ($stampa_nota_documento == 'SI') {
                    $pdf->CellFitScale(50, 0, $dirigente_trentino.'(*)', 0, 1, "C");
                }
                else {
                    $pdf->CellFitScale(50, 0, $dirigente_trentino, 0, 1, "C");
                }
                $pdf->CellFitScale(130, 0, $studente['descrizione_comuni'] . ", " . $data, 0, 0, "L");
                $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");

                // Firma omessa
                if ($stampa_firma_omessa == 'SI')
                {
                    $pdf->SetFont('helvetica', '', 7);
                    $pdf->CellFitScale(125, 0, "", 0, 0, "C");
                    $pdf->CellFitScale(55, 0, "Firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
                }

                if ($stampa_nota_documento == 'SI')
                {
                    $pdf->SetFont('helvetica', '', 7);
                    $pdf->ln(14);
                    $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
                }
                //}}} </editor-fold>

            //}}} </editor-fold>
        }

        //}}} </editor-fold>
    }
    else
    {
        //{{{ <editor-fold defaultstate="collapsed" desc="----SUPERIORI (include caso Trentino)----">

        //{{{ <editor-fold defaultstate="collapsed" desc="legenda livelli di valutazione">
        $livelli_s = [
            "A"     => "Avanzato",
            "B"     => "Intermedio",
            "C"     => "Base",
            "D"     => "Iniziale"
        ];
        $livelli_em = [
            "A - Avanzato"      => "L’alunno/a svolge compiti e risolve problemi complessi, mostrando padronanza nell’uso delle conoscenze e delle abilità; propone e sostiene le proprie opinioni e assume in modo responsabile decisioni consapevoli.",
            "B - Intermedio"    => "L’alunno/a svolge compiti e risolve problemi in situazioni nuove, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.",
            "C - Base"          => "L’alunno/a svolge compiti semplici anche in situazioni nuove, mostrando di possedere conoscenze e abilità fondamentali e di saper applicare basilari regole e procedure apprese.",
            "D - Iniziale"      => "L’alunno/a, se opportunamente guidato/a, svolge compiti semplici in situazioni note."
        ];
        //}}} </editor-fold>

        if($valle_aosta_abilitata == 'SI')
        {
            $livelli_s_ao = [
                "A"     => "livello Avanzato; niveau avancé",
                "B"     => "livello Intermedio; niveau moyen",
                "C"     => "livello Base; niveau base",
                "D"     => "livello base non raggiunto; niveau non atteint",
//
//                        "A_ao"     => "niveau avancé",
//                        "B_ao"     => "niveau moyen",
//                        "C_ao"     => "niveau base",
//                        "D_ao"     => "niveau non atteint"
            ];

            $labels_ao = [
                "ASSE DEI LINGUAGGI"    => "DOMAINE LINGUISTIQUE",
                "ASSE MATEMATICO"       => "DOMAINE MATH&Eacute;MATIQUE",
                "ASSE SCIENTIFICO-TECNOLOGICO"      => "DOMAINE SCIENTIFIQUE ET TECHNOLOGIQUE",
                "ASSE STORICO-SOCIALE"  => "DOMAINE HISTORIQUE ET SOCIAL",
                "ASSE MUSICALE"         => "DOMAINE MUSICAL",

                "Lingua italiana:"      => "Langue italienne&nbsp;:
&bull; Ma&icirc;triser les outils de l&rsquo;expression et de l&rsquo;argumentation indispensables pour g&eacute;rer l&rsquo;interaction communicative verbale dans diff&eacute;rents contextes
&bull; Lire, comprendere et interpr&eacute;ter les textes &eacute;crits de diff&eacute;rents types
&bull; Produire diff&eacute;rents types de textes, en fonction du but de la communication",
                "Lingua francese:"      => "\nLangue fran&ccedil;aise&nbsp;:
&bull; Ma&icirc;triser les outils de l&rsquo;expression et de l&rsquo;argumentation indispensables pour g&eacute;rer l&rsquo;interaction communicative verbale dans diff&eacute;rents contextes
&bull; Lire, comprendere et interpr&eacute;ter les textes &eacute;crits de diff&eacute;rents types
&bull; Produire diff&eacute;rents types de textes, en fonction du but de la communication",
                "Lingua straniera:"      => "Langue &eacute;trang&egrave;re&nbsp;:
&bull; Utiliser la langue(3) dans le cadre des principaux objectifs de communication et d&rsquo;action",
                "Altri linguaggi"      => "Autres langues&nbsp;:
&bull; Utiliser les outils fondamentaux pour exploiter de fa&ccedil;on responsable le patrimoine artistique et litt&eacute;raire
&bull; Utiliser et produire des textes multim&eacute;dias",


                "ASSE MATEMATICO ao"       => "&bull; Utiliser les techniques et proc&eacute;dures de calcul arithm&eacute;tiques et alg&eacute;briques et savoir les repr&eacute;senter sous forme de graphique, &eacute;ventuellement
&bull; Comparer et analyser des figures g&eacute;om&eacute;triques en en trouvant les &eacute;l&eacute;ments constants et les relations qui les caract&eacute;risent
&bull; Savoir d&eacute;finir la strat&eacute;gie appropri&eacute;e pour r&eacute;soudre un probl&egrave;me
&bull; Analyser des donn&eacute;es et les interpr&eacute;ter en d&eacute;veloppant des d&eacute;ductions et des raisonnements sur celles-ci, &eacute;ventuellement &agrave; l&rsquo;aide de repr&eacute;sentations graphiques, et en utilisant correctement les instruments de calcul et le potentiel des applications sp&eacute;cifiques de type informatique",
                "ASSE SCIENTIFICO-TECNOLOGICO ao"      => "&bull; Observer, d&eacute;crire et analyser des ph&eacute;nom&egrave;nes relevant de la r&eacute;alit&eacute; naturelle et artificielle et reconna&icirc;tre dans les diverses formes les concepts de syst&egrave;me et de complexit&eacute;
&bull; Analyser qualitativement et quantitativement les ph&eacute;nom&egrave;nes li&eacute;s aux transformations de l&rsquo;&eacute;nergie, &agrave; partir de l&rsquo;exp&eacute;rience
&bull; Etre conscient du potentiel et des limites des technologies, dans le contexte culturel et social o&ugrave; elles sont appliqu&eacute;es",
                "ASSE STORICO-SOCIALE ao"  => "&bull; Comprendre le changement et la diversit&eacute; des p&eacute;riodes historiques dans une dimension diachronique, en comparant les &eacute;poques, et dans une dimension synchronique, en comparant les zones g&eacute;ographiques et culturelles
&bull; Situer l&rsquo;exp&eacute;rience personnelle dans un syst&egrave;me de r&egrave;gles fond&eacute; sur la reconnaissance des droits garantis par la Constitution aux personnes, &agrave; la collectivit&eacute; et &agrave; l&rsquo;environnement
&bull; Reconna&icirc;tre les caract&eacute;ristiques essentielles du syst&egrave;me socio-&eacute;conomique afin de savoir se rep&eacute;rer dans le tissu productif de son territoire",

                "ASSE MUSICALE brano musicale"         => "&bull; Analyser et d&eacute;crire pour l&rsquo;&eacute;coute les principales caract&eacute;ristiques morphologiques et syntaxiques-formelles et les principaux traits stylistiques relatifs &agrave; un extrait musical.",
                "ASSE MUSICALE dimensione storica"         => "&bull; Analyser et d&eacute;crire par une terminologie et des sch&eacute;mas relatifs, des extraits de musique significatifs et en comprendre les caract&eacute;ristiques de genre, l&rsquo;&eacute;volution de style et la dimension historique.",
                "ASSE MUSICALE interpretativi affrontati"         => "&bull; R&eacute;aliser par un instrument et par la voix, aussi bien individuellement qu&rsquo;en groupe, les aspects techniques, d&rsquo;ex&eacute;cution, d&rsquo;expression et d&rsquo;interpr&eacute;tation abord&eacute;s.",
                "ASSE MUSICALE stili diversi"         => "&bull; R&eacute;aliser &agrave; travers la composition et/ou l&rsquo;improvisation en se servant des technologies pertinentes, m&ecirc;me par le multim&eacute;dia, des produits musicaux caract&eacute;ris&eacute;s par des genres, des formes et des styles diff&eacute;rents.",
            ];

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
            $font_size_1 = 12;

            $pdf->AddPage('P');
            $pdf->SetAutoPageBreak("off", 1);

            $pdf->SetFont('helvetica', '', $font_size_1 );
            $pdf->Image('immagini_scuola/logo_vda.png', 50, 10, 0, 16);
            $pdf->SetX(70);
            $pdf->CellFitScale(0, 3, 'REGIONE AUTONOMA VALLE D’AOSTA', 0, 1);
            $pdf->SetX(70);
            $pdf->CellFitScale(0, 3, 'RÉGION AUTONOME VALLÉE D’AOSTE', 0, 1);
            $pdf->ln(9);


            $pdf->SetFont('helvetica', 'B', $font_size_1 + 2);
            $pdf->CellFitScale(190, 0, $labels['titolo3'], 0, 1, "C");
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['titolo3_ao'], 0, 1, 0, true, 'C');
            $pdf->ln(2);
            $pdf->CellFitScale(190, 0, $labels['titolo4'], 0, 1, "C");
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['titolo4_ao'], 0, 1, 0, true, 'C');
            $pdf->ln(8);
            $pdf->SetFont('helvetica', '', $font_size_1 + 1);
            $pdf->CellFitScale(190, 0, $labels['dirigente'], 0, 1, "C");
            $pdf->CellFitScale(190, 0, $labels['dirigente_ao'], 0, 1, "C");
            $pdf->ln(8);

            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->MultiCell(190, 0, $labels['decreto3'], 0, 'L', false, 1);
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['decreto3_ao'], 0, 1, 0, true, 'L');
            $pdf->ln(2);
            $pdf->MultiCell(190, 0, $labels['decreto4'], 0, 'L', false, 1);
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['decreto4_ao'], 0, 1, 0, true, 'L');
            $pdf->ln(16);
            $pdf->SetFont('helvetica', 'B', $font_size_1 + 2);
            $pdf->CellFitScale(190, 0, $labels['certifica2'], 0, 1, "C");
            $pdf->CellFitScale(190, 0, $labels['certifica2_ao'], 0, 1, "C");
            $pdf->ln(7);

            // Dati alunno
            //nome cognome
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(45, 0, $labels['studente'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(145, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
            $pdf->writeHTMLCell( 45, 0, '', '', $labels['studente_ao'], 0, 1, 0, true, 'L');
            $pdf->ln(3);

            //data luogo nascita
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(20, 0, $labels['nato'] . " il", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(35, 0, date('d/m/Y', $studente['data_nascita']), 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(10, 0, "a", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(60, 0, $studente_nascita, 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(25, 0, "Stato", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(25, 0, estrai_nazione($studente['stato_nascita'])['descrizione'], 0, 1, "L");

            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(20, 0, $labels['nato_ao'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(35, 0, '', 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(10, 0, "à", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(60, 0, '', 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(25, 0, "Etat", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(25, 0, '', 0, 1, "L");
            $pdf->ln(3);
            // classe sezione
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(95, 0, $labels['iscritto'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(10, 0, $classe_romana, 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(10, 0, "sez.", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(20, 0, $studente['sezione'], 0, 1, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->writeHTMLCell( 95, 0, '', '', $labels['iscritto_ao'], 0, 0, 0, true, 'L');
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(10, 0, "", 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(10, 0, "section", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(20, 0, '', 0, 1, "L");
            $pdf->ln(3);
            // indirizzo
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(50, 0, $labels['indirizzo_studio'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(140, 0, $studente['descrizione_indirizzi'], 0, 1, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->writeHTMLCell( 50, 0, '', '', $labels['indirizzo_studio_ao'], 0, 0, 0, true, 'L');
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(140, 0, '', 0, 1, "L");
            $pdf->ln(3);
            // anno scolastico
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(50, 0, $labels['anno_scolastico2'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(35, 0, $anno_scolastico, 0, 1, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->writeHTMLCell( 50, 0, '', '', $labels['anno_scolastico2_ao'], 0, 0, 0, true, 'L');
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(35, 0, '', 0, 1, "L");
            $pdf->ln(3);
            //livelli competenza
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(190, 0, $labels['obbligo_istruzione'], 0, 1, "L");
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['obbligo_istruzione_ao'], 0, 1, 0, true, 'L');

            // Acquisizione competenze
            $pdf->ln(8);
            $pdf->SetFont('helvetica', 'BI', $font_size_1 + 2);
            $pdf->CellFitScale(190, 0, $labels['acquisito'], 0, 1, "C");
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['acquisito_ao'], 0, 1, 0, true, 'C');
            $pdf->ln(8);
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(190, 0, $labels['competenze'], 0, 1, "L");
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['competenze_ao'], 0, 1, 0, true, 'L');
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2">
            $font_size_2 = 8;

            $pdf->AddPage('P');
            $x_base = $pdf->getX();

            // Intestazione tabella
            $pdf->SetFont('helvetica', 'B', $font_size_2 + 2);
            $x_tmp = $x_base;
            $pdf->setX($x_tmp);
            $pdf->writeHTMLCell( 190, 10, '', '', $labels['titolo1_tab2'].'<br>'.$labels['titolo1_tab2_ao'], 1, 1, 0, true, 'C');


            // Raggruppo le competenze con la stessa chiave
            $competenze_livello = [];
            foreach ($competenze as $competenza)
            {
                $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
            }

            // Ciclo competenze
            $larghezza_assi = ($trentino_abilitato == 'SI') ? 105 : 120;
            $larghezza_livelli = ($trentino_abilitato == 'SI') ? 35 : 70;
            $largezza_cittadinanza = 190 - 105 - 35 - $x_base;
            $cont_righe = 0;

            foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
            {
                $pdf->SetFont('helvetica', 'B', $font_size_2 + 1);
                $pdf->setX($x_tmp);
                $pdf->writeHTMLCell( $larghezza_assi, 11, '', '', $chiave.'<br>'.$labels_ao[$chiave], "LRB", 0, 0, true, 'C');
                $pdf->MultiCell($larghezza_livelli, 11, $labels['livelli']."\n".$labels['livelli_ao'], "RB", 'C', 0, $ln=1, $x='', $y='', $reseth=true, $stretch=0, $ishtml=false, $autopadding=true, $maxh=0);

                $cont_righe += 6;

                foreach($raggruppamento_chiave as $competenza_singola)
                {
                    $pdf->SetFont('helvetica', '', $font_size_2);

                    $descrizione_competenza = $competenza_singola['descrizione'];
                    $descrizione_competenza_ao = '';

                    if ($chiave == 'ASSE DEI LINGUAGGI')
                    {
                        foreach ($labels_ao as $key_ao => $desc_ao) {
                            if ( (strpos($descrizione_competenza, $key_ao) !== false) ) {
                                $descrizione_competenza_ao = $desc_ao;
                                break;
                            }
                        }
                    }
                    elseif ($chiave == 'ASSE MUSICALE')
                    {
                        foreach ($labels_ao as $key_ao => $desc_ao) {
                            $search_txt = str_replace('ASSE MUSICALE ', '', $key_ao);
                            if ( (strpos($descrizione_competenza, $search_txt) !== false) ) {
                                $descrizione_competenza_ao = "\n$desc_ao";
                                break;
                            }
                        }
                    }
                    else
                    {
                        $descrizione_competenza_ao = $labels_ao[$chiave.' ao'];
                    }
                    $descrizione_competenza .= "\n" . $descrizione_competenza_ao;

                    //{{{ <editor-fold defaultstate="collapsed" desc="Filtro asse dei linguaggi unico per il trentino e sostituzione lingua straniera">
                    if ($chiave == "ASSE DEI LINGUAGGI")
                    {
                        $prima_lingua_straniera = estrai_dati_materia($studente['id_lingua_1']);

                        if ((strpos($descrizione_competenza, "italiana") !== false)
                            && (strpos($descrizione_competenza, "straniera") !== false)
                            && (strpos($descrizione_competenza, "linguaggi") !== false))
                        {
                            $mostra = false;
                        }
                        else
                        {
                            $mostra = true;
                            if (count($prima_lingua_straniera) > 0)
                            {
                                $descrizione_competenza = str_replace("###", $prima_lingua_straniera['descrizione'], $descrizione_competenza);
                            }
                            else
                            {
                                $descrizione_competenza = str_replace("###", "____________", $descrizione_competenza);
                            }
                        }
                    }
                    else
                    {
                        $mostra = true;
                    }
                    //}}} </editor-fold>

                    if ($mostra)
                    {
                        $riga_controllo_altezza_chiave = decode($descrizione_competenza);
                        $riga_controllo_altezza_chiave = str_replace("\\n", "\n", $riga_controllo_altezza_chiave);
//                                $riga_controllo_altezza_chiave = str_replace("<br>", "\n", $riga_controllo_altezza_chiave);
                        if ($trentino_abilitato != 'SI')
                        {
                            $riga_chiave = $pdf->MultiCellNbLines($larghezza_assi, $riga_controllo_altezza_chiave) * 4.5;
                        }
                        else
                        {
                            $riga_chiave = $pdf->MultiCellNbLines($larghezza_assi, $riga_controllo_altezza_chiave) * 4;
                        }
                        $riga_controllo_altezza_livelli = decode($livelli_s[$competenza_singola['valore']]) . " perchè " . decode($competenza_singola['testo']);
                        $riga_livelli = $pdf->MultiCellNbLines($larghezza_livelli, $riga_controllo_altezza_livelli) * 4.5;
                        $riga = ($riga_chiave > $riga_livelli) ? $riga_chiave : $riga_livelli;

                        $pdf->setX($x_tmp);
                        $pdf->MultiCell($larghezza_assi, $riga, $riga_controllo_altezza_chiave, "RBL", "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                        $pdf->SetFont('helvetica', 'B', $font_size_2);

                        if ($competenza_singola['valore'] == "D")
                        {
                            if ($trentino_abilitato != 'SI')
                            {
                                if (trim($competenza_singola['testo']) != '')
                                {
                                    $livello_raggiunto = decode($livelli_s[$competenza_singola['valore']]) . " perchè " . decode($competenza_singola['testo']);
                                }
                                else
                                {
                                    $livello_raggiunto = decode($livelli_s[$competenza_singola['valore']]);
                                }
                            }
                            else
                            {
                                $livello_raggiunto = decode($livelli_s[$competenza_singola['valore']]);
                            }
                        }
                        else
                        {
                            $livello_raggiunto = $competenza_singola['valore'];
                        }
                        $pdf->MultiCell($larghezza_livelli, $riga, $livello_raggiunto, "BR", "C", false, 1, "", "", true, 0, false, true, $riga, "M");
                        $cont_righe += $riga;
                    }
                }
            }
            $pdf->ln(4);
            $pdf->writeHTMLCell( 0, 0, '', '', "Legenda:", 0, 1, 0, true, 'C');
            foreach ($livelli_s_ao as $liv => $desc_liv) {
                $pdf->writeHTMLCell( 0, 0, '', '', "$liv: $desc_liv;", 0, 1, 0, true, 'L');
            }
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 3">
            if ($stampa_compatta != 'SI') {
                $pdf->AddPage('P');
            }
            $pdf->ln(3);
            $pdf->SetFont('helvetica', '', $font_size_2 );
            $pdf->MultiCell(190, 0, $labels['cittadinanza'], 0, "L", false, 1, "", "", true, 0, false, true, 0, "M");
            $pdf->writeHTMLCell( 190, 0, '', '', $labels['cittadinanza_ao'], 0, 1, 0, true, 'L');
            $pdf->ln(5);

//                $pdf->SetY(265);
            $pdf->SetFont('helvetica', 'B', $font_size_2);
            $pdf->CellFitScale(140, 6, $studente['descrizione_comuni'] . ", " . $data, 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_2);
            if ($stampa_nota_documento == 'SI') {
                $pdf->MultiCell(50, 6, "Il Dirigente Scolastico(*)\nLE DIRIGEANT SCOLAIRE", 0, 'C', 0, 1);
            }
            else {
                $pdf->MultiCell(50, 6, "Il Dirigente Scolastico\nLE DIRIGEANT SCOLAIRE", 0, 'C', 0, 1);
            }
            $pdf->SetFont('helvetica', 'B', $font_size_2);
            $pdf->CellFitScale(140, 0, "", 0, 0, "C");
            $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");
            $pdf->ln(12);

            // Firma omessa
            if ($stampa_firma_omessa == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->CellFitScale(135, 0, "", 0, 0, "C");
                $pdf->CellFitScale(55, 0, "Firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
            }

            if ($stampa_nota_documento == 'SI')
            {
                $pdf->SetFont('helvetica', '', 8);
                $pdf->ln(14);
                $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
            }

            $x_base = $pdf->getX();
            $pdf->SetFont('helvetica', '', 9);

            $html = "<p>(1) Il presente certificato ha validit&agrave; nazionale Le pr&eacute;sent certificat est reconnu sur l'ensemble du territoire national.</p>

<p>(2) Livelli relativi all&rsquo;acquisizione delle competenze di ciascun asse: Niveau des comp&eacute;tences acquises dans chaque domaine&#160;:</p>

<p>Livello base: lo studente svolge compiti semplici in situazioni note, mostrando di possedere conoscenze ed abilit&agrave; essenziali e di saper applicare regole e procedure fondamentali. Nel caso in cui non sia stato raggiunto il livello base, &egrave; riportata l&rsquo;espressione &ldquo;livello base non raggiunto&rdquo;, con l&rsquo;indicazione della relativa motivazione. <br>Niveau de base&#160;: l&rsquo;&eacute;l&egrave;ve effectue des t&acirc;ches simples dans un contexte qu&rsquo;il conna&icirc;t, il d&eacute;montre qu&rsquo;il poss&egrave;de les connaissances et les comp&eacute;tences essentielles et qu&rsquo;il sait appliquer les r&egrave;gles et les proc&eacute;dures fondamentales. Si le niveau de base n&rsquo;est pas acquis, la mention &laquo;&#160;Niveau de base non acquis&#160;&raquo; est assortie du motif de cette &eacute;valuation.</p>

<p>Livello intermedio: lo studente svolge compiti e risolve problemi complessi in situazioni note, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilit&agrave; acquisite. <br>Niveau interm&eacute;diaire&#160;: l&rsquo;&eacute;l&egrave;ve effectue des t&acirc;ches et r&eacute;sout des probl&egrave;mes complexes dans un contexte qu&rsquo;il conna&icirc;t, il effectue des choix responsables et d&eacute;montre qu&rsquo;il sait utiliser les connaissances et les comp&eacute;tences acquises.</p>

<p>Livello avanzato: lo studente svolge compiti e problemi complessi in situazioni anche non note, mostrando padronanza nell&rsquo;uso delle conoscenze e delle abilit&agrave;. Sa proporre e sostenere le proprie opinioni e assumere autonomamente decisioni consapevoli. <br>Niveau avanc&eacute;&#160;: l&rsquo;&eacute;l&egrave;ve effectue des t&acirc;ches et r&eacute;sout des probl&egrave;mes complexes dans un contexte qu&rsquo;il ne conna&icirc;t pas toujours et d&eacute;montre qu&rsquo;il ma&icirc;trise l&rsquo;utilisation des connaissances et des comp&eacute;tences acquises. Il est capable de pr&eacute;senter et de soutenir ses opinions et de prendre des d&eacute;cisions responsables de fa&ccedil;on autonome.</p>

<p>(3) Specificare la prima lingua straniera studiata. <br>Pr&eacute;ciser la premi&egrave;re langue &eacute;trang&egrave;re &eacute;tudi&eacute;e.</p>";

            $pdf->writeHTML($html, true, false, true, false, '');
            //}}} </editor-fold>
        }
        else
        {
            if ($trentino_abilitato != 'SI' && $anno_inizio >= 2023) {
            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
            $pdf->AddPage('P');
            $pdf->SetAutoPageBreak("off", 1);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->CellFitScale(160, 0, '', 0, 0, "C");

            // Logo
            if ($intestazione == "logo")
            {
//                    inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione, 50, 20);
                inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione);
            }
            elseif ($intestazione == "testo_personalizzato")
            {
                $pdf->Image('immagini_scuola/logo_repubblica.jpg', "C", 25, 25, '', 'JPG', false, 'C', false, 300, 'C', false, false, 0, false, false, false);
                $pdf->SetFont('helvetica', 'I', 17);
                $pdf->setY(55);
                $pdf->writeHTMLCell(0, 0, '', '', $labels['ministero_istruzione'], 0, 1, false, true, 'C');
                $pdf->ln(15);
                $pdf->SetFont('helvetica', '', 13);

                $pdf->writeHTMLCell(0, 0, '', '', 'Istituzione scolastica', 0, 1, false, true, 'C');
                if ($testo_riga_1 != '' || $testo_riga_2 != '') {
                    $pdf->ln(5);
                    $pdf->CellFitScale(190, 0, $testo_riga_1, 0, 1, "C");
                    $pdf->ln(3);
                    $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
                } else {
                    $pdf->ln(5);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_scuola'], 0, 1, "C");
                    $pdf->ln(3);
                    $pdf->CellFitScale(190, 0, $studente['descrizione_comuni']." ({$studente['provincia_comuni']})", 0, 1, "C");
                }
            }

            // Titoli
            $pdf->setY(130);
            $pdf->SetFont('helvetica', 'B', 19);
            $pdf->CellFitScale(190, 0, $labels['titolo1'], 0, 1, "C");
            $pdf->CellFitScale(190, 0, $labels['titolo2'], 0, 1, "C");
            $pdf->ln(30);

            // Dati alunno
            //nome cognome
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['alunno'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(160, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
            $pdf->ln(5);
            //data luogo nascita
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(30, 0, $labels['nato'] . " a", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(60, 0, $luogo_nascita, 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            $pdf->writeHTMLCell(50, 0, '', '', $labels['provincia'], 0, 0, false, true, 'L');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(10, 0, "il", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->CellFitScale(40, 0, date("d/m/Y", $studente['data_nascita']), 0, 1, "L");
            $pdf->ln(5);
            //livelli competenza
            $pdf->SetFont('helvetica', '', 12);
            $pdf->CellFitScale(190, 0, $labels['livello_c'], 0, 0, "L");
            $pdf->SetFont('helvetica', '', 12);
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2">
            $pdf->AddPage('P');
            $x_base = $pdf->getX();

            // Intestazione tabella
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->MultiCell(42, 12, $labels['titolo1_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(123, 12, $labels['titolo2_tab1'], "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
            $pdf->MultiCell(25, 12, $labels['titolo3_tab1'], "LTRB", "C", false, 1, "", "", true, 0, false, true, 12, "M");
            $pdf->SetFont('helvetica', '', 9);

            // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
            $competenze_livello = [];
            $competenze_testo = [];
            foreach ($competenze as $competenza)
            {
                if ($competenza['competenze_chiave'] == "")
                {
                    $competenze_testo[] = $competenza;
                }
                else
                {
                    $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
                }
            }

            $cont = 1;
            // echo_debug($competenze_livello);
            // Scrivo le competenze con i livelli
            foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
            {
                // scansione lingue
                $chiave_lingue_compilata = false;
                foreach ($raggruppamento_chiave as $key => $competenza_singola)
                {
                    $desc_tmp = $competenza_singola['descrizione'];
                    if (stripos($desc_tmp, '###id_lingua_') !== false ) {
                        $lingua_varsrc = explode('###', $desc_tmp)[1];
                        
                        if (!empty($competenza_singola['testo'])) {
                            $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", $competenza_singola['testo'], $desc_tmp);
                            $raggruppamento_chiave[$key]['lingua_valutazione'] = 'si';
                            $chiave_lingue_compilata = true;
                        } 
                        // elseif (!$chiave_lingue_compilata) {
                        //     $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", '______________________________', $desc_tmp);
                        //     $raggruppamento_chiave[$key]['lingua_valutazione'] = 'si';
                        // } 
                        else {
                            unset($raggruppamento_chiave[$key]);
                        }
                    }
                }

                $h_competenze = 0;
                foreach ($raggruppamento_chiave as $k => $competenza_singola)
                {
                    if (stripos($chiave, 'Competenza multilinguistica') !== false &&
                        !isset($competenza_singola['lingua_valutazione']) ) {
                        $raggruppamento_chiave[$k]['h_riga'] = $pdf->getStringHeight(148, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 );
                    } else {
                        $raggruppamento_chiave[$k]['h_riga'] = max($pdf->getStringHeight(123, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                            5);
                    }
                    $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
                }
                $h_chiave = max($h_competenze, $pdf->getStringHeight(42, decode($chiave),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1));

                if ($pdf->GetY()+$h_chiave>$pdf->GetPageHeight()) {
                    $pdf->AddPage('P');
                }
                $pdf->SetFont('helvetica', 'B', 9);
                $pdf->MultiCell(42, $h_chiave, decode($chiave), 1, "L", false, 0, "", "", true, 1, false, true, $h_chiave, "M");
                $pdf->SetFont('helvetica', '', 9);
                $x_rel = $pdf->getX();
                foreach ($raggruppamento_chiave as $competenza_singola)
                {
                    if ($competenza_singola['valore'] == '') { $competenza_singola['valore'] = ' ';}

                    if (stripos($chiave, 'Competenza multilinguistica') !== false &&
                        !isset($competenza_singola['lingua_valutazione'])) {
                        $pdf->MultiCell(148, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 1, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                    } else {
                        $pdf->MultiCell(123, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                        $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
                    }
                    $pdf->setX($x_rel);
                }


                $pdf->setX($x_base);
                $cont++;
            }

            // Scrivo le competenze con il testo
            foreach ($competenze_testo as $competenza_singola)
            {
                $riga_controllo_altezza = decode(str_replace("alunno/a", "alunn$min_oa",$competenza_singola['descrizione'])) . "\n " . decode($competenza_singola['testo']);
                $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;
                $pdf->MultiCell(190, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
                $cont++;
            }
            // Data e firma
            $pdf->ln(1);
            $pdf->CellFitScale(50, 7, "Data $data", 0, 0, "L");
            $pdf->CellFitScale(80, 7, "", 0, 0, "C");

            if ($stampa_nota_documento == 'SI') {
                $pdf->CellFitScale(50, 7, $definizione_dirigente ."(1)(*)", 0, 1, "C");
            }
            else {
                $pdf->CellFitScale(50, 7, $definizione_dirigente." (1)", 0, 1, "C");
            }
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->CellFitScale(50, 0, '', 0, 0, "C");
            $pdf->CellFitScale(80, 0, "", 0, 0, "C");
            $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");

            // Firma omessa
            if ($stampa_firma_omessa == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->CellFitScale(125, 0, "", 0, 0, "C");
                $pdf->CellFitScale(55, 0, "Firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
            }

            if ($stampa_nota_documento == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
//                $pdf->setY(225);
                $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
            }

            // Legenda livelli di valutazione
            if ($pdf->GetY()>240) {
                $pdf->AddPage('P');
            } else {
                $pdf->setY(241);
            }
            $pdf->SetFont('helvetica', 'B', 8);
            $pdf->CellFitScale(30, 0, $labels['legenda_livello'], "B", 0, "L");
            $pdf->CellFitScale(5, 0, "", "B", 0, "C");
            $pdf->CellFitScale(155, 0, "Indicatori esplicativi", "B", 1, "L");
            $pdf->SetFont('helvetica', '', 8);
            foreach ($livelli_em as $valore => $spiegazione)
            {
                $riga = $pdf->MultiCellNbLines(155, $spiegazione) * 4.7;
                $pdf->SetFont('helvetica', 'IB', 8);
                $pdf->MultiCell(30, $riga, $valore, 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->SetFont('helvetica', '', 8);
                $pdf->MultiCell(5, $riga, '', 0, "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(155, $riga, $spiegazione, 0, "L", false, 1, "", "", true, 0, false, true, $riga, "M");
            }
            $pdf->CellFitScale(0, 2, "", "B", 1, "C");
            $pdf->writeHTMLCell( 0, 0, '', '', $labels['nota_legenda_ss'], 0, 0);
            //}}} </editor-fold>
            } else {
            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
            $font_size_1 = 12;

            $pdf->AddPage('P');
            $pdf->SetAutoPageBreak("off", 1);
            // Logo
            if ($trentino_abilitato != 'SI')
            {
                if ($intestazione == "logo")
                {
                    inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione, 45, 0);
                }
                elseif ($intestazione == "testo_personalizzato")
                {
                    $sty = $pdf->GetY();
                    $pdf->SetFont('helvetica', 'B', $font_size_1 + 2);
                    $pdf->CellFitScale(0, 0, $labels['ministero1'], 0, 1, "C");
//                        $pdf->CellFitScale(190, 0, $labels['ministero2'], 0, 1, "C");
                    $pdf->SetY($sty);
                    $pdf->Image('immagini_scuola/logo_repubblica.jpg', 30, 9, 13);
                    $pdf->SetFont('helvetica', 'B', 14);
                    $pdf->setY(26);
                    $pdf->CellFitScale(190, 0, $testo_riga_1, 0, 1, "C");
                    $pdf->ln(1);
                    $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
                }
            }
            else
            {
                if ($intestazione == "logo")
                {
                    inserisci_intestazione_pdf($pdf, (int) $id_classe_certificazione, 45, 20);
                }
                elseif ($intestazione == "testo_personalizzato")
                {
                    $pdf->Image('immagini_scuola/logo_repubblica.jpg', 21, 15, 16);
                    $pdf->Image('immagini_scuola/logo_trento.jpg', 170, 11, 16);
                    $pdf->setXY(10, 38);
                    $pdf->SetFont('helvetica', 'B', 8);
                    $pdf->CellFitScale(40, 0, "REPUBBLICA ITALIANA", 0, 1, "C");
                    $pdf->setXY(157, 38);
                    $pdf->MultiCell(40, 10, "PROVINCIA\nAUTONOMA DI TRENTO", 0, 'C', false, 0);
                    $pdf->setY(52);
                    $pdf->SetFont('helvetica', 'B', 14);
                    $pdf->CellFitScale(190, 0, $testo_riga_1, 0, 1, "C");
                    $pdf->ln(1);
                    $pdf->CellFitScale(190, 0, $testo_riga_2, 0, 1, "C");
                }
            }

            // Titoli
            if ($trentino_abilitato != 'SI')
            {
                $pdf->setY(50);
            }
            else
            {
                $pdf->setY(65);
            }
                $pdf->SetFont('helvetica', 'B', $font_size_1 + 4);
                $pdf->CellFitScale(190, 0, $labels['titolo3'], 0, 1, "C");
                $pdf->CellFitScale(190, 0, $labels['titolo4'], 0, 1, "C");
        if ($trentino_abilitato == 'SI')
            {
                $pdf->ln(10);
                $pdf->SetFont('helvetica', 'B', $font_size_1 + 2);
//                    $pdf->CellFitScale(190, 0, $labels['dirigente_trentino'], 0, 1, "C");
                $pdf->CellFitScale(190, 0, $dirigente_trentino, 0, 1, "C");
                $pdf->ln(5);
            }
            else
            {
                $pdf->ln(15);
                $pdf->SetFont('helvetica', 'B', $font_size_1 + 2);
                $pdf->CellFitScale(190, 0, $labels['dirigente'], 0, 1, "C");
                $pdf->ln(15);
            }

            // Regolamenti
            if ($trentino_abilitato == 'SI')
            {
                $pdf->SetFont('helvetica', '', $font_size_1);
                $pdf->MultiCell(190, 0, $labels['decreto3'] . "\n\n" . $labels['decreto5'] . "\n\n" . $labels['decreto4'], 0, 'L', false, 0);
                $pdf->ln(60);
                $pdf->SetFont('helvetica', 'B', $font_size_1 + 2);
                $pdf->CellFitScale(190, 0, $labels['certifica'], 0, 1, "C");
                $pdf->ln(8);
            }
            else
            {
                $pdf->SetFont('helvetica', '', $font_size_1);
                $pdf->MultiCell(190, 0, $labels['decreto3'] . "\n\n" . $labels['decreto4'], 0, 'L', false, 0);
                $pdf->ln(30);
                $pdf->SetFont('helvetica', 'B', $font_size_1 + 2);
                $pdf->CellFitScale(190, 0, $labels['certifica2'], 0, 1, "C");
                $pdf->ln(15);
            }

            // Dati alunno
            //nome cognome
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(45, 0, $labels['studente'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(145, 0, $studente['cognome'] . " " . $studente['nome'], 0, 1, "L");
            $pdf->ln(3);

            //data luogo nascita
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(20, 0, $labels['nato'] . " il", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(35, 0, date('d/m/Y', $studente['data_nascita']), 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(10, 0, "a", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(60, 0, $studente_nascita, 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(25, 0, "Stato", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(25, 0, estrai_nazione($studente['stato_nascita'])['descrizione'], 0, 1, "L");
            $pdf->ln(3);
            // classe sezione
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(95, 0, $labels['iscritto'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(10, 0, $classe_romana, 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(10, 0, "sez.", 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(20, 0, $studente['sezione'], 0, 1, "L");
            $pdf->ln(3);
            // indirizzo
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(50, 0, $labels['indirizzo_studio'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(140, 0, $studente['descrizione_indirizzi'], 0, 1, "L");
            $pdf->ln(3);
            // anno scolastico
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(50, 0, $labels['anno_scolastico2'], 0, 0, "L");
            $pdf->SetFont('helvetica', 'B', $font_size_1);
            $pdf->CellFitScale(35, 0, $anno_scolastico, 0, 1, "L");
            $pdf->ln(3);
            //livelli competenza
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(190, 0, $labels['obbligo_istruzione'], 0, 1, "L");

            // Acquisizione competenze
            $pdf->ln(15);
            $pdf->SetFont('helvetica', 'BI', $font_size_1 + 2);
            $pdf->CellFitScale(190, 0, $labels['acquisito'], 0, 1, "C");
            $pdf->ln(15);
            $pdf->SetFont('helvetica', '', $font_size_1);
            $pdf->CellFitScale(190, 0, $labels['competenze'], 0, 1, "L");
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2">
            $font_size_2 = ($trentino_abilitato == 'SI' || $stampa_compatta == 'SI' ) ? 8 : 10;

            $pdf->AddPage('P');
            $x_base = $pdf->getX();

            // Intestazione tabella
            $pdf->SetFont('helvetica', 'B', $font_size_2 + 2);
            if ($trentino_abilitato == 'SI')
            {
                $x_tmp = 50;
                $y_iniziale = $pdf->getY();
                $pdf->setX($x_tmp);
                $pdf->CellFitScale(140, 8, decode($labels['titolo1_tab2_trentino']), 1, 1, "C");
            }
            else
            {
                $x_tmp = $x_base;
                $pdf->setX($x_tmp);
                $pdf->CellFitScale(190, 8, decode($labels['titolo1_tab2']), 1, 1, "C");
            }

            // Raggruppo le competenze con la stessa chiave
            $competenze_livello = [];
            foreach ($competenze as $competenza)
            {
                $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
            }

            // Ciclo competenze
            $larghezza_assi = ($trentino_abilitato == 'SI') ? 105 : 120;
            $larghezza_livelli = ($trentino_abilitato == 'SI') ? 35 : 70;
            $largezza_cittadinanza = 190 - 105 - 35 - $x_base;
            $cont_righe = 0;

            foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
            {
                $pdf->SetFont('helvetica', 'B', $font_size_2 + 1);
                $pdf->setX($x_tmp);
                $pdf->CellFitScale($larghezza_assi, 6, $chiave, "LRB", 0, "C");
                $pdf->CellFitScale($larghezza_livelli, 6, $labels['livelli'], "RB", 1, "C");
                $cont_righe += 6;

                foreach($raggruppamento_chiave as $competenza_singola)
                {
                    $pdf->SetFont('helvetica', '', $font_size_2);

                    $descrizione_competenza = $competenza_singola['descrizione'];

                    //{{{ <editor-fold defaultstate="collapsed" desc="Filtro asse dei linguaggi unico per il trentino e sostituzione lingua straniera">
                    if ($chiave == "ASSE DEI LINGUAGGI")
                    {
                        if ($trentino_abilitato == "NO")
                        {
                            $prima_lingua_straniera = estrai_dati_materia($studente['id_lingua_1']);

                            if ((strpos($descrizione_competenza, "italiana") !== false)
                                && (strpos($descrizione_competenza, "straniera") !== false)
                                && (strpos($descrizione_competenza, "linguaggi") !== false))
                            {
                                $mostra = false;
                            }
                            else
                            {
                                $mostra = true;
                                if (count($prima_lingua_straniera) > 0)
                                {
                                    $descrizione_competenza = str_replace("###", $prima_lingua_straniera['descrizione'], $descrizione_competenza);
                                }
                                else
                                {
                                    $descrizione_competenza = str_replace("###", "____________", $descrizione_competenza);
                                }
                            }
                        }
                        else
                        {
                            if ((strpos($descrizione_competenza, "italiana") !== false)
                                && (strpos($descrizione_competenza, "straniera") !== false)
                                && (strpos($descrizione_competenza, "linguaggi") !== false))
                            {
                                $mostra = true;
                            }
                            else
                            {
                                $mostra = false;
                            }
                        }
                    }
                    else
                    {
                        $mostra = true;
                    }
                    //}}} </editor-fold>

                    if ($mostra)
                    {
                        $riga_controllo_altezza_chiave = decode($descrizione_competenza);
                        $riga_controllo_altezza_chiave = str_replace("\\n", "\n", $riga_controllo_altezza_chiave);
                        if ($trentino_abilitato != 'SI')
                        {
                            $riga_chiave = $pdf->MultiCellNbLines($larghezza_assi, $riga_controllo_altezza_chiave) * 4.5;
                        }
                        else
                        {
                            $riga_chiave = $pdf->MultiCellNbLines($larghezza_assi, $riga_controllo_altezza_chiave) * 4;
                        }
                        $riga_controllo_altezza_livelli = decode($livelli_s[$competenza_singola['valore']]) . " perchè " . decode($competenza_singola['testo']);
                        $riga_livelli = $pdf->MultiCellNbLines($larghezza_livelli, $riga_controllo_altezza_livelli) * 4.5;
                        $riga = ($riga_chiave > $riga_livelli) ? $riga_chiave : $riga_livelli;

                        $pdf->setX($x_tmp);
                        $pdf->MultiCell($larghezza_assi, $riga, $riga_controllo_altezza_chiave, "RBL", "L", false, 0, "", "", true, 0, false, true, $riga, "M");
                        $pdf->SetFont('helvetica', 'B', $font_size_2);
                        if ($competenza_singola['valore'] == "D")
                        {
                            if ($trentino_abilitato != 'SI')
                            {
                                if (trim($competenza_singola['testo']) != '')
                                {
                                    $livello_raggiunto = decode($livelli_s[$competenza_singola['valore']]) . " perchè " . decode($competenza_singola['testo']);
                                }
                                else
                                {
                                    $livello_raggiunto = decode($livelli_s[$competenza_singola['valore']]);
                                }
                            }
                            else
                            {
                                $livello_raggiunto = decode($livelli_s[$competenza_singola['valore']]);
                            }
                        }
                        else
                        {
                            $livello_raggiunto = decode($livelli_s[$competenza_singola['valore']]);
                        }
                        $pdf->MultiCell($larghezza_livelli, $riga, $livello_raggiunto, "BR", "C", false, 1, "", "", true, 0, false, true, $riga, "M");
                        $cont_righe += $riga;
                    }
                }
            }
            if ($trentino_abilitato != 'SI')
            {
                $pdf->ln(3);
                $pdf->SetFont('helvetica', '', $font_size_2 - 2);
                $pdf->MultiCell(190, 0, $labels['cittadinanza'], 0, "L", false, 1, "", "", true, 0, false, true, 0, "M");
            }
            else
            {
                $pdf->setXY($x_base, $y_iniziale);
                $pdf->SetFont('helvetica', 'B', $font_size_2);
                $pdf->MultiCell($largezza_cittadinanza, 14, $labels['titolo2_tab2'], "TLB", "C", false, 1, "", "", true, 0, false, true, 14, "M");
                $pdf->SetFont('helvetica', '', $font_size_2);
                $pdf->MultiCell($largezza_cittadinanza, $cont_righe - 6, $labels['cittadinanza_trentino'], "LB", "L", false, 1, "", "", true, 0, false, true, $cont_righe - 6, "M");
            }
//                $pdf->SetY(265);
            $pdf->ln(4);
            $pdf->SetFont('helvetica', 'B', $font_size_2);
            $pdf->CellFitScale(140, 6, $studente['descrizione_comuni'] . ", " . $data, 0, 0, "L");
            $pdf->SetFont('helvetica', '', $font_size_2);
            if ($trentino_abilitato == 'SI')
            {
                if ($stampa_nota_documento == 'SI') {
                    $pdf->CellFitScale(50, 6, $dirigente_trentino.'(*)', 0, 1, "C");
                }
                else {
                    $pdf->CellFitScale(50, 6, $dirigente_trentino, 0, 1, "C");
                }
            }
            else
            {
                if ($stampa_nota_documento == 'SI') {
                    $pdf->CellFitScale(50, 6, "$definizione_dirigente(*)", 0, 1, "C");
                }
                else {
                    $pdf->CellFitScale(50, 6, "$definizione_dirigente", 0, 1, "C");
                }
            }
            $pdf->SetFont('helvetica', 'B', $font_size_2);
            $pdf->CellFitScale(140, 0, "", 0, 0, "C");
            $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, "C");

            // Firma omessa
            if ($stampa_firma_omessa == 'SI')
            {
                $pdf->SetFont('helvetica', '', 7);
                $pdf->CellFitScale(135, 0, "", 0, 0, "C");
                $pdf->CellFitScale(55, 0, "Firma omessa ai sensi dell'art. 3, D.to Lgs. 12.02.1993, n. 39", 0, 1, "R");
            }

            if ($stampa_nota_documento == 'SI')
            {
                $pdf->SetFont('helvetica', '', 8);
                $pdf->ln(14);
                $pdf->MultiCell(0, 0, $labels['nota_documento_digitale'], 0, "L", false, 1);
            }

            if ($trentino_abilitato == 'SI')
            {
                $pdf->SetFont('helvetica', '', $font_size_2);
                $pdf->ln(6);
                //legenda
                $html = "<p>1) Il presente certificato ha validità nazionale</p>

                                <p><b>2) Livelli relativi all'acquisizione delle competenze di ciascun asse:</b></p>

                                <p><b>Livello base:</b> lo studente svolge compiti semplici in situazione note, mostrando di possedere conoscenze ed abilità essenziali e di saper applicare regole e procedure fondamentali.</p>

                                <p>Nel caso in cui non sia stato raggiunto il livello base, è riportata l'espressione <b>\"Livello base non raggiunto\".</b></p>

                                <p><b>Livello intermedio:</b> lo studente svolge compiti e risolve problemi complessi in situazioni note, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.</p>

                                <p><b>Livello avanzato:</b> lo studente svolge compiti e problemi complessi in situazioni anche non note, mostrando padronanza nell'uso delle conoscenze e delle abilità. Es. proporre e sostenere le proprie
                                opinioni e assumere autonomamente decisioni consapevoli.</p>";
                $pdf->writeHTML($html, true, false, true, false, '');
            }
            //}}} </editor-fold>

            //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 3">
            if ($trentino_abilitato != 'SI')
            {
                if ($stampa_compatta != 'SI') {
                    $pdf->AddPage('P');
                }
                $x_base = $pdf->getX();

                $html = "<p>(1) Il presente certificato ha <b>validità nazionale</b></p>

                        <p>(2) livelli relativi all'acquisizione delle competenze di ciascun asse:</p>

                        <p><b>LIVELLO BASE:</b> lo studente svolge compiti semplici in situazione note, mostrando di possedere conoscenze ed abilità essenziali e di saper applicare regole e procedure fondamentali.<br>
                        <i>Nel caso in cui non sia stato raggiunto il livello base, è riportata l'espressione <b>\"Livello base non raggiunto\"</b>, con l'indicazione della relativa motivazione.</p>

                        <p><b>LIVELLO INTERMEDIO:</b> lo studente svolge compiti e risolve problemi complessi in situazioni note, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.</p>

                        <p><b>LIVELLO AVANZATO:</b> lo studente svolge compiti e problemi complessi in situazioni anche non note, mostrando padronanza nell'uso delle conoscenze e delle abilità.</p>

                        <p>(3) Specificare la <b>prima lingua straniera studiata</b></p>";

                if ($stampa_compatta == 'SI') {
                    $pdf->SetFont('helvetica', '', $font_size_2-1);
                    $html = str_replace(['<p>', '</p>'], '<br>', $html);
                    $pdf->setCellHeightRatio(1);
                    $pdf->writeHTML($html, true, false, true, false, '');
                    $pdf->setCellHeightRatio(1.25);
                }
                else {
                    $pdf->SetFont('helvetica', '', 12);
                    $pdf->writeHTML($html, true, false, true, false, '');
                }
            }
            //}}} </editor-fold>
            }
        }
        //}}} </editor-fold>
    }
}


switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori
        $doc_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF;
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);

            if ($da_includere != "NO")
            {
                $file_name = $tipo_stampa . '.pdf';
                $doc = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($doc)) {
                    unlink($doc);
                }
                $pdf->Output($doc, "F");

                $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
                $content = file_get_contents($doc);

                // NOTA: presume vi sia al max un solo file per i criteri di ricerca
                if ($file[0]['id']) {
                    messengerUpdateFile($file[0]['id'], $content);
                } else {
                    // Destinatari: Studente + genitori
                    $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                    messengerSaveFile([
                        'content'    => $content,
                        'hidden'     => false,
                        'mime'       => 'application/pdf',
                        'name'       => $file_name,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year'   => "{$anno_inizio}/{$anno_fine}",
                            'period' => 'finale'
                        ],
                        'recipients' => $recipients,
                        'tags'       => ['PAGELLE']
                    ]);
                }

                if (file_exists($doc)) {
                    $doc_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                    unlink($doc);
                }
            }
        }

        if (empty($doc_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $doc_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF;
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);
            if ($da_includere != "NO") {
                $pdf->Output($file, "F");
            }
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF;
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
