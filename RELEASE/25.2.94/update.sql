-- cancella gli abbinamenti precedentemente inseriti per la religione standard superiori per il periodo trimestre
WITH template_cte AS (
    SELECT id_template
    FROM template_word
    WHERE nome_file = 'IIGradoReligioneAlternativa.docx'
    LIMIT 1
),
classi AS (
    SELECT id_classe
    FROM classi_complete
    WHERE ordinamento = '0'
      AND tipo_indirizzo IN ('0', '1', '2', '3', '5')
      AND id_codice_ministeriale != 94
)
DELETE FROM abbinamenti_template_word_pagelle_classi aw
USING template_cte t, classi c
WHERE aw.id_template = t.id_template
  AND aw.id_classe = c.id_classe
  AND aw.periodo = '8'
  AND aw.tipo_abbinamento = 'NORMALE';



--
-- Aggiunta entrata dei template pagella standard delle superiori (A3 e A4), di default non attive (flag_can valorizzato)
--
INSERT INTO template_word (nome_file,estensione,locazione,tipo,ordinamento,nome,parametri_personalizzati, flag_canc) VALUES ('IIGradoStandardA3.docx','docx','PAGELLE','singolo',1,'Pagella scuola Secondaria II grado - A3','{"sezione_stampa":"G","destinazione_pubblicazione":"PAGELLE","parametri":[{"nome":"data_stampa_","label":"Data di stampa","tipo":"date"},{"nome":"data_stampa_finale_","label":"Data di stampa finale","tipo":"date"},{"nome":"tipologia_scuola","label":"Tipologia della scuola","tipo":"select","valori":{"default":"Statale","opzioni":[{"label":"Statale","valore":"Statale"},{"label":"Paritaria","valore":"Paritaria"}]}},{"nome":"filtro_studenti_scrutinio","label":"Filtro gli studenti per scrutinio","tipo":"select","valori":{"default":"TUTTI","opzioni":[{"label":"Tutti","valore":"TUTTI"},{"label":"Almeno un voto negativo","valore":"NEGATIVO"}]}},{"nome":"filtro_materie","label":"Quali materie stampare","tipo":"select","valori":{"default":"NO_FILTRO","opzioni":[{"label":"Tutte","valore":"NO_FILTRO"},{"label":"Solo con voti\/competenze","valore":"SOLO_VOTI"}]},"selected":true},{"nome":"filtro_giudizio_sospeso","label":"Filtro degli studenti sospesi","tipo":"select","valori":{"default":"TUTTI","opzioni":[{"label":"Di tutti","valore":"TUTTI"},{"label":"Solo di quelli che hanno risolto il giudizio sospeso che sono stati poi promossi","valore":"SOSPESI_PROMOSSI"},{"label":"Solo di quelli che hanno risolto il giudizio sospeso che non sono stati poi promossi","valore":"SOSPESI_BOCCIATI"},{"label":"Solo di quelli che NON hanno avuto il giudizio sospeso","valore":"NON_SOSPESI"},{"label":"Solo di quelli che HANNO il giudizio in sospeso","valore":"SOLO_SOSPESI"}]},"selected":false},{"nome":"quanti_studenti_stampare","label":"Quanti studenti stampare (solo per nexus)","tipo":"select","valori":{"default":"-1","opzioni":[{"label":"Tutti","valore":"-1"},{"label":"1","valore":"1"},{"label":"2","valore":"2"},{"label":"3","valore":"3"},{"label":"4","valore":"4"},{"label":"5","valore":"5"},{"label":"6","valore":"6"},{"label":"7","valore":"7"},{"label":"8","valore":"8"},{"label":"9","valore":"9"},{"label":"10","valore":"10"},{"label":"11","valore":"11"},{"label":"12","valore":"12"},{"label":"13","valore":"13"},{"label":"14","valore":"14"},{"label":"15","valore":"15"},{"label":"16","valore":"16"},{"label":"17","valore":"17"},{"label":"18","valore":"18"},{"label":"19","valore":"19"},{"label":"20","valore":"20"},{"label":"21","valore":"21"},{"label":"22","valore":"22"},{"label":"23","valore":"23"},{"label":"24","valore":"24"},{"label":"25","valore":"25"},{"label":"26","valore":"26"},{"label":"27","valore":"27"},{"label":"28","valore":"28"},{"label":"29","valore":"29"},{"label":"30","valore":"30"}]},"limiti":"superutente","selected":true}]}', 1);
INSERT INTO template_word (nome_file,estensione,locazione,tipo,ordinamento,nome,parametri_personalizzati, flag_canc) VALUES ('IIGradoStandardA4.docx','docx','PAGELLE','singolo',2,'Pagella scuola Secondaria II grado - A4','{"sezione_stampa":"G","destinazione_pubblicazione":"PAGELLE","parametri":[{"nome":"data_stampa_","label":"Data di stampa","tipo":"date"},{"nome":"data_stampa_finale_","label":"Data di stampa finale","tipo":"date"},{"nome":"tipologia_scuola","label":"Tipologia della scuola","tipo":"select","valori":{"default":"Statale","opzioni":[{"label":"Statale","valore":"Statale"},{"label":"Paritaria","valore":"Paritaria"}]}},{"nome":"filtro_studenti_scrutinio","label":"Filtro gli studenti per scrutinio","tipo":"select","valori":{"default":"TUTTI","opzioni":[{"label":"Tutti","valore":"TUTTI"},{"label":"Almeno un voto negativo","valore":"NEGATIVO"}]}},{"nome":"filtro_materie","label":"Quali materie stampare","tipo":"select","valori":{"default":"NO_FILTRO","opzioni":[{"label":"Tutte","valore":"NO_FILTRO"},{"label":"Solo con voti\/competenze","valore":"SOLO_VOTI"}]},"selected":true},{"nome":"filtro_giudizio_sospeso","label":"Filtro degli studenti sospesi","tipo":"select","valori":{"default":"TUTTI","opzioni":[{"label":"Di tutti","valore":"TUTTI"},{"label":"Solo di quelli che hanno risolto il giudizio sospeso che sono stati poi promossi","valore":"SOSPESI_PROMOSSI"},{"label":"Solo di quelli che hanno risolto il giudizio sospeso che non sono stati poi promossi","valore":"SOSPESI_BOCCIATI"},{"label":"Solo di quelli che NON hanno avuto il giudizio sospeso","valore":"NON_SOSPESI"},{"label":"Solo di quelli che HANNO il giudizio in sospeso","valore":"SOLO_SOSPESI"}]},"selected":false},{"nome":"quanti_studenti_stampare","label":"Quanti studenti stampare (solo per nexus)","tipo":"select","valori":{"default":"-1","opzioni":[{"label":"Tutti","valore":"-1"},{"label":"1","valore":"1"},{"label":"2","valore":"2"},{"label":"3","valore":"3"},{"label":"4","valore":"4"},{"label":"5","valore":"5"},{"label":"6","valore":"6"},{"label":"7","valore":"7"},{"label":"8","valore":"8"},{"label":"9","valore":"9"},{"label":"10","valore":"10"},{"label":"11","valore":"11"},{"label":"12","valore":"12"},{"label":"13","valore":"13"},{"label":"14","valore":"14"},{"label":"15","valore":"15"},{"label":"16","valore":"16"},{"label":"17","valore":"17"},{"label":"18","valore":"18"},{"label":"19","valore":"19"},{"label":"20","valore":"20"},{"label":"21","valore":"21"},{"label":"22","valore":"22"},{"label":"23","valore":"23"},{"label":"24","valore":"24"},{"label":"25","valore":"25"},{"label":"26","valore":"26"},{"label":"27","valore":"27"},{"label":"28","valore":"28"},{"label":"29","valore":"29"},{"label":"30","valore":"30"}]},"limiti":"superutente","selected":true}]}', 1);

--
-- Aggiunge gli abbinamenti necessari alla pagella standard delle superiori a tutte le classi superiori, per i periodi
-- quadrimestre e finale
--
-- abbinamenti per modello in A3
WITH template_cte AS (
    SELECT id_template
    FROM template_word
    WHERE nome_file = 'IIGradoStandardA3.docx'
    LIMIT 1
),
classi AS (
    SELECT id_classe
    FROM classi_complete
    WHERE ordinamento = '0'
      AND tipo_indirizzo IN ('0', '1', '2', '3', '5')
      AND id_codice_ministeriale != 94
),
periodi AS (
    SELECT 7 AS periodo
    UNION ALL
    SELECT 9
)
INSERT INTO abbinamenti_template_word_pagelle_classi (
    id_template, id_classe, periodo, tipo_abbinamento
)
SELECT
    t.id_template,
    c.id_classe,
    p.periodo,
    'NORMALE'
FROM
    template_cte t,
    classi c,
    periodi p;
-- abbinamenti per modello in A4
WITH template_cte AS (
    SELECT id_template
    FROM template_word
    WHERE nome_file = 'IIGradoStandardA4.docx'
    LIMIT 1
),
classi AS (
    SELECT id_classe
    FROM classi_complete
    WHERE ordinamento = '0'
      AND tipo_indirizzo IN ('0', '1', '2', '3', '5')
      AND id_codice_ministeriale != 94
),
periodi AS (
    SELECT 7 AS periodo
    UNION ALL
    SELECT 9
)
INSERT INTO abbinamenti_template_word_pagelle_classi (
    id_template, id_classe, periodo, tipo_abbinamento
)
SELECT
    t.id_template,
    c.id_classe,
    p.periodo,
    'NORMALE'
FROM
    template_cte t,
    classi c,
    periodi p;

--
-- Attiva il template pagella standard delle superiori se la standard pdf è attiva sulla scuola,
-- e cancella quella pdf
--
UPDATE template_word
SET flag_canc = 0
WHERE (nome_file = 'IIGradoStandardA3.docx' or 
  nome_file = 'IIGradoStandardA4.docx')
  AND flag_canc > 0
  AND EXISTS (
    SELECT 1
    FROM elenco_stampe_personalizzate
    WHERE valore = 'PAGELLA_AS2012'
      AND flag_canc = 0
);
update elenco_stampe_personalizzate set flag_canc=999 where valore='PAGELLA_AS2012' and flag_canc=0;
