<?php

/**
 * Smarty plugin
 * -------------------------------------------------------------
 *
 * -------------------------------------------------------------
 */
require_once(SMARTY_PLUGINS_DIR . 'function.html_select_date.php');

// function scrivi_riepilogo_competenze_materia(&$html, $elenco_competenze)
// {
//     foreach ($elenco_competenze as $competenza) {
//         $html .= "\t" . "\t" . "\t" . "\t" . '<tr style="font-weight: normal; border-top: 1px dotted gray;">' . "\n";
//         $html .= "\t" . "\t" . "\t" . "\t" . "\t" . '<td class="padding_cella_generica"><b>' . $competenza['codice'] . '</b> (' . $competenza['descrizione'] . ')</td>' . "\n";
//         $html .= "\t" . "\t" . "\t" . "\t" . "\t" . '<td class="padding_cella_generica" style="white-space: nowrap; width: 100px;">Media: <b>' . $competenza['media'] . '</b></td>' . "\n";
//         $html .= "\t" . "\t" . "\t" . "\t" . '</tr>' . "\n";
//     }
// }

function smarty_function_mastercom_grid_detail_student_report($params, $content) {

    global $anno_inizio, $anno_fine, $trentino_abilitato;

    extract($params);

    $html = '';
    $id_materia_condotta = null;

    $array_lingue_straniere = [
        'INGLESE',
        'INGLESE POTENZIATO',
        'FRANCESE',
        'SPAGNOLO',
        'TEDESCO',
        'RUSSO',
        'ARABO',
        'CINESE',
        'EBRAICO',
        'GIAPPONESE',
        'ALBANESE',
        'SLOVENO',
        'SERBO-CROATO',
        'NEOGRECO',
        'PORTOGHESE'
    ];

    // funzione javascript per fare il salvataggio dei dati
    $html .= "<script>
        function verificaObbligatorietaValore(){
            var selects = document.querySelectorAll('select[data-obbligatorio]');
            var inputs = document.querySelectorAll('input[data-obbligatorio]');
            var valid = true;

            selects.forEach(function(select) {
                var relatedInput = document.querySelector('select[name=\"' + select.getAttribute('data-obbligatorio') + '\"]');
                if (relatedInput && relatedInput.value !== '' && select.value === '') {
                    valid = false;
                    select.style.borderColor = 'red';
                    select.style.borderWidth = '2px';
                } else {
                    select.style.borderColor = '';
                    select.style.borderWidth = '';
                }
            });

            inputs.forEach(function(input) {
                var relatedSelect = document.querySelector('select[name=\"' + input.getAttribute('data-obbligatorio') + '\"]');
                if (relatedSelect && relatedSelect.value !== '' && input.value === '') {
                    valid = false;
                    input.style.borderColor = 'red';
                    input.style.borderWidth = '2px';
                } else {
                    input.style.borderColor = '';
                    input.style.borderWidth = '';
                }
            });

            return valid;
        }

        function salvaPagellina(btn){
            if (!verificaObbligatorietaValore()) {
                alert('Compilare tutti i campi obbligatori delle competenze');
                return;
            }
            btn.disabled=true;
            btn.parentNode.appendChild(Object.assign(document.createElement('input'),{type:'hidden',name:'torna_a',value:'studente'}));
            btn.form.submit();
        }

        function salvaPagellinaTornaTabellone(btn){
            if (!verificaObbligatorietaValore()) {
                alert('Compilare tutti i campi obbligatori delle competenze');
                return;
            }
            btn.disabled=true;
            btn.form.submit();
        }

        function checkCondotta6(select) {
            if (select.value == 6) {
                alert('ATTENZIONE! \\n Con valutazione pari a sei decimi allo studente verrà assegnata la sospensione del giudizio.');
            }
        }
    </script>";

    if (is_array($mat_studenti) && is_array($mat_materie)) {
        $tipo_visualizzazione_voto = 'codice';
        $studente = $mat_studenti[0];

        if (stripos($_SERVER['HTTP_USER_AGENT'], 'msie')) {
            $pref_classe = 'ie_';
            $browser = 'ie';
        } else {
            $pref_classe = '';
            $browser = 'moz';
        }

        if ($alto_contrasto == 'SI') {
            $pref_classe .= 'hc_';
        }

        if (!($schermo > 0)) {
            $schermo = 1024;
        }

        $width_nomi = 250;

        if ($browser == 'ie') {
            $width_nomi = $width_nomi - (5);
        }

        if ($schermo <= 800) {
            if ($alto_contrasto == 'NO') {
                $font_size = 8;
                $row_height = 25;
            } else {
                $font_size = 10;
                $row_height = 27;
            }
        } elseif ($schermo > 800 && $schermo <= 1024) {
            if ($alto_contrasto == 'NO') {
                $font_size = 8;
                $row_height = 25;
            } else {
                $font_size = 10;
                $row_height = 27;
            }
        } elseif ($schermo > 1024 && $schermo <= 1280) {
            if ($alto_contrasto == 'NO') {
                $font_size = 10;
                $row_height = 27;
            } else {
                $font_size = 12;
                $row_height = 29;
            }
        } elseif ($schermo > 1280) {
            if ($alto_contrasto == 'NO') {
                $font_size = 10;
                $row_height = 27;
            } else {
                $font_size = 14;
                $row_height = 29;
            }
        }
        $font_size = 16;

        switch ($periodo) {
            case '1':
                $nome_pagella = '1a pagellina infraquadrimestrale';
                break;
            case '2':
                $nome_pagella = '2a pagellina infraquadrimestrale';
                break;
            case '3':
                $nome_pagella = '3a pagellina infraquadrimestrale';
                break;
            case '4':
                $nome_pagella = '4a pagellina infraquadrimestrale';
                break;
            case '5':
                $nome_pagella = '5a pagellina infraquadrimestrale';
                break;
            case '6':
                $nome_pagella = '6a pagellina infraquadrimestrale';
                break;
            case '7':
                $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
                break;
            case '8':
                $nome_pagella = 'Pagella fine 2o trimestre';
                break;
            case '9':
                $nome_pagella = 'Pagella fine anno';
                break;
            case '10':
                $nome_pagella = 'Prove strutturate';
                break;
            case '11':
                $nome_pagella = 'Esami di licenza maestro d\'arte';
                break;
            case '21':
                $nome_pagella = '1a pagellina infraquadrimestrale';
                break;
            case '22':
                $nome_pagella = '2a pagellina infraquadrimestrale';
                break;
            case '23':
                $nome_pagella = '3a pagellina infraquadrimestrale';
                break;
            case '24':
                $nome_pagella = '4a pagellina infraquadrimestrale';
                break;
            case '25':
                $nome_pagella = '5a pagellina infraquadrimestrale';
                break;
            case '26':
                $nome_pagella = '6a pagellina infraquadrimestrale';
                break;
            case '27':
                $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
                break;
            case '28':
                $nome_pagella = 'Pagella fine 2o trimestre';
                break;
            case '29':
                $nome_pagella = 'Pagella fine anno';
                break;
        }

        $peso_voti = estrai_parametri_singoli("PESO_VOTI", $dati_classe['id_classe'], 'classe');
        if($peso_voti == "SI")
        {
            $peso_voti='PONDERATA';
        }
        else
        {
            $peso_voti='ARITMETICA';
        }

        $data_inizio_calcolo_voti = estrai_parametri_singoli("DATA_INIZIO_CALCOLO_VOTI", $dati_classe['id_classe'], 'classe');
        $data_fine_calcolo_voti = estrai_parametri_singoli("DATA_FINE_CALCOLO_VOTI", $dati_classe['id_classe'], 'classe');
        $giudizio_sospeso_6_in_condotta = estrai_parametri_singoli('ABILITA_GIUDIZIO_SOSPESO_6_CONDOTTA');

        $nome_pagella .= ' della classe ' . $dati_classe['classe'] . ' ' . $dati_classe['sezione'] . ' ' . $dati_classe['codice']
                . ' per lo studente: <br><b>' . $studente['cognome'] . ' ' . $studente['nome'] . '</b>';

        $mat_chiusura_scrutini = explode('@', $dati_classe['blocco_scrutini']);

        foreach ($mat_chiusura_scrutini as $singolo_periodo_chiusura) {
            $mat_singolo_periodo = explode('#', $singolo_periodo_chiusura);

            if ($mat_singolo_periodo[0] == $periodo) {
                $stato_chiusura_scrutini = $mat_singolo_periodo[1];
            }
        }

        $html .= "<form method='post'>\n"
                . "<div id='div_main' width='100%' class='div_" . $pref_classe . "tabellone_main'>\n";

        $html .= '<table width=\'100%\'>'
                . '<tr>'
                . '<td align=\'center\'> ' . $nome_pagella
                . '</td>'
                . '</tr>'
                . '<tr><td><br></td></tr>'
                . '</table>';
        $row_start = $row_height * 2;

//        $bgcolor = "#91E3DC";
        $bgcolor = "#bfdaff";

        $border_color = "#544012";
        $html .= '<table width="100%" style="font-size: ' . $font_size . ';font-weight: bold;" border=\'1\'>' . "\n";

        $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';" style="font-weight: bold;">' . "\n"
                . "\t\t" . '<td align="center" style="font-size: ' . $font_size . ';font-weight: bold;">Materia' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-right: 0px; font-size: ' . $font_size . '; min-width: 80px;\'>Media' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-right: 0px; font-size: ' . $font_size . '; min-width: 80px;\'>Proposta' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-left: 0px; border-right: 0px; font-size: ' . $font_size . ';\'>' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-left: 0px; font-size: ' . $font_size . '; min-width: 80px;\'>Voto' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" colspan=\'2\' style="font-size: ' . $font_size . ';font-weight: bold;">Ore (ore:min)' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" colspan=\'2\' style="font-size: ' . $font_size . ';font-weight: bold;">Recuperi' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '</tr>' . "\n";


        $fill = true;

        //{{{ <editor-fold defaultstate="collapsed" desc="riordino array per lettura verticale">
        list($parte1, $parte2) = array_chunk($mat_campi_liberi, ceil(count($mat_campi_liberi) / 2));
        $nuovo_array_base = [];
        $cont_campi = 0;
        foreach ($parte1 as $key => $value)
        {
            $nuovo_array_base[$cont_campi] = $parte1[$key];
            $cont_campi++;
            if ($parte2[$key])
            {
                $nuovo_array_base[$cont_campi] = $parte2[$key];
                $cont_campi++;
            }
        }
        $mat_campi_liberi = $nuovo_array_base;
        //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="Script">
        // Script per pulsante Copia e maschera di ore e minuti
        $html .= "<script>
                    function copiaValori(nome_classe, nome_textbox, id_materia)
                    {
                        var testo = '';
                        var len = $(\".\"+nome_classe).length;
                        for (var i = 0; i < len; i++)
                        {

                            if ($(\".\"+nome_classe)[i].selectedOptions[0].text != '---')
                            {
                                testo += $(\".\"+nome_classe)[i].selectedOptions[0].text + ' ';
                            }
                        }
                        document.getElementsByName(nome_textbox)[0].value = testo;
                        document.getElementById(\"modificato_\" + id_materia).value='SI';
                    }

                    function abilitaAreaTesto(valore_select, area_testo)
                    {
                        if (valore_select == -1)
                        {
                            area_testo.disabled = false;
                        }
                        else
                        {
                            area_testo.disabled = true;
                        }
                    }

                    $(document).ready(function () {
                                // Create jqxMaskedInputs
                                $(\".hourInput\").jqxMaskedInput({mask: '####:##'});
                            });
                </script>";
        //}}} </editor-fold>

        $abilita_pulsante_salva = false;

        //{{{ <editor-fold defaultstate="collapsed" desc="blocco recuperi da metà a gosto a metà settembre"
        $data = time();
        $data_limite_agosto = mktime(0, 0, 0, 8, 15, date('Y', $data));
        $data_limite_settembre = mktime(23, 59, 59, 9, 15, date('Y', $data));
        if (($data >= $data_limite_agosto) && ($data <= $data_limite_settembre) && in_array($periodo, ['9', '29']))
        {
            $blocco_recuperi = true;
        }
        else
        {
            $blocco_recuperi = false;
        }
        //}}} </editor-fold>

        $media_array_voti = [];
        $somma_voti = [];

        foreach ($mat_materie as $indice_materia => $materia)
        {
            //{{{ <editor-fold defaultstate="collapsed">
            if (is_numeric($indice_materia))
            {
                $fill = !$fill;
                if ($fill)
                {
//                    $bgcolor = "#91E3DC";
                    $bgcolor = "#bfdaff";
                } else {
//                    $bgcolor = "#66BADC";
                    $bgcolor = "#afc4ff";
                }

                // Definisco il tipo di voto da utilizzare
                if (in_array($materia['cpm_scritto'], ['0','1'] ))
                {
                    $vis_scritto = $materia['cpm_scritto'];
                }
                else
                {
                    $vis_scritto = $materia['scritto'];
                }

                if (in_array($materia['cpm_orale'], ['0','1'] ))
                {
                    $vis_orale = $materia['cpm_orale'];
                }
                else
                {
                    $vis_orale = $materia['orale'];
                }

                if (in_array($materia['cpm_pratico'], ['0','1'] ))
                {
                    $vis_pratico = $materia['cpm_pratico'];
                }
                else
                {
                    $vis_pratico = $materia['pratico'];
                }

                $html .= "\t" . '<tr style="background-color:' . $bgcolor . '; border-top: 1px solid black;">' . "\n";
                $html .= '<td style=\'padding-left: 5px; font-size: ' . $font_size . ';\'>' . $materia['nome_materia_breve'] . '</td>';
                $cont_row++;

                // Studente esonerato da religione
                if ($materia['tipo_materia'] == 'RELIGIONE' and $studente['esonero_religione'] == 1)
                {
                    $html .= '<td colspan=\'8\' align=\'center\' style=\'font-size: ' . $font_size . ';\'>--Esonerato--</td>';
                }
                else
                {
                    //{{{ <editor-fold defaultstate="collapsed" desc="materia normale">
                    $voto_modificabile = (
                                            (
                                                $form_stato == 'amministratore'
                                                or
                                                (
                                                    $coordinatore == 'SI'
                                                    and
                                                    $dati_classe['consiglio_classe_attivo'] == 'SI'
                                                    or
                                                    ($materia['codice'] == 'CIV' || strpos($materia['descrizione'], 'CIVICA') == true)
                                                )
                                            )
                                            and
                                            $stato_chiusura_scrutini != 'SI'
                                        );
                    $proposta_modificabile = (
                                                (
                                                    in_array($current_user, $materia['professori'])
                                                    and
                                                    $dati_classe['consiglio_classe_attivo'] == 'NO'
                                                    or
                                                    ($materia['codice'] == 'CIV' || strpos($materia['descrizione'], 'CIVICA') == true)
                                                )
                                                and
                                                $stato_chiusura_scrutini != 'SI'
                                            );

                    if ($voto_modificabile || $proposta_modificabile)
                    {
                        $abilita_pulsante_salva = true;
                    }

                    //{{{ <editor-fold defaultstate="collapsed" desc="Tabella generata">
                    if (
                            ($tipo_visualizzazione == 'voto_singolo')
                            or ( $tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '1')
                        )
                    {
                        // Voto Unico
                        //{{{ <editor-fold defaultstate="collapsed">
                        // Inizio inserimento proposta
                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>';
                        $valore_media = calcola_media_voti_studente_singola_materia($studente['id_studente'], $materia['id_materia'], $data_inizio_calcolo_voti, $data_fine_calcolo_voti, 'XX', 2, "NO", '', $peso_voti);
                        if(intval($valore_media['totale']) == 0)
                        {
                            $valore_media['totale'] = '--';
                        }
                        $html .= $valore_media['totale'] . '</td>';

                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>';
                        if ($proposta_modificabile)
                        {
                            $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_pagellina_' . $materia['id_materia'] . '" ' . "\n";
                            $html .= "\t\t" . ' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\'; ' . "\n";
                            if ($materia['tipo_materia'] == 'CONDOTTA'
                                && $trentino_abilitato != 'SI'
                                && !in_array($dati_classe["tipo_indirizzo"], [4,6,7,8])
                                && in_array($periodo, ['9'])
                                && $giudizio_sospeso_6_in_condotta == 'SI'){
                                $html .= "\t\t" . ' checkCondotta6(this); ' . "\n";
                            }
                            $html .= "\t" . ' ">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                            {
                                                $media_array_voti[$materia['id_materia']]['proposta']['unico'] = $significato_voto['voto'];
                                                $somma_voti['somma_voti']['proposta']['unico'] += $significato_voto['voto'];
                                                $somma_voti['cont_voti']['proposta']['unico']++;
                                            }
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
//                                        if ($tipo_visualizzazione_voto == 'codice') {
//                                            $significato_voto_tradotto = $significato_voto['codice'];
//                                        } else {
//                                            $significato_voto_tradotto = $significato_voto['valore'];
//                                        }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        if ($materia['tipo_materia'] == 'RELIGIONE'){
                                            $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['codice'] . ')</option>' . "\n";
                                        }else{
                                            $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                        }
                                    }
                                }
                                $html .= "\t" . '</select>' . "\n";
                        }
                        else
                        {
                            if ($materia['tipo_materia'] == 'RELIGIONE'){
                                if (is_array($materia['schema_voti'])) {
                                    $significato_voto_tradotto = '';
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] == $significato_voto['voto']) {
                                            $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                            $html .= $significato_voto_tradotto . "\n";
                                            break;
                                        }
                                    }
                                }else{
                                    $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] . "\n";
                                }
                            }else{
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] . "\n";
                            }
                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                            {
                                $media_array_voti[$materia['id_materia']]['proposta']['unico'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'];
                                $somma_voti['somma_voti']['proposta']['unico'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'];
                                $somma_voti['cont_voti']['proposta']['unico']++;
                            }
                        }
                        $html .= '</td>';

                        // Legenda voti
                        $html .= '<td align=\'center\' style=\'border-left: 0px; border-right: 0px; font-size: ' . $font_size . '; font-weight: normal;\'>'
                                . 'Unico'
                                . '</td>' . "\n";

                        // Inizio inserimento voto
                        $html .= '<td align=\'center\' style=\'padding: 3px 0px 3px 0px; border-left: 0px; font-size: ' . $font_size . '\'>';
                        if ($voto_modificabile)
                        {
                            $html .= "\t" . '<select style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_pagellina_' . $materia['id_materia'] . "\"\n";
                            $html .= "\t\t" . ' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';' . "\n";
                            if ($materia['tipo_materia'] == 'CONDOTTA'
                                && $trentino_abilitato != 'SI'
                                && !in_array($dati_classe["tipo_indirizzo"], [4,6,7,8]) && in_array($periodo, ['9'])
                                && $giudizio_sospeso_6_in_condotta == 'SI'){
                                $html .= "\t\t" . ' checkCondotta6(this); ' . "\n";
                            }
                            $html .= "\t\t" . ' ' . "\n";
                            $html .= "\t\t" . ' ">' . "\n";
                            if (is_array($materia['schema_voti'])) {
                                $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                foreach ($materia['schema_voti'] as $significato_voto) {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] == $significato_voto['voto']) {
                                        $html .= "\t" . "\t" . '<option selected ';
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                        {
                                            $media_array_voti[$materia['id_materia']]['voto']['unico'] = $significato_voto['voto'];
                                            $somma_voti['somma_voti']['voto']['unico'] += $significato_voto['voto'];
                                            $somma_voti['cont_voti']['voto']['unico']++;
                                        }

                                    } else {
                                        $html .= "\t" . "\t" . '<option ';
                                    }
//                                    if ($tipo_visualizzazione_voto == 'codice') {
//                                        $significato_voto_tradotto = $significato_voto['codice'];
//                                    } else {
//                                        $significato_voto_tradotto = $significato_voto['valore'];
//                                    }
                                    $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                    if ($materia['tipo_materia'] == 'RELIGIONE'){
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['codice'] . ')</option>' . "\n";
                                    }else{
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                            }
                            $html .= "\t" . '</select>' . "\n";
                        }
                        else
                        {
                            if ($materia['tipo_materia'] == 'RELIGIONE'){
                                if (is_array($materia['schema_voti'])) {
                                    $significato_voto_tradotto = '';
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] == $significato_voto['voto']) {
                                            $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                            $html .= $significato_voto_tradotto . "\n";
                                            break;
                                        }
                                    }
                                }else{
                                    $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] . "\n";
                                }
                            }else{
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] . "\n";
                            }
                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                            {
                                $media_array_voti[$materia['id_materia']]['voto']['unico'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'];
                                $somma_voti['somma_voti']['voto']['unico'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'];
                                $somma_voti['cont_voti']['voto']['unico']++;
                            }
                        }
                        $html .= '</td>' . "\n";
                        //}}} </editor-fold>
                    }
                    else
                    {
                        // Voto multiplo
                        //{{{ <editor-fold defaultstate="collapsed">
                        // Inizio inserimento proposta

                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>';
                        $valore_media = calcola_media_voti_studente_singola_materia($studente['id_studente'], $materia['id_materia'], $data_inizio_calcolo_voti, $data_fine_calcolo_voti, 'XX', 2, "NO", '', $peso_voti);
                        if(intval($valore_media['scritto']) == 0)
                        {
                            $valore_media['scritto'] = '--';
                        }
                        if(intval($valore_media['orale']) == 0)
                        {
                            $valore_media['orale'] = '--';
                        }
                        if(intval($valore_media['pratico']) == 0)
                        {
                            $valore_media['pratico'] = '--';
                        }

                        $dato_medie = '';
                        if ($vis_scritto == 1)
                        {
                            $dato_medie .= $valore_media['scritto'] . '<br>';
                        }
                        if ($vis_orale == 1)
                        {
                            $dato_medie .= $valore_media['orale'] . '<br>';
                        }
                        if ($vis_pratico == 1)
                        {
                            $dato_medie .= $valore_media['pratico'];
                        }
                        $html .= $dato_medie . '</td>';

                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . ';\'>';
                        if ($vis_scritto == 1)
                        {
                            if ($proposta_modificabile)
                            {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_scritto_pagella_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                            {
                                                $media_array_voti[$materia['id_materia']]['proposta']['scritto'] = $significato_voto['voto'];
                                                $somma_voti['somma_voti']['proposta']['scritto'] += $significato_voto['voto'];
                                                $somma_voti['cont_voti']['proposta']['scritto']++;
                                            }
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
//                                        if ($tipo_visualizzazione_voto == 'codice') {
//                                            $significato_voto_tradotto = $significato_voto['codice'];
//                                        } else {
//                                            $significato_voto_tradotto = $significato_voto['valore'];
//                                        }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            }
                            else
                            {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'] . '<br>';
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                {
                                    $media_array_voti[$materia['id_materia']]['proposta']['scritto'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'];
                                    $somma_voti['somma_voti']['proposta']['scritto'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'];
                                    $somma_voti['cont_voti']['proposta']['scritto']++;
                                }
                            }
                        }
                        if ($vis_orale == 1)
                        {
                            if ($proposta_modificabile)
                            {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_orale_pagella_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                            {
                                                $media_array_voti[$materia['id_materia']]['proposta']['orale'] = $significato_voto['voto'];
                                                $somma_voti['somma_voti']['proposta']['orale'] += $significato_voto['voto'];
                                                $somma_voti['cont_voti']['proposta']['orale']++;
                                            }
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
//                                        if ($tipo_visualizzazione_voto == 'codice') {
//                                            $significato_voto_tradotto = $significato_voto['codice'];
//                                        } else {
//                                            $significato_voto_tradotto = $significato_voto['valore'];
//                                        }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            }
                            else
                            {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'] . '<br>';
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                {
                                    $media_array_voti[$materia['id_materia']]['proposta']['orale'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'];
                                    $somma_voti['somma_voti']['proposta']['orale'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'];
                                    $somma_voti['cont_voti']['proposta']['orale']++;
                                }
                            }
                        }
                        if ($vis_pratico == 1 && (
                                                    ($tipo_visualizzazione == 'scritto_orale_pratico')
                                                    ||
                                                    ($tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '3')
                                                ))
                        {
                            if ($proposta_modificabile)
                            {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_pratico_pagella_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                            {
                                                $media_array_voti[$materia['id_materia']]['proposta']['pratico'] = $significato_voto['voto'];
                                                $somma_voti['somma_voti']['proposta']['pratico'] += $significato_voto['voto'];
                                                $somma_voti['cont_voti']['proposta']['pratico']++;
                                            }
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
//                                        if ($tipo_visualizzazione_voto == 'codice') {
//                                            $significato_voto_tradotto = $significato_voto['codice'];
//                                        } else {
//                                            $significato_voto_tradotto = $significato_voto['valore'];
//                                        }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            }
                            else
                            {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'] . '<br>';
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                {
                                    $media_array_voti[$materia['id_materia']]['proposta']['pratico'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'];
                                    $somma_voti['somma_voti']['proposta']['pratico'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'];
                                    $somma_voti['cont_voti']['proposta']['pratico']++;
                                }
                            }
                        }
                        $html .= "</td>";

                        // Legenda voti
                        $html .= '<td align=\'center\' style=\'border-left: 0px; border-right: 0px; font-size: ' . $font_size . '; font-weight: normal;\'>';
                        if ($vis_scritto == 1)
                        {
                            $html .= 'scritto<br>' . "\n";
                        }
                        if ($vis_orale == 1)
                        {
                            $html .= 'orale<br>' . "\n";
                        }
                        if ($vis_pratico == 1 && (
                                                    ($tipo_visualizzazione == 'scritto_orale_pratico')
                                                    ||
                                                    ($tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '3')
                                                ))
                        {
                            $html .= 'pratico<br>' . "\n";
                        }
                        $html .= "</td>";

                        // Inizio inserimento voto
                        $html .= '<td align=\'center\' style=\'padding: 3px 0px 3px 0px; border-left: 0px; font-size: ' . $font_size . ';\'>';
                        if ($vis_scritto == 1)
                        {
                            if ($voto_modificabile)
                            {
                                //$html .= "\t" . '<select  style=\'font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;\' name=\'voto_scritto_pagella_' . $materia['id_materia'] . '\' onchange=\'document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';\'>' . "\n";
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_scritto_pagella_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti']))
                                {
                                    $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto)
                                    {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] == $significato_voto['voto'])
                                        {
                                            $html .= "\t" . "\t" . '<option selected ';
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                            {
                                                $media_array_voti[$materia['id_materia']]['voto']['scritto'] = $significato_voto['voto'];
                                                $somma_voti['somma_voti']['voto']['scritto'] += $significato_voto['voto'];
                                                $somma_voti['cont_voti']['voto']['scritto']++;
                                            }
                                        }
                                        else
                                        {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
//                                        if ($tipo_visualizzazione_voto == 'codice')
//                                        {
//                                            $significato_voto_tradotto = $significato_voto['codice'];
//                                        }
//                                        else
//                                        {
//                                            $significato_voto_tradotto = $significato_voto['valore'];
//                                        }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>';
                            }
                            else
                            {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] . '<br>' . "\n";
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                {
                                    $media_array_voti[$materia['id_materia']]['voto']['scritto'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'];
                                    $somma_voti['somma_voti']['voto']['scritto'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'];
                                    $somma_voti['cont_voti']['voto']['scritto']++;
                                }
                            }
                        }
                        if ($vis_orale == 1)
                        {
                            if ($voto_modificabile)
                            {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_orale_pagella_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti']))
                                {
                                    $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto)
                                    {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] == $significato_voto['voto'])
                                        {
                                            $html .= "\t" . "\t" . '<option selected ';
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                            {
                                                $media_array_voti[$materia['id_materia']]['voto']['orale'] = $significato_voto['voto'];
                                                $somma_voti['somma_voti']['voto']['orale'] += $significato_voto['voto'];
                                                $somma_voti['cont_voti']['voto']['orale']++;
                                            }
                                        }
                                        else
                                        {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
//                                        if ($tipo_visualizzazione_voto == 'codice')
//                                        {
//                                            $significato_voto_tradotto = $significato_voto['codice'];
//                                        }
//                                        else
//                                        {
//                                            $significato_voto_tradotto = $significato_voto['valore'];
//                                        }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>';
                            }
                            else
                            {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] . '<br>' . "\n";
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                {
                                    $media_array_voti[$materia['id_materia']]['voto']['orale'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'];
                                    $somma_voti['somma_voti']['voto']['orale'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'];
                                    $somma_voti['cont_voti']['voto']['orale']++;
                                }
                            }
                        }
                        if ($vis_pratico == 1 && (
                                                    ($tipo_visualizzazione == 'scritto_orale_pratico')
                                                    ||
                                                    ($tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '3')
                                                ))
                        {
                            if ($voto_modificabile)
                            {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1px;" name="voto_pratico_pagella_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti']))
                                {
                                    $html .= "\t" . "\t" . '<option value=""> </option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto)
                                    {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] == $significato_voto['voto'])
                                        {
                                            $html .= "\t" . "\t" . '<option selected ';
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                            {
                                                $media_array_voti[$materia['id_materia']]['voto']['pratico'] = $significato_voto['voto'];
                                                $somma_voti['somma_voti']['voto']['pratico'] += $significato_voto['voto'];
                                                $somma_voti['cont_voti']['voto']['pratico']++;
                                            }
                                        }
                                        else
                                        {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
//                                        if ($tipo_visualizzazione_voto == 'codice')
//                                        {
//                                            $significato_voto_tradotto = $significato_voto['codice'];
//                                        }
//                                        else
//                                        {
//                                            $significato_voto_tradotto = $significato_voto['valore'];
//                                        }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>';
                            }
                            else
                            {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] . '<br>' . "\n";
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] > 0 && $mat_voti[$studente['id_studente']][$materia['id_materia']]['in_media_pagelle'] == 'SI')
                                {
                                    $media_array_voti[$materia['id_materia']]['voto']['pratico'] = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'];
                                    $somma_voti['somma_voti']['voto']['pratico'] += $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'];
                                    $somma_voti['cont_voti']['voto']['pratico']++;
                                }
                            }
                        }
                        $html .= '</td>' . "\n";
                        //}}} </editor-fold>
                    }

                    if ($materia['tipo_materia'] == 'CONDOTTA')
                    {
                        $id_materia_condotta = $materia['id_materia'];

                        if ($trentino_abilitato == 'SI' || in_array($dati_classe["tipo_indirizzo"], [4,6,7,8]) || !in_array($periodo, ['9']) || $giudizio_sospeso_6_in_condotta != 'SI'){
                            $html .= '<td colspan=\'5\'></td>' . "\n";
                        } else {
                            // Esito recuperi per chi ha 6 in condotta per le superiori
                            $html .= '<td colspan=\'2\'></td>' . "\n";
                            $html .= '<td align=\'right\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>'
                                    . 'Esito:'
                                    . '</td>' . "\n"
                                    . "\t" . '<td style=\'border-left: 0px; font-size: ' . $font_size . '\'>';
                            if ($proposta_modificabile || $voto_modificabile)
                            {
                                if (($form_stato == 'professore' && $current_user == $materia['professori'][0]) || $form_stato == 'amministratore' || $coordinatore == 'SI')
                                {
                                    $html .= "\t" . '<select style="font-size: ' . $font_size . ';font-weight: normal; width: 300px; margin: 1;" name="esito_recupero_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";

                                    switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'])
                                    {
                                        case 'SI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NO':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'ASSENTE':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NI">Parziale</option>' . "\n";
                                            break;
                                        default:
                                            $html .= "\t" . "\t" . '<option selected value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                    }
                                    $html .= "\t" . '</select>' . "\n";
                                }
                            } else {
                                switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'])
                                {
                                    case 'SI':
                                        $html .= 'Positivo';
                                        break;
                                    case 'NO':
                                        $html .= 'Negativo';
                                        break;
                                    case 'ASSENTE':
                                        $html .= 'Assente';
                                        break;
                                    case 'NI':
                                        $html .= 'Parziale';
                                        break;
                                    default:
                                        $html .= '------';
                                        break;
                                }
                            }
                            $html .= '</td>' . "\n";
                        }
                    }
                    else
                    {
                        // Inizio inserimento ore
                        //{{{ <editor-fold defaultstate="collapsed">
                        // Assenza
                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '; min-width: 130px;\'>' . "\n";
                        $html .= "\t" .
                                '<input type="hidden"
                                                id="ore_assenza_' . $materia['id_materia'] . '"
                                                name="ore_assenza_' . $materia['id_materia'] . '"
                                                value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza']) . '"
                                                onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';"
                                            >' . "\n";
                        $html .= 'Assenze<br>';
                        if ($proposta_modificabile || $voto_modificabile)
                        {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . 'px" type="text" size="3"
                                                    pattern="[0-9]"
                                                    maxlength="4"
                                                    id="ore_assenza_ore_' . $materia['id_materia'] . '"
                                                    name="ore_assenza_ore_' . $materia['id_materia'] . '"
                                                    value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] / 60) . '"
                                                    onkeyup="jm_integermask(this);"
                                                    onmouseup="jm_integermask(this);"
                                                    onchange="
                                                    document.getElementById(\'ore_assenza_' . $materia['id_materia'] . '\').value
                                                    =
                                                    (parseInt(document.getElementById(\'ore_assenza_ore_' . $materia['id_materia'] . '\').value)*60)
                                                    +
                                                    (parseInt(document.getElementById(\'ore_assenza_min_' . $materia['id_materia'] . '\').value));
                                                    document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';
                                                    "
                                                >' . "\n";
                        }
                        else
                        {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] / 60);
                        }
                        $html .= ':';
                        if ($proposta_modificabile || $voto_modificabile)
                        {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . 'px" type="text" size="3"
                                                    pattern="[0-9]"
                                                    maxlength="2"
                                                    id="ore_assenza_min_' . $materia['id_materia'] . '"
                                                    name="ore_assenza_min_' . $materia['id_materia'] . '"
                                                    value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] % 60) . '"
                                                    onkeyup="jm_integermask(this);"
                                                    onmouseup="jm_integermask(this);"
                                                    onchange="document.getElementById(\'ore_assenza_' . $materia['id_materia'] . '\').value
                                                    =
                                                    (parseInt(document.getElementById(\'ore_assenza_ore_' . $materia['id_materia'] . '\').value)*60)
                                                    +
                                                    (parseInt(document.getElementById(\'ore_assenza_min_' . $materia['id_materia'] . '\').value));
                                                    document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';
                                                    "
                                                >' . "\n";
                        }
                        else
                        {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] % 60);
                        }
                        $html .= '</td>' . "\n";

                        // Monteore
                        $html .= '<td align=\'center\' style=\'border-left: 0px; font-size: ' . $font_size . '; min-width: 130px;\'>' . "\n";
                        $html .= "\t" .
                                '<input type="hidden"
                                                id="monteore_totale_' . $materia['id_materia'] . '"
                                                name="monteore_totale_' . $materia['id_materia'] . '"
                                                value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale']) . '"
                                                onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';"
                                            >' . "\n";
                        $html .= 'Monteore<br>';
                        if ($proposta_modificabile || $voto_modificabile)
                        {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . '" type="text" size="3"
                                                    pattern="[0-9]"
                                                    maxlength="4"
                                                    id="monteore_totale_ore_' . $materia['id_materia'] . '"
                                                    name="monteore_totale_ore_' . $materia['id_materia'] . '"
                                                    value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] / 60) . '"
                                                    onkeyup="jm_integermask(this);"
                                                    onmouseup="jm_integermask(this);"
                                                    onchange="
                                                    document.getElementById(\'monteore_totale_' . $materia['id_materia'] . '\').value
                                                    =
                                                    (parseInt(document.getElementById(\'monteore_totale_ore_' . $materia['id_materia'] . '\').value)*60)
                                                    +
                                                    (parseInt(document.getElementById(\'monteore_totale_min_' . $materia['id_materia'] . '\').value));
                                                    document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';
                                                    "
                                                >' . "\n";
                        }
                        else
                        {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] / 60);
                        }
                        $html .= ':';
                        if ($proposta_modificabile || $voto_modificabile)
                        {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . 'px" type="text" size="3"
                                                    pattern="[0-9]"
                                                    maxlength="2"
                                                    id="monteore_totale_min_' . $materia['id_materia'] . '"
                                                    name="monteore_totale_min_' . $materia['id_materia'] . '"
                                                    value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] % 60) . '"
                                                    onkeyup="jm_integermask(this);"
                                                    onmouseup="jm_integermask(this);"
                                                    onchange="document.getElementById(\'monteore_totale_' . $materia['id_materia'] . '\').value
                                                    =
                                                    (parseInt(document.getElementById(\'monteore_totale_ore_' . $materia['id_materia'] . '\').value)*60)
                                                    +
                                                    (parseInt(document.getElementById(\'monteore_totale_min_' . $materia['id_materia'] . '\').value));
                                                    document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';
                                                    "
                                                >' . "\n";
                        }
                        else
                        {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] % 60);
                        }
                        $html .= '</td>';
                        //}}} </editor-fold>

                        // Inizio inserimento recuperi ed esiti
                        //{{{ <editor-fold defaultstate="collapsed">
                        if ($materia['tipo_materia'] == 'ALTERNANZA')
                        {
                            $html .= '<td colspan=\'2\'></td>' . "\n";
                        }
                        else
                        {
                            $html .= '<td align=\'right\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>'
                                    . 'Tipo:'
                                    . '<br>'
                                    . 'Esito:'
                                    . '</td>' . "\n"
                                    . "\t" . '<td style=\'border-left: 0px; font-size: ' . $font_size . '\'>';
                            if ($proposta_modificabile || $voto_modificabile)
                            {
                                if (!$blocco_recuperi || 1==1)
                                {
                                    $html .= '<select  style="font-size: ' . $font_size . ';font-weight: normal; width: 300px; margin: 1;" name="tipo_recupero_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n"
                                            . "\t" . "\t" . '<option value="">Nessun recupero necessario</option>' . "\n";

                                    foreach ($tipi_recupero as $tipo_recupero)
                                    {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == $tipo_recupero['valore'])
                                        {
                                            $html .= "\t" . "\t" . '<option selected ' . "\n";
                                        }
                                        else
                                        {
                                            $html .= "\t" . "\t" . '<option ' . "\n";
                                        }
                                        $html .= 'value="' . $tipo_recupero['valore'] . '">' . $tipo_recupero['nome'] . '</option>' . "\n";
                                    }
                                    $html .= "\t" . '</select>' . "\n";
                                }
                                else
                                {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '')
                                    {
                                        $html .= '------';
                                    }
                                    else
                                    {
                                        foreach ($tipi_recupero as $tipo_recupero)
                                        {
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == $tipo_recupero['valore'])
                                            {
                                                $html .= $tipo_recupero['nome'];
                                                $html .= '<input type="hidden" name="tipo_recupero_' . $materia['id_materia'] . '" value="' . $mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] . '">';
                                            }
                                        }
                                    }
                                }
                                $html .= "<br>";
                                if (($form_stato == 'professore' && $current_user == $materia['professori'][0]) || $form_stato == 'amministratore' || $coordinatore == 'SI')
                                {
                                    $html .= "\t" . '<select style="font-size: ' . $font_size . ';font-weight: normal; width: 300px; margin: 1;" name="esito_recupero_' . $materia['id_materia'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";

                                    switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'])
                                    {
                                        case 'SI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NO':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'ASSENTE':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NI">Parziale</option>' . "\n";
                                            break;
                                        default:
                                            $html .= "\t" . "\t" . '<option selected value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                    }
                                    $html .= "\t" . '</select>' . "\n";
                                }
                            }
                            else
                            {
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '')
                                {
                                    $html .= '------';
                                }
                                else
                                {
                                    foreach ($tipi_recupero as $tipo_recupero)
                                    {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == $tipo_recupero['valore'])
                                        {
                                            $html .= $tipo_recupero['nome'];
                                        }
                                    }
                                }
                                $html .= '<br>';
                                switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'])
                                {
                                    case 'SI':
                                        $html .= 'Positivo';
                                        break;
                                    case 'NO':
                                        $html .= 'Negativo';
                                        break;
                                    case 'ASSENTE':
                                        $html .= 'Assente';
                                        break;
                                    case 'NI':
                                        $html .= 'Parziale';
                                        break;
                                    default:
                                        $html .= '------';
                                        break;
                                }
                            }
                            $html .= '</td>' . "\n";
                        }
                        //}}} </editor-fold>
                    }
                    //}}} </editor-fold>

                    $html .= "\t" . '</tr>' . "\n";

                    // Campi liberi
                    //{{{ <editor-fold defaultstate="collapsed" desc="Sezione campi liberi">
                    if (
                            (
                                (
                                    is_array($mat_campi_liberi)
                                    and (!empty($mat_campi_liberi))
                                )
                                ||
                                (
                                    is_array($mat_campi_liberi_parenti)
                                    and (!empty($mat_campi_liberi_parenti))
                                )
                            )
                            and (
                                    $dati_classe['livello_abilitazione_campi_liberi'] == 'totale'
                                    or
                                    (
                                        $dati_classe['livello_abilitazione_campi_liberi'] == 'condotta'
                                        and
                                        $materia['tipo_materia'] == 'CONDOTTA'
                                    )
                                )
                    )
                    {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Campi liberi normali">
                        $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                        $cont_row++;

                        $html .= '<td  colspan="9">' . "\n" . "\n";

                        $html .= '<table width="100%" style="font-size: ' . $font_size . ';font-weight: bold">' . "\n";
                        $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                        $html .= "\t" . "\t" . '<td width=\'100%\'>';
                        $html .= '<table width=\'100%\' style="font-size: ' . $font_size . ';font-weight: bold; border-collapse: separate;">' . "\n";
                        $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                        $cont_campi = 1;
                        foreach ($mat_campi_liberi as $key => $campo_libero)
                        {
                            if ($campo_libero['id_materia'] == 0 or $campo_libero['id_materia'] == $materia['id_materia'])
                            {
                                //{{{ <editor-fold defaultstate="collapsed">
                                $html .= "\t" . "\t" . '<td align=\'center\' width=\'50%\' style=\'border: 1px dotted grey; padding: 3px;\'>';
                                $html .= $campo_libero['nome'];
                                $html .= '<br>';
                                if (stripos($campo_libero['tipo_valore'], 'PRECOMPILATO') !== false)
                                {
                                    if ($voto_modificabile || $proposta_modificabile)
                                    {
                                        // Precompilato e precompilato testo modifica
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        if (count($campo_libero['valori_precomp']) > 0)
                                        {
                                            $html .= "\t" . "\t" . "\t" . '<select style=\'width: 98%;\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\'; abilitaAreaTesto(this.value, cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . ');">' . "\n";
                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option value="">---</option>."\n"';
                                            foreach ($campo_libero['valori_precomp'] as $valore_precomp) {
                                                if ($campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                } else {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                }

                                                switch ($campi_liberi_visualizzazione) {
                                                    case 'valore':
                                                        $descrizione_select = $valore_precomp['valore'];
                                                        break;
                                                    case 'codice':
                                                        $descrizione_select = $valore_precomp['codice'];
                                                        break;
                                                    case 'descrizione':
                                                        $descrizione_select = $valore_precomp['descrizione'];
                                                        break;
                                                    default:
                                                        $descrizione_select = $valore_precomp['codice'];
                                                        break;
                                                }

                                                $html .= 'value=\'' . $valore_precomp['id_valore_precomp'] . '\'>' . $descrizione_select . '</option>' . "\n";
                                            }
                                        }
                                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO')
                                        {
                                            if (count($campo_libero['valori_precomp']) > 0)
                                            {
                                                if ($campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] == -1) {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                } else {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                }
                                                $html .= 'value=\'-1\'>Altro</option>' . "\n";
                                            }
                                            else
                                            {
                                                $html .= '<input type=\'hidden\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'-1\'>' . "\n";
                                            }
                                        }

                                        if (count($campo_libero['valori_precomp']) > 0)
                                        {
                                            $html .= "\t" . "\t" . "\t" . '</select>' . "\n";
                                            $html .= "\t" . "\t" . "\t";
                                        }
                                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO')
                                        {
                                            if ($campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] == -1
                                                ||
                                                count($campo_libero['valori_precomp']) == 0
                                                )
                                            {
                                                $disabled = '';
                                            }
                                            else
                                            {
                                                $disabled = 'disabled';
                                            }

                                            if($campo_libero['tipo_precompilato_testo'] == 'textarea')
                                            {
                                                $html .= '<textarea ' . $disabled . ' rows=\'5\' cols='.$campo_libero['dimensione_precompilato_testo'].' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">'.$campo_libero['valore'][$materia['id_materia']]['valore_testuale'] .'</textarea>' . "\n";
                                            }
                                            else
                                            {
                                                $html .= '<input ' . $disabled . ' size=\''.$campo_libero['dimensione_precompilato_testo'].' \' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value="' . $campo_libero['valore'][$materia['id_materia']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                            }
                                        }
                                        //}}} </editor-fold>
                                    }
                                    else
                                    {
                                        // Precompilato e precompilato testo sola visualizzazione
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        $descrizione_campo_libero = '';
                                        foreach ($campo_libero['valori_precomp'] as $valore_precomp)
                                        {
                                            if ($campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp'])
                                            {
                                                switch ($campi_liberi_visualizzazione)
                                                {
                                                    case 'valore':
                                                        $descrizione_campo_libero = $valore_precomp['valore'];
                                                        break;
                                                    case 'codice':
                                                        $descrizione_campo_libero = $valore_precomp['codice'];
                                                        break;
                                                    case 'descrizione':
                                                        $descrizione_campo_libero = $valore_precomp['descrizione'];
                                                        break;
                                                    default:
                                                        $descrizione_campo_libero = $valore_precomp['codice'];
                                                        break;
                                                }
                                            }
                                        }

                                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO')
                                        {
                                            if ($campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] == -1)
                                            {
                                                $descrizione_campo_libero = $campo_libero['valore'][$materia['id_materia']]['valore_testuale'];
                                            }
                                        }

                                        if ($descrizione_campo_libero == '')
                                        {
                                            $descrizione_campo_libero = '------';
                                        }

                                        $html .= '<span style=\'font-weight: normal;\'>' . $descrizione_campo_libero . '</span>';
                                        //}}} </editor-fold>
                                    }
                                }
                                elseif ($campo_libero['tipo_valore'] == 'NUMERO')
                                {
                                    if ($voto_modificabile || $proposta_modificabile)
                                    {
                                        $html .= "\t" . "\t" . "\t" . '<input size=\'2\' type=\'text\' name=\'cl_number_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'' . intval($campo_libero['valore'][$materia['id_materia']]['valore_numerico']) . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                    }
                                }
                                else
                                {
                                    // Caso solo TESTO
                                    if ($voto_modificabile || $proposta_modificabile)
                                    {
                                        if($campo_libero['tipo_precompilato_testo'] == 'textarea')
                                        {
                                            $html .= '<textarea rows=\'5\' cols='.$campo_libero['dimensione_precompilato_testo'].' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">'.$campo_libero['valore'][$materia['id_materia']]['valore_testuale'] .'</textarea>' . "\n";
                                        }
                                        else
                                        {
                                            $html .= "\t" . "\t" . "\t" . '<input size=\'10\' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value="' . $campo_libero['valore'][$materia['id_materia']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                        }
                                    }
                                }
                                if ($campo_libero['valore'][$materia['id_materia']]['id_valore_campo_libero'] > 0)
                                {
                                    $html .= "\t" . "\t" . "\t" . '<input type=\'hidden\' name=\'cl_id_valore_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'' . $campo_libero['valore'][$materia['id_materia']]['id_valore_campo_libero'] . '\'>' . "\n";
                                }
                                $html .= "\t" . "\t" . '</td>';
                                // creazione della nuova riga ogni due colonne
                                if (($cont_campi) % 2 == 0)
                                {
                                    $html .= "\t" . '</tr><tr style="background-color:' . $bgcolor . ';">' . "\n";
                                }
                                $cont_campi++;
                                //}}} </editor-fold>
                            }
                        }
                        //verifico che sia pari perchè nel ciclo prima il contatore viene incrementato alla fine e nel caso aggiungo la cella vuota
                        if (($cont_campi) % 2 == 0)
                        {
                            $html .= "\t" . "\t" . '<td width=\'50%\'></td>';
                        }
                        $html .= "\t" . '</tr>';
                        $html .= "</table>";
                        //}}} </editor-fold>

                        //{{{ <editor-fold defaultstate="collapsed" desc="Campi liberi padre-figlio">
                        $html .= '<table width=\'100%\' style="font-size: ' . $font_size . ';font-weight: bold;">' . "\n";
                        foreach ($mat_campi_liberi_parenti as $campo_libero)
                        {
                            if ($campo_libero['id_materia'] == 0 or $campo_libero['id_materia'] == $materia['id_materia'])
                            {
                                //{{{ <editor-fold defaultstate="collapsed">
                                $html .= "\t" . '<tr><td><span style=\'font-size: 5;\'><br></span></td></tr>' . "\n";
                                $html .= "\t" . '<tr style="background-color:' . $bgcolor . '; border-top: 1px dotted grey;">' . "\n";
                                $html .= "\t" . "\t" . '<td colspan=\'3\' align=\'center\'>';
                                $html .= $campo_libero['nome'];
                                $html .= '</td>' . "\n";
                                $html .= "\t" . '</tr>' . "\n";
                                if ($voto_modificabile || $proposta_modificabile)
                                {
                                    $cont = 0;
                                    foreach ($campo_libero['figli'] as $campo_libero_figlio)
                                    {
                                        $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                                        $html .= "\t" . "\t" . '<td width=\'60%\'>';
                                        $html .= $campo_libero_figlio['nome'];
                                        if (stripos($campo_libero_figlio['tipo_valore'], 'PRECOMPILATO') !== false)
                                        {
                                            //{{{ <editor-fold defaultstate="collapsed">
                                            if (count($campo_libero_figlio['valori_precomp']) > 0)
                                            {
                                                $html .= "\t" . "\t" . "\t" . '<select style=\'width: 100%\' name=\'cl_precomp_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' class=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                                $html .= "\t" . "\t" . "\t" . "\t" . '<option value="">---</option>."\n"';
                                                foreach ($campo_libero_figlio['valori_precomp'] as $valore_precomp) {
                                                    if ($campo_libero_figlio['valore'][$materia['id_materia']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                    } else {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                    }

                                                    switch ($campi_liberi_visualizzazione) {
                                                        case 'valore':
                                                            $descrizione_select = $valore_precomp['valore'];
                                                            break;
                                                        case 'codice':
                                                            $descrizione_select = $valore_precomp['codice'];
                                                            break;
                                                        case 'descrizione':
                                                            $descrizione_select = $valore_precomp['descrizione'];
                                                            break;
                                                        default:
                                                            $descrizione_select = $valore_precomp['codice'];
                                                            break;
                                                    }

                                                    $html .= 'value=\'' . $valore_precomp['id_valore_precomp'] . '\'>' . $descrizione_select . '</option>' . "\n";
                                                }
                                            }
                                            if ($campo_libero_figlio['tipo_valore'] == 'PRECOMPILATO_TESTO')
                                            {
                                                if (count($campo_libero_figlio['valori_precomp']) > 0)
                                                {
                                                    if ($campo_libero_figlio['valore'][$materia['id_materia']]['id_valore_precomp'] == -1) {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                    } else {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                    }
                                                    $html .= 'value=\'-1\'>Altro</option>' . "\n";
                                                }
                                                else
                                                {
                                                    $html .= '<input type=\'hidden\' name=\'cl_precomp_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'-1\'>' . "\n";
                                                }
                                            }

                                            if (count($campo_libero_figlio['valori_precomp']) > 0)
                                            {
                                                $html .= "\t" . "\t" . "\t" . '</select>' . "\n";
                                                $html .= "\t" . "\t" . "\t";
                                            }
                                            if ($campo_libero_figlio['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                                if($campo_libero_figlio['tipo_precompilato_testo'] == 'textarea')
                                                {
                                                    $html .= '<textarea rows=\'5\' cols='.$campo_libero_figlio['dimensione_precompilato_testo'].' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">'.$campo_libero_figlio['valore'][$materia['id_materia']]['valore_testuale'] .'</textarea>' . "\n";
                                                }
                                                else
                                                {
                                                    $html .= '<input size=\''.$campo_libero_figlio['dimensione_precompilato_testo'].' \' type=\'text\' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value="' . $campo_libero_figlio['valore'][$materia['id_materia']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                                }
                                            }
                                            //}}} </editor-fold>

                                        }
                                        elseif ($campo_libero_figlio['tipo_valore'] == 'NUMERO')
                                        {
                                            $html .= "\t" . "\t" . "\t" . '<input size=\'2\' type=\'text\' name=\'cl_number_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'' . intval($campo_libero_figlio['valore'][$materia['id_materia']]['valore_numerico']) . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                        }
                                        else
                                        {
                                            // Caso solo TESTO
                                            if($campo_libero_figlio['tipo_precompilato_testo'] == 'textarea')
                                            {
                                                $html .= '<textarea rows=\'5\' cols='.$campo_libero_figlio['dimensione_precompilato_testo'].' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">'.$campo_libero_figlio['valore'][$materia['id_materia']]['valore_testuale'] .'</textarea>' . "\n";
                                            }
                                            else
                                            {
                                                $html .= "\t" . "\t" . "\t" . '<input size=\'10\' type=\'text\' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value="' . $campo_libero_figlio['valore'][$materia['id_materia']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                            }
                                        }

                                        if ($campo_libero_figlio['valore'][$materia['id_materia']]['id_valore_campo_libero'] > 0)
                                        {
                                            $html .= "\t" . "\t" . "\t" . '<input type=\'hidden\' name=\'cl_id_valore_' . $campo_libero_figlio['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'' . $campo_libero_figlio['valore'][$materia['id_materia']]['id_valore_campo_libero'] . '\'>' . "\n";
                                        }
                                        $html .= '</td>' . "\n";

                                        //-------- Celle del Copia e del riquadro di testo (inseriti qui per avere il rowspan)
                                        if ($cont == 0)
                                        {
                                            $html .= "\t" . "\t" . '<td rowspan=\'' . count($campo_libero['figli']) . '\' align=\'center\'>';
                                            if($campo_libero['tipo_valore'] != 'RAGGRUPPAMENTO')
                                            {
                                                $html .= '<input type=\'button\' value=\'Copia ->\' style=\'height: 60px;\' onclick="copiaValori(\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\', \'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\', \'' . $materia['id_materia'] . '\');" ';
                                                $html .= 'onmouseover="document.getElementById(\'tooltip_copia\').style.display = \'\';"';
                                                $html .= 'onmouseout="document.getElementById(\'tooltip_copia\').style.display = \'none\';"';
                                                $html .= '>';
                                            }
                                            $html .= '</td>' . "\n";
                                            $html .= "\t" . "\t" . '<td rowspan=\'' . count($campo_libero['figli']) . '\'>';
                                            if (stripos($campo_libero['tipo_valore'], 'PRECOMPILATO') !== false) {
                                                //{{{ <editor-fold defaultstate="collapsed">
                                                if (count($campo_libero['valori_precomp']) > 0)
                                                {
                                                    $html .= "\t" . "\t" . "\t" . '<select style=\'width: 50%;\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option value="">---</option>."\n"';
                                                    foreach ($campo_libero['valori_precomp'] as $valore_precomp) {
                                                        if ($campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                        } else {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                        }

                                                        switch ($campi_liberi_visualizzazione) {
                                                            case 'valore':
                                                                $descrizione_select = $valore_precomp['valore'];
                                                                break;
                                                            case 'codice':
                                                                $descrizione_select = $valore_precomp['codice'];
                                                                break;
                                                            case 'descrizione':
                                                                $descrizione_select = $valore_precomp['descrizione'];
                                                                break;
                                                            default:
                                                                $descrizione_select = $valore_precomp['codice'];
                                                                break;
                                                        }

                                                        $html .= 'value=\'' . $valore_precomp['id_valore_precomp'] . '\'>' . $descrizione_select . '</option>' . "\n";
                                                    }
                                                }
                                                if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO')
                                                {
                                                    if (count($campo_libero['valori_precomp']) > 0)
                                                    {
                                                        if ($campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] == -1) {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                        } else {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                        }
                                                        $html .= 'value=\'-1\'>Altro</option>' . "\n";
                                                    }
                                                    else
                                                    {
                                                        $html .= '<input type=\'hidden\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'-1\'>' . "\n";
                                                    }
                                                }

                                                if (count($campo_libero['valori_precomp']) > 0)
                                                {
                                                    $html .= "\t" . "\t" . "\t" . '</select>' . "\n";
                                                    $html .= "\t" . "\t" . "\t";
                                                }
                                                if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO')
                                                {
                                                    if($campo_libero['tipo_precompilato_testo'] == 'textarea')
                                                    {
                                                        $html .= '<textarea rows=\'5\' cols='.$campo_libero['dimensione_precompilato_testo'].' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">'.$campo_libero['valore'][$materia['id_materia']]['valore_testuale'] .'</textarea>' . "\n";
                                                    }
                                                    else
                                                    {
                                                        $html .= '<input size=\''.$campo_libero['dimensione_precompilato_testo'].' \' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value="' . $campo_libero['valore'][$materia['id_materia']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                                    }
                                                }
                                                //}}} </editor-fold>
                                            }
                                            elseif ($campo_libero['tipo_valore'] == 'RAGGRUPPAMENTO')
                                            {
                                                $html .= "\t" . "\t" . "\t" . "\n";
                                            }
                                            elseif ($campo_libero['tipo_valore'] == 'NUMERO')
                                            {
                                                $html .= "\t" . "\t" . "\t" . '<input size=\'2\' type=\'text\' name=\'cl_number_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'' . intval($campo_libero['valore'][$materia['id_materia']]['valore_numerico']) . '\' onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                            }
                                            else
                                            {
                                                $html .= "\t" . "\t" . "\t" . '<input size=\'10\' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value="' . $campo_libero['valore'][$materia['id_materia']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $materia['id_materia'] . '\').value=\'SI\';">' . "\n";
                                            }
                                            if ($campo_libero['valore'][$materia['id_materia']]['id_valore_campo_libero'] > 0)
                                            {
                                                $html .= "\t" . "\t" . "\t" . '<input type=\'hidden\' name=\'cl_id_valore_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia'] . '\' value=\'' . $campo_libero['valore'][$materia['id_materia']]['id_valore_campo_libero'] . '\'>' . "\n";
                                            }
                                            $html .= '</td>' . "\n";
                                        }
                                        //--------
                                        $cont++;
                                    }
                                }
                                else
                                {
                                    $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                                    $html .= "\t" . "\t" . '<td colspan=\'3\' align=\'center\' style=\'font-weight: normal;\'>';
                                    if (trim($campo_libero['valore'][$materia['id_materia']]['valore_testuale'] == ''))
                                    {
                                        if ($campo_libero['tipo_valore'] == 'RAGGRUPPAMENTO')
                                        {
                                            $html .= '<br></td>';
                                            foreach ($campo_libero['figli'] as $campo_del_raggruppamento)
                                            {
                                                $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                                                $html .= "\t" . "\t" . '<td colspan=\'3\' align=\'center\' style=\'font-weight: normal;\'>' . "\n";
                                                $html .= "\t" . "\t" . "\t" . '<b>' . $campo_del_raggruppamento['descrizione'] . '</b>' . "\n";
                                                $html .= "\t" . "\t" . "\t" . '<br>' . "\n";
                                                if ($campo_del_raggruppamento['valore'][$materia['id_materia']]['id_valore_precomp'] > 0)
                                                {
                                                    foreach ($campo_del_raggruppamento['valori_precomp'] as $valore_precomp)
                                                    {
                                                        if ($campo_del_raggruppamento['valore'][$materia['id_materia']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp'])
                                                        {
                                                            $html .= $valore_precomp['descrizione'];
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    $html .= '------';
                                                }
                                                $html .= '</td>';
                                                $html .= '</tr>';
                                            }
                                        }
                                        else
                                        {
                                            $html .= '------';
                                        }
                                    }
                                    else
                                    {
                                        $html .= $campo_libero['valore'][$materia['id_materia']]['valore_testuale'];
                                    }
                                    $html .= '</td>';
                                    $html .= '</tr>';
                                }
                                $html .= "\t" . '</tr>' . "\n";
                                //}}} </editor-fold>
                            }
                        }
                        $html .= "\t" . '</tr>' . "\n";
                        $html .= '</table>' . "\n" . "\n";
                        $html .= '</td>' . "\n";
                        $html .= "\t" . '</tr>' . "\n";
                        $html .= '</table>' . "\n" . "\n";

                        $html .= '</td>' . "\n";
                        $html .= "\t" . '</tr>' . "\n";
                        //}}} </editor-fold>
                    }
                    //}}} </editor-fold>

                    // Riepilogo COMPETENZE
                    if (isset($materia['riepilogo_valutazioni_competenze']) && count($materia['riepilogo_valutazioni_competenze']) > 0)
                    {
                        $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                        $cont_row++;

                        $html .= '<td  colspan="9">' . "\n" . "\n";

                        $html .= '<table width="100%" style="font-size: ' . $font_size . ';font-weight: bold">' . "\n";
                        $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                        $html .= "\t" . "\t" . '<td width=\'100%\'>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . '<div style="color: darkblue; font-weight: normal;">' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . '<div class="padding_cella_generica" style="float: left;">' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . "\t" . '<b>Competenze / Obiettivi</b>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . "\t" . '&emsp;-&emsp;Media generale delle valutazioni di ' . $materia['descrizione'] . ': <b>' . $materia['media_generale_competenze_materia'] . '</b>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . "\t" . '&emsp;-&emsp;Media generale delle valutazioni di tutte le materie: <b>' . $materia['media_generale_competenze_trasversale'] . '</b>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . '</div>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . '<div style="float: right; padding-top: 2px;">' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . "\t" . '<div class="padding_cella_generica" ';
                        $html .=                                        ' style="color: darkblue; float: right; display: inline-block; padding: 0px 7px; border: 1px solid darkblue; border-radius: 20px; margin-right: 5px; cursor: pointer;" ';
                        $html .=                                        ' title="Informazioni e spiegazioni riguardanti le competenze" ';
                        $html .=                                        ' onclick=\'window.open("aiuto_competenze_scrutini.php", "Informazioni Competenze", "width=400,height=400,top=50,left=50");\' ';
                        $html .=                                    '>Aiuto</div>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . "\t" . '</div>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . '</div>' . "\n";
                        $html .= "\t" . "\t" .  "\t" . '<table align="center" width="98%" style="font-size: ' . $font_size . ';">' . "\n";

                        if (!function_exists('scrivi_riepilogo_competenze_materia'))
                        {
                            function scrivi_riepilogo_competenze_materia(&$html, $elenco_competenze, $materia)
                            {
                                $id_materia = $materia['id_materia'];
                                foreach ($elenco_competenze as $competenza) {
                                    $espandibile = false;
                                    $toggle = '';
                                    if (
                                        (isset($competenza['valutazioni']) && count($competenza['valutazioni']) > 0)
                                        ||
                                        (isset($competenza['figli']) && count($competenza['figli']) > 0)
                                    ){
                                        $espandibile = true;
                                        $toggle = '<button type="button" onclick="$(comp'.$competenza['id']. '_' . $id_materia . ').toggle(); $(this).text(function(i, text){return (text == \'&#707;\') ? \'&#709;\' : \'&#707;\'});">&#707;</button> ';
                                    }

                                    $descrizione_competenza = (trim($competenza['descrizione']) !== "") ? ' (' . $competenza['descrizione'] . ')' : '';

                                    $html .= '<tr style="font-weight: normal; border-top: 1px dotted gray;">' . "\n";
                                    $html .= "\t" . '<td class="padding_cella_generica">' . $toggle . '<b>' . $competenza['codice'] . '</b>' . $descrizione_competenza . '</td>' . "\n";
                                    $html .= "\t" . '<td class="padding_cella_generica" style="white-space: nowrap; width: 400px;">' . "\n";
                                    $html .= "\t" . "\t" . '<table width="100%">' . "\n";

                                    if (isset($competenza['media_nodo_materia']))
                                    {
                                        $html .= "\t" .  "\t" . '<tr>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right">Media di ' . $materia['nome_materia_breve'] . ':</td>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right" style="width: 30px;"><b>' . $competenza['media_nodo_materia'] . '</b></td>' . "\n";
                                        $html .= "\t" .  "\t" . '</tr>' . "\n";
                                    }
                                    if (isset($competenza['media_nodo_trasversale']))
                                    {
                                        $html .= "\t" .  "\t" . '<tr>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right">Media trasversale:</td>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right" style="width:30px;"><b>' . $competenza['media_nodo_trasversale'] . '</b></td>' . "\n";
                                        $html .= "\t" .  "\t" . '</tr>' . "\n";
                                    }
                                    if (isset($competenza['media_profonda_materia']))
                                    {
                                        $html .= "\t" .  "\t" . '<tr>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right">Media di ' . $materia['nome_materia_breve'] . ' con sottogruppi:</td>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right" style="width: 30px;"><b>' . $competenza['media_profonda_materia'] . '</b></td>' . "\n";
                                        $html .= "\t" .  "\t" . '</tr>' . "\n";
                                    }
                                    if (isset($competenza['media_profonda_trasversale']))
                                    {
                                        $html .= "\t" .  "\t" . '<tr>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right">Media trasversale con sottogruppi:</td>' . "\n";
                                        $html .= "\t" .  "\t" . "\t" . '<td align="right" style="width: 30px;"><b>' . $competenza['media_profonda_trasversale'] . '</b></td>' . "\n";
                                        $html .= "\t" .  "\t" . '</tr>' . "\n";
                                    }
                                    $html .= "\t" . "\t" . '</table>' . "\n";
                                    $html .= "\t" . '</td>' . "\n";
                                    $html .= '</tr>' . "\n";

                                    if ($espandibile)
                                    {
                                        $html .= '<tr style="font-weight: normal; display: none;" id="comp' . $competenza['id'] . '_' . $id_materia . '">' . "\n";
                                        $html .= "\t" . '<td colspan="2" class="padding_cella_generica">' . "\n";
                                        $html .= "\t" . "\t" . '<div style="margin-left: 20px;">' . "\n";

                                        if (isset($competenza['valutazioni']) && count($competenza['valutazioni']) > 0)
                                        {
                                            //valutazioni competenza
                                            $html .= "\t" . "\t" . '<div style="color: darkblue;">Valutazioni</div>' . "\n";

                                            $array_valutazioni = [];
                                            foreach ($competenza['valutazioni'] as $valutazione)
                                            {
                                                if ($valutazione['id_materia'] == $id_materia)
                                                {
                                                    $array_valutazioni[] = '<b>' . $valutazione['codice'] . '</b> (' . $valutazione['valore_numerico'] . ')';
                                                }
                                            }
                                            $html .= "\t" . "\t" . '<div>' . implode('&ensp;-&ensp;', $array_valutazioni) . '</div>' . "\n";
                                        }

                                        if (isset($competenza['figli']) && count($competenza['figli']) > 0)
                                        {
                                            //figli competenza
                                            $html .= "\t" . "\t" . '<div style="color: darkblue;">Sottogruppi</div>' . "\n";
                                            $html .= "\t" . "\t" . '<table width="100%">' . "\n";

                                            scrivi_riepilogo_competenze_materia($html, $competenza['figli'], $materia);

                                            $html .= "\t" . "\t" . '</table>' . "\n";
                                        }

                                        $html .= "\t" . "\t" . '</div>' . "\n";
                                        $html .= "\t" . '</td>' . "\n";
                                        $html .= '</tr>' . "\n";
                                    }
                                }
                            }
                        }

                        scrivi_riepilogo_competenze_materia($html, $materia['riepilogo_valutazioni_competenze'], $materia);

                        $html .= "\t" . "\t" .  "\t" . '</table>' . "\n";
                        $html .= "\t" . "\t" . '</td>' . "\n";
                        $html .= "\t" . '</tr>' . "\n";
                        $html .= '</table>' . "\n" . "\n";

                        $html .= '</td>' . "\n";
                        $html .= "\t" . '</tr>' . "\n";
                    }

                    $html .= "<input type='hidden' id='modificato_" . $materia['id_materia'] . "' name='modificato_" . $materia['id_materia'] . "'>\n";
                    $html .= "<input type='hidden' name='id_voto_pagellina_" . $materia['id_materia'] . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['id_voto_pagellina'] . "'>\n";
                    $html .= "<input type='hidden' name='giudizio_analitico_" . $materia['id_materia'] . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['giudizio_analitico'] . "'>\n";
                    $html .= "<input type='hidden' name='modificato_da_amministratore_" . $materia['id_materia'] . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['modificato_da_amministratore'] . "'>\n";
                    //}}} </editor-fold>
                }
            }
            //}}} </editor-fold>
        }

        // Tooltip pulsante copia
        $html .= "<div id='tooltip_copia' style='position: fixed; bottom: 5px; background-color: #303030; color: white; padding: 10px; display: none; left: 50%;
                    -ms-transform: translateX(-50%); -webkit-transform: translateX(-50%); -moz-transform: translateX(-50%); -o-transform: tranlslateX(-50%); transform: translateX(-50%);'>";
        $html .= "<span style='font-size: 18;'>ATTENZIONE: verrà sovrascritto il testo precedentemente inserito</span>";
        $html .= "</div>";

        // Hidden
        $html .= "<input type='hidden' name='form_stato' 			value='$form_stato'								>\n"
                . "<input type='hidden' name='stato_principale' 	value='pagelle_principale'									>\n"
                . "<input type='hidden' name='stato_secondario' 	value='modifica_tabellone_pagelline_display'				>\n"
                . "<input type='hidden' name='id_materia' 			value=''													>\n"
                . "<input type='hidden' name='id_studente'			value='" . $studente['id_studente'] . "'						>\n"
                . "<input type='hidden' name='indirizzo' 			value='" . $dati_classe['descrizione'] . "'						>\n"
                . "<input type='hidden' name='id_indirizzo' 		value='" . $dati_classe['id_indirizzo'] . "'					>\n"
                . "<input type='hidden' name='classe' 				value='" . $dati_classe['classe'] . $dati_classe['sezione'] . "'	>\n"
                . "<input type='hidden' name='id_classe' 			value='" . $dati_classe['id_classe'] . "'						>\n"
                . "<input type='hidden' name='id_classe_interno' 	value=''													>\n"
                . "<input type='hidden' name='periodo'				value='$periodo'											>\n"
                . "<input type='hidden' name='visualizza_assenze' 	value='$visualizza_assenze'									>\n"
                . "<input type='hidden' name='codice_descrizione' 	value='$codice_descrizione'									>\n"
                . "<input type='hidden' name='screen_width' 		value='$schermo'											>\n"
                . "<input type='hidden' name='alto_contrasto' 		value='$alto_contrasto'										>\n"
                . "<input type='hidden' name='current_user' 		value='$current_user'										>\n"
                . "<input type='hidden' name='current_key'			value='$current_key'										>\n"
                . "<input type='hidden' name='salva' 				value='studente'											>\n"
                . "<input type='hidden' name='torna_a'				value='pagellina'											>\n";
               // . "</table>\n";

        if ($parametro_mostra_media = 'SI')
        {
            foreach ($media_array_voti as $materia_media)
            {
                if (
                        ($tipo_visualizzazione == 'voto_singolo')
                    )
                {
                    $testo_media_proposta = round($somma_voti['somma_voti']['proposta']['unico'] / $somma_voti['cont_voti']['proposta']['unico'], 2);
                    $testo_media_voto = round($somma_voti['somma_voti']['voto']['unico'] / $somma_voti['cont_voti']['voto']['unico'], 2);
                }
                else
                {
                    $testo_media_proposta = '';
                    if ($somma_voti['cont_voti']['proposta']['scritto'] > 0)
                    {
                        $testo_media_proposta .= 'scritto: ' . round($somma_voti['somma_voti']['proposta']['scritto'] / $somma_voti['cont_voti']['proposta']['scritto'], 2) . ' ';
                    }
                    if ($somma_voti['cont_voti']['proposta']['orale'] > 0)
                    {
                        $testo_media_proposta .= 'orale: ' . round($somma_voti['somma_voti']['proposta']['orale'] / $somma_voti['cont_voti']['proposta']['orale'], 2) . ' ';
                    }
                    if ($somma_voti['cont_voti']['proposta']['pratico'] > 0)
                    {
                        $testo_media_proposta .= 'pratico: ' . round($somma_voti['somma_voti']['proposta']['pratico'] / $somma_voti['cont_voti']['proposta']['pratico'], 2);
                    }

                    $testo_media_voto = '';
                    if ($somma_voti['cont_voti']['voto']['scritto'] > 0)
                    {
                        $testo_media_voto .= 'scritto: ' . round($somma_voti['somma_voti']['voto']['scritto'] / $somma_voti['cont_voti']['voto']['scritto'], 2) . ' ';
                    }
                    if ($somma_voti['cont_voti']['voto']['orale'] > 0)
                    {
                        $testo_media_voto .= 'orale: ' . round($somma_voti['somma_voti']['voto']['orale'] / $somma_voti['cont_voti']['voto']['orale'], 2) . ' ';
                    }
                    if ($somma_voti['cont_voti']['voto']['pratico'] > 0)
                    {
                        $testo_media_voto .= 'pratico: ' . round($somma_voti['somma_voti']['voto']['pratico'] / $somma_voti['cont_voti']['voto']['pratico'], 2);
                    }
                }


            }
            //$html .= '<table width="100%" style="font-size: ' . $font_size . ';font-weight: bold;" border=\'1\'>' . "\n";
            $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';" style="font-weight: bold;">' . "\n"
                . "\t\t" . '<td align="center" style="font-size: ' . $font_size . ';font-weight: bold;"></td>' . "\n"
                . "\t\t" . '<td align="center" style="font-size: ' . $font_size . ';font-weight: bold;">Medie' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-right: 0px; font-size: ' . $font_size . '; min-width: 80px;\'>Proposta<br>' . $testo_media_proposta . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-left: 0px; border-right: 0px; font-size: ' . $font_size . ';\'>' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-left: 0px; font-size: ' . $font_size . '; min-width: 80px;\'>Voto<br>' . $testo_media_voto . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td colspan=4 align="center" style="font-size: ' . $font_size . ';font-weight: bold;"></td>' . "\n"
                . "</table>";
        }

        if ($parametro_competenze == 'SI') {
            //{{{ <editor-fold defaultstate="collapsed" desc="sezione competenze">
            $fill = !$fill;
            if ($fill) {
                $bgcolor = "#91E3DC";
            } else {
                $bgcolor = "#66BADC";
            }

            $voto_modificabile = (
                                    (
                                        $form_stato == 'amministratore'
                                        or
                                        (
                                            $coordinatore == 'SI'
                                            and
                                            $dati_classe['consiglio_classe_attivo'] == 'SI'
                                        )
                                    )
                                    and
                                    $stato_chiusura_scrutini != 'SI'
                                );
            $proposta_modificabile = (
                                        (
                                            in_array($current_user, $materia['professori'])
                                            and
                                            $dati_classe['consiglio_classe_attivo'] == 'NO'
                                        )
                                        and
                                        $stato_chiusura_scrutini != 'SI'
                                    );

            if ($voto_modificabile || $proposta_modificabile)
            {
                $abilita_pulsante_salva = true;
            }
            // Tre possibilità:
            //  1 - Scuola Elementare
            //      Classe quinta
            //  2 - Scuola media
            //      Classe terza
            //  3 Liceo
            //      A - Indirizzo standard e studente di 16 anni
            //        o
            //      B - Liceo classico e classe quinta (ginnasio)
            //        o
            //      C - Classe seconda

            // Verifico se lo studente ha 16 anni nell'anno scolastico in corso (inizio 1 settembre, fine 31 agosto)
            $nascita = new DateTime(date('Y-m-d', $studente['data_nascita']));
            $inizio = new DateTime(date('Y-m-d', mktime(0, 0, 0, 9, 1, $anno_inizio)));
            $fine  = new DateTime(date('Y-m-d', mktime(0, 0, 0, 8, 31, $anno_fine)));

            $anni_inizio = $nascita->diff($inizio);
            $anni_fine = $nascita->diff($fine);
            //$competenze_abilitate = ($anni_inizio->format("%Y") == 16 or $anni_fine->format("%Y") == 16) ? true : false;

            if 
                (
                    ($dati_classe["classe"] == 2 
                        && 
                        (
                            $dati_classe['tipo_indirizzo'] == 0 
                            || 
                            $dati_classe['tipo_indirizzo'] == 2
                            || 
                            $dati_classe['tipo_indirizzo'] == 3
                            || 
                            $dati_classe['tipo_indirizzo'] == 5
                        )
                    )
                    ||
                    ($dati_classe['tipo_indirizzo'] == 1 && $dati_classe["classe"] == 5)
                    ||
                    $studente['forza_competenze'] == '1'
                )
            {
                $prima_lingua_straniera = estrai_dati_materia($studente['id_lingua_1']);

                if ($parametro_debiti == 'SI')
                {
                    //{{{ <editor-fold defaultstate="collapsed" desc="scuole superiori">
                    if ($browser == 'ie') {
                        $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                    } else {
                        //$html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                        $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px;">' . "\n";
                    }

                    if ($anno_inizio < 2017)
                    {
                    //{{{ <editor-fold defaultstate="collapsed" desc="vecchia interfaccia">
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select">
                        $html_to_return_cmp_val_ita = '';
                        switch ($dati_competenze['cmp_val_ita']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi italiano">
                            case 'BASE':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_mat = '';
                        switch ($dati_competenze['cmp_val_mat']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                            case 'BASE':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_sci_tec = '';
                        switch ($dati_competenze['cmp_val_sci_tec']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                            case 'BASE':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_sto_soc = '';
                        switch ($dati_competenze['cmp_val_sto_soc']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                            case 'BASE':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }
                        //}}} </editor-fold>

                        if ($browser == 'ie') {
                            $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                        } else {
                            //$html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                            $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px;">' . "\n";
                        }
                        $html .= "<table width='100%'>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td colspan='2' align='center'>"
                                . "\t\t<b><font color='#000044' size='3'>CERTIFICAZIONE delle COMPETENZE DI BASE DELLO STUDENTE</font></b>"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Lingua Italiana, Lingua Straniera, Altri Linguaggi:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_linguaggi_italiano'>"
                                . $html_to_return_cmp_val_ita
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_linguaggi_italiano' size='50' value='" . $dati_competenze['cmp_txt_ita'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE MATEMATICO:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_matematico'>"
                                . $html_to_return_cmp_val_mat
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_matematico' size='50' value='" . $dati_competenze['cmp_txt_mat'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE SCIENTIFICO-TECNOLOGICO:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_scientifico_tecno'>"
                                . $html_to_return_cmp_val_sci_tec
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_scientifico_tecno' size='50' value='" . $dati_competenze['cmp_txt_sci_tec'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE STORICO-SOCIALE</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_storico_sociale'>"
                                . $html_to_return_cmp_val_sto_soc
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_storico_sociale' size='50' value='" . $dati_competenze['cmp_txt_sto_soc'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "</table>\n"
                                . "</div>\n";
                    //}}} </editor-fold>
                    }
                    else
                    {
                    //{{{ <editor-fold defaultstate="collapsed" desc="nuova interfaccia">
                    // ---- NUOVA INTERFACCIA ----
                        $html .= "<table width='100%'>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td colspan='3' align='center' class='sottotitolo_testo'>"
                                . "\t\t<b>CERTIFICAZIONE delle COMPETENZE DI BASE DELLO STUDENTE</b><br>"
                                . "\t\t</td>\n"
                                . "\t</tr>\n";

                        $arr_valori = ['A' => 'A - Livello avanzato',
                                       'B' => 'B - Livello intermedio',
                                       'C' => 'C - Livello base',
                                       'D' => 'D - Livello base non raggiunto',
                                    ];

                        foreach ($studente['competenze_scolastiche']['SS'] as $competenza)
                        {
                            if ($competenza['ordine_scolastico'] == 'SS')
                            {
                                //Filtro asse dei linguaggi unico per il trentino
                                $mostra = false;
                                if ($competenza['competenze_chiave'] == "ASSE DEI LINGUAGGI")
                                {
                                    if ((strpos($competenza['descrizione'], "italiana") !== false)
                                        && (strpos($competenza['descrizione'], "straniera") !== false)
                                        && (strpos($competenza['descrizione'], "linguaggi") !== false))
                                    {
                                        $id_competenza = $competenza['id_competenza_scolastica'];
                                        $mostra = true;
                                    }
                                }
                                else
                                {
                                    $id_competenza = $competenza['id_competenza_scolastica'];
                                    $mostra = true;
                                }
                                if ($mostra)
                                {
                                    $fill = !$fill;
                                    if ($fill) {
                                        $bgcolor = "#91E3DC";
                                    } else {
                                        $bgcolor = "#66BADC";
                                    }

                                    $html .= "\t<tr width='35%' style='background-color:" . $bgcolor . ";'>\n"
                                            . "\t\t<td class='padding_cella_generica' valign='top'>"
                                            . "\t\t" . str_replace("\\n", "<br>", decode($competenza['descrizione']))
                                            . "\t\t</td>\n"
                                            . "\t\t<td valign='top' width='20%' class='padding_cella_generica'><center>"
                                            . "\t\t" . str_replace("|", "<br>", decode($competenza['competenze_chiave']))
                                            . "\t\t</center></td>\n"
                                            . "\t\t<td  width='40%' class='padding_cella_generica'>";

                                    // Select
                                    $html .= "\t\t<SELECT name='ss_valore_" . $id_competenza . "'>";
                                    $html .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>";

                                    foreach ($arr_valori as $k => $valore)
                                    {
                                        $sel = $competenza['valore'] == $k ? "selected" : "";

                                        $html .= "\t\t\t<OPTION value='" . $k . "' " . $sel . ">" . $valore . "</OPTION>";
                                    }

                                    $html .= "\t\t</SELECT>";
                                    $html .= "\t\t\t<input type='text' name='ss_testo_" . $id_competenza ."' size='50' value='" . decode($competenza['testo']) . "'>\n"
                                            . "\t\t</td>\n"
                                            . "\t</tr>\n";
                                }
                            }
                        }

                        $html .= "</table>\n"
                                . "</div>\n";
                    //}}} </editor-fold>
                    }
                    //}}} </editor-fold>
                }
                else
                {
                    //{{{ <editor-fold defaultstate="collapsed" desc="scuole superiori">
                    if ($browser == 'ie') {
                        $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                    } else {
                        //$html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                        $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px;">' . "\n";
                    }

                    if ($anno_inizio < 2017)
                    {
                    //{{{ <editor-fold defaultstate="collapsed" desc="vecchia interfaccia">
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select">
                        $html_to_return_cmp_val_ita = '';
                        switch ($dati_competenze['cmp_val_ita']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi italiano">
                            case 'BASE':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_ita .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_ing = '';
                        switch ($dati_competenze['cmp_val_ing']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                            case 'BASE':
                                $html_to_return_cmp_val_ing .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_ing .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_ing .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_ing .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_ing .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_altri = '';
                        switch ($dati_competenze['cmp_val_altri']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi altri">
                            case 'BASE':
                                $html_to_return_cmp_val_altri .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_altri .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_altri .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_altri .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_altri .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_mat = '';
                        switch ($dati_competenze['cmp_val_mat']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                            case 'BASE':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_mat .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_sci_tec = '';
                        switch ($dati_competenze['cmp_val_sci_tec']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                            case 'BASE':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }

                        $html_to_return_cmp_val_sto_soc = '';
                        switch ($dati_competenze['cmp_val_sto_soc']) {
                            //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                            case 'BASE':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'NOBASE':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'INTERMEDIO':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            case 'AVANZATO':
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            default:
                                $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                        . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                        . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                        . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                        . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                                break;
                            //}}} </editor-fold>
                        }
                        //}}} </editor-fold>

                        if ($browser == 'ie') {
                            $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                        } else {
                            $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                        }
                        $html .= "<table width='100%'>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td colspan='2' align='center'>"
                                . "\t\t<b><font color='#000044' size='3'>CERTIFICAZIONE delle COMPETENZE DI BASE DELLO STUDENTE</font></b>"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Lingua Italiana:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_linguaggi_italiano'>"
                                . $html_to_return_cmp_val_ita
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_linguaggi_italiano' size='50' value='" . $dati_competenze['cmp_txt_ita'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Lingua Straniera:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_linguaggi_inglese'>"
                                . $html_to_return_cmp_val_ing
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_linguaggi_inglese' size='50' value='" . $dati_competenze['cmp_txt_ing'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Altri Linguaggi:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_linguaggi_altri'>"
                                . $html_to_return_cmp_val_altri
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_linguaggi_altri' size='50' value='" . $dati_competenze['cmp_txt_altri'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE MATEMATICO:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_matematico'>"
                                . $html_to_return_cmp_val_mat
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_matematico' size='50' value='" . $dati_competenze['cmp_txt_mat'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE SCIENTIFICO-TECNOLOGICO:</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_scientifico_tecno'>"
                                . $html_to_return_cmp_val_sci_tec
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_scientifico_tecno' size='50' value='" . $dati_competenze['cmp_txt_sci_tec'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td align='right'>"
                                . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE STORICO-SOCIALE</b></font>"
                                . "\t\t</td>\n"
                                . "\t\t<td>"
                                . "\t\t<SELECT name='valore_storico_sociale'>"
                                . $html_to_return_cmp_val_sto_soc
                                . "\t\t</SELECT>"
                                . "\t\t\t<input type='text' name='testo_storico_sociale' size='50' value='" . $dati_competenze['cmp_txt_sto_soc'] . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n"
                                . "</table>\n"
                                . "</div>\n";
                    //}}} </editor-fold>
                    }
                    else
                    {
                    //{{{ <editor-fold defaultstate="collapsed" desc="nuova interfaccia">
                    // ---- NUOVA INTERFACCIA ----
                        $html .= "<table width='100%'>\n"
                                . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td colspan='3' align='center' class='sottotitolo_testo'>"
                                . "\t\t<b>CERTIFICAZIONE delle COMPETENZE DI BASE DELLO STUDENTE (SECONDARIA DI II GRADO)</b><br>"
                                . "\t\t</td>\n"
                                . "\t</tr>\n";

                        if ($anno_inizio>=2023 && $trentino_abilitato != "SI") {
                            $arr_valori = [ 'A' => 'A - Livello avanzato',
                                        'B' => 'B - Livello intermedio',
                                        'C' => 'C - Livello base',
                                        'D' => 'D - Livello iniziale',
                                 ];
                        } else {
                            $arr_valori = ['A' => 'A - Livello avanzato',
                                       'B' => 'B - Livello intermedio',
                                       'C' => 'C - Livello base',
                                       'D' => 'D - Livello base non raggiunto',
                                    ];
                        }

                        foreach ($studente['competenze_scolastiche']['SS'] as $competenza)
                        {
                            if ($competenza['ordine_scolastico'] == 'SS'
                                    //&& $competenza['anno_fine'] == 0
                                    )
                            {
                                $descrizione_competenza = $competenza['descrizione'];

                                //Filtro asse dei linguaggi per non mettere quello unico per il trentino
                                $mostra = false;
                                if ($competenza['competenze_chiave'] == "ASSE DEI LINGUAGGI")
                                {
                                    if ((strpos($competenza['descrizione'], "italiana") !== false)
                                        && (strpos($competenza['descrizione'], "straniera") !== false)
                                        && (strpos($competenza['descrizione'], "linguaggi") !== false))
                                    {
                                        $mostra = false;
                                    }
                                    else
                                    {
                                        $id_competenza = $competenza['id_competenza_scolastica'];
                                        $mostra = true;
                                        if (count($prima_lingua_straniera) > 0)
                                        {
                                            $descrizione_competenza = str_replace("###", $prima_lingua_straniera['descrizione'], $descrizione_competenza);
                                        }
                                        else
                                        {
                                            $descrizione_competenza = str_replace("###", "____________", $descrizione_competenza);
                                        }
                                    }
                                }
                                else
                                {
                                    $id_competenza = $competenza['id_competenza_scolastica'];
                                    $mostra = true;
                                }
                                if ($mostra)
                                {
                                    $fill = !$fill;
                                    if ($fill) {
                                        $bgcolor = "#91E3DC";
                                    } else {
                                        $bgcolor = "#66BADC";
                                    }

                                    $html .= "\t<tr width='35%' style='background-color:" . $bgcolor . ";'>\n"
                                            . "\t\t<td class='padding_cella_generica' valign='top'>"
                                            . "\t\t" . str_replace("\\n", "<br>", decode($descrizione_competenza))
                                            . "\t\t</td>\n"
                                            . "\t\t<td valign='top' width='20%' class='padding_cella_generica'><center>"
                                            . "\t\t" . str_replace("|", "<br>", decode($competenza['competenze_chiave']))
                                            . "\t\t</center></td>\n"
                                            . "\t\t<td  width='40%' class='padding_cella_generica'>";

                                    // escludo la prima competenza delle linguistiche perche' non va valutata, ma stampa solo il testo
                                    $escludi = false;
                                    if ($competenza['codice_competenza_sidi'] == 18
                                        && strpos(strtoupper(decode($descrizione_competenza)), 'UTILIZZARE LE DIVERSE LINGUE') !== false
                                        && $trentino_abilitato != "SI"){
                                        $escludi = true;
                                    }

                                    if (!$escludi){
                                        // Select
                                        if ($competenza['codice_competenza_sidi'] != 99){
                                            $html .= "\t\t<SELECT name='ss_valore_" . $id_competenza . "'>";
                                            $html .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>";

                                            foreach ($arr_valori as $k => $valore)
                                            {
                                                $sel = $competenza['valore'] == $k ? "selected" : "";

                                                $html .= "\t\t\t<OPTION value='" . $k . "' " . $sel . ">" . $valore . "</OPTION>";
                                            }

                                            $html .= "\t\t</SELECT>";
                                        }

                                        if ($competenza['codice_competenza_sidi'] == 18){
                                            // select con le lingue, che deve essere cmpilata obbligatoriamente se viene compilato il valore della competenza
                                            $html .= "\t\t<SELECT name='ss_testo_" . $id_competenza . "' data-obbligatorio='ss_valore_" . $id_competenza . "'>";
                                            $html .= "\t\t\t<OPTION value=''>--Selezionare lingua--</OPTION>";
                                            foreach ($array_lingue_straniere as $lingua)
                                            {
                                                $sel = $competenza['testo'] == $lingua ? "selected" : "";
                                                $html .= "\t\t\t<OPTION value='" . $lingua . "' " . $sel . ">" . $lingua . "</OPTION>";
                                            }
                                            $html .= "\t\t</SELECT>\n";
                                        } else {
                                            $html .= "\t\t\t<input type='text' name='ss_testo_" . $id_competenza ."' size='50' value='" . decode($competenza['testo']) . "' >\n";
                                        }
                                    }

                                    $html .= "\t\t</td>\n"
                                        . "\t</tr>\n";
                                }


                            }
                        }

                        $html .= "</table>\n"
                                    . "</div>\n";

                    //}}} </editor-fold>
                    }
                //}}} </editor-fold>
                }
            }
            elseif ($dati_classe["tipo_indirizzo"] == 4)
            {
                //{{{ <editor-fold defaultstate="collapsed" desc="scuole medie">

                if ($browser == 'ie') {
                    $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width: 100%;">' . "\n";
                } else {
                    $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width: 100%;">' . "\n";
                }

                if ($anno_inizio < 2016) {
                    //{{{ <editor-fold defaultstate="collapsed" desc="vecchia interfaccia">
                    //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select">
                    $html_to_return_cmp_val_ita = '';
                    switch ($dati_competenze['cmp_val_ita']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi italiano">
                        case 'BASE':
                            $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_ita .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_ita .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_l2 = '';
                    switch ($dati_competenze['cmp_val_l2']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select lingua 2">
                        case 'BASE':
                            $html_to_return_cmp_val_l2 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_l2 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_l2 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_l2 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_l2 .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_l3 = '';
                    switch ($dati_competenze['cmp_val_l3']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select lingua 3">
                        case 'BASE':
                            $html_to_return_cmp_val_l3 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_l3 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_l3 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_l3 .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_l3 .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_arte = '';
                    switch ($dati_competenze['cmp_val_arte']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi altri arte">
                        case 'BASE':
                            $html_to_return_cmp_val_arte .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_arte .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_arte .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_arte .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_arte .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_mus = '';
                    switch ($dati_competenze['cmp_val_mus']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi altri">
                        case 'BASE':
                            $html_to_return_cmp_val_mus .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_mus .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_mus .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_mus .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_mus .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_mot = '';
                    switch ($dati_competenze['cmp_val_mot']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi altri motoria">
                        case 'BASE':
                            $html_to_return_cmp_val_mot .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_mot .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_mot .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_mot .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_mot .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_mat = '';
                    switch ($dati_competenze['cmp_val_mat']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                        case 'BASE':
                            $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_mat .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_mat .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_sci_tec = '';
                    switch ($dati_competenze['cmp_val_sci_tec']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                        case 'BASE':
                            $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                            break;
                        default:
                            $html_to_return_cmp_val_sci_tec .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }

                    $html_to_return_cmp_val_sto_soc = '';
                    switch ($dati_competenze['cmp_val_sto_soc']) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="impostazione select linguaggi inglese">
                        case 'BASE':
                            $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION selected value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'NOBASE':
                            $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION selected value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'INTERMEDIO':
                            $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION selected value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        case 'AVANZATO':
                            $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION selected value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        default:
                            $html_to_return_cmp_val_sto_soc .= "\t\t\t<OPTION selected value=''>--Selezionare livello--</OPTION>"
                                    . "\t\t\t<OPTION value='BASE'>Livello base</OPTION>"
                                    . "\t\t\t<OPTION value='NOBASE'>Livello iniziale:</OPTION>"
                                    . "\t\t\t<OPTION value='INTERMEDIO'>Livello intermedio</OPTION>"
                                    . "\t\t\t<OPTION value='AVANZATO'>Livello avanzato</OPTION>";
                            break;
                        //}}} </editor-fold>
                    }
                    //}}} </editor-fold>

                    $html .= "<table width='100%'>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td colspan='2' align='center'>"
                            . "\t\t<b><font color='#000044' size='3'>CERTIFICAZIONE delle COMPETENZE AL TERMINE DEL PRIMO CICLO DI ISTRUZIONE</font></b>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Lingua Italiana:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_linguaggi_italiano'>"
                            . $html_to_return_cmp_val_ita
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_linguaggi_italiano' size='50' value='" . $dati_competenze['cmp_txt_ita'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Lingua Straniera 2:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_linguaggi_lingua2'>"
                            . $html_to_return_cmp_val_l2
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_linguaggi_lingua2' size='50' value='" . $dati_competenze['cmp_txt_l2'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Lingua Straniera 3:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_linguaggi_lingua3'>"
                            . $html_to_return_cmp_val_l3
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_linguaggi_lingua3' size='50' value='" . $dati_competenze['cmp_txt_l3'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Altri Linguaggi Arte e immagine:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_linguaggi_altri_arte'>"
                            . $html_to_return_cmp_val_arte
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_linguaggi_altri_arte' size='50' value='" . $dati_competenze['cmp_txt_arte'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Altri Linguaggi Musica:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_linguaggi_altri_musica'>"
                            . $html_to_return_cmp_val_mus
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_linguaggi_altri_musica' size='50' value='" . $dati_competenze['cmp_txt_mus'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE DEI LINGUAGGI - Altri Linguaggi Motoria:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_linguaggi_altri_motoria'>"
                            . $html_to_return_cmp_val_mot
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_linguaggi_altri_motoria' size='50' value='" . $dati_competenze['cmp_txt_mot'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE MATEMATICO:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_matematico'>"
                            . $html_to_return_cmp_val_mat
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_matematico' size='50' value='" . $dati_competenze['cmp_txt_mat'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE SCIENTIFICO-TECNOLOGICO:</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_scientifico_tecno'>"
                            . $html_to_return_cmp_val_sci_tec
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_scientifico_tecno' size='50' value='" . $dati_competenze['cmp_txt_sci_tec'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='right'>"
                            . "\t\t<font color='#000044'>Livello raggiunto per </font><b><font color='#000044'>ASSE STORICO-SOCIALE</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td>"
                            . "\t\t<SELECT name='valore_storico_sociale'>"
                            . $html_to_return_cmp_val_sto_soc
                            . "\t\t</SELECT>"
                            . "\t\t\t<input type='text' name='testo_storico_sociale' size='50' value='" . $dati_competenze['cmp_txt_sto_soc'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "</table>\n"
                            . "</div>\n";
                    //}}} </editor-fold>
                }
                else //if ($dati_classe["classe"] == '3')
                {
                    //{{{ <editor-fold defaultstate="collapsed" desc="nuova interfaccia">
                    $html .= "<table width='100%'>\n"
                            . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td colspan='3' align='center' class='sottotitolo_testo'>"
                            . "\t\t<b>CERTIFICAZIONE delle COMPETENZE AL TERMINE DEL PRIMO CICLO DI ISTRUZIONE</b><br>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";

                    if ($trentino_abilitato == "SI")
                    {
                        $arr_valori = [ 'A' => 'A - Livello base',
                                        'B' => 'B - Livello intermedio',
                                        'C' => 'C - Livello avanzato',
                                        '/' => '/',
                                 ];
                    }
                    else
                    {
                        $arr_valori = [ 'A' => 'A - Livello avanzato',
                                        'B' => 'B - Livello intermedio',
                                        'C' => 'C - Livello base',
                                        'D' => 'D - Livello iniziale',
                                 ];
                    }

                    foreach ($studente['competenze_scolastiche']['MM'] as $competenza)
                    {
                        if ($competenza['ordine_scolastico'] == 'MM'
                                    //&& $competenza['anno_fine'] == 0
                                    )
                        {
                            $id_competenza = $competenza['id_competenza_scolastica'];
                            $fill = !$fill;
                            if ($fill) {
                                $bgcolor = "#91E3DC";
                            } else {
                                $bgcolor = "#66BADC";
                            }

                            $html .= "\t<tr width='35%' style='background-color:" . $bgcolor . ";'>\n"
                                    . "\t\t<td class='padding_cella_generica' valign='top'>"
                                    . "\t\t" . decode($competenza['descrizione'])
                                    . "\t\t</td>\n"
                                    . "\t\t<td valign='top' width='20%' class='padding_cella_generica'><center>"
                                    . "\t\t" . str_replace("|", "<br>", decode($competenza['competenze_chiave']))
                                    . "\t\t</center></td>\n"
                                    . "\t\t<td  width='40%' class='padding_cella_generica'>";

                            // Select
                            if ($competenza['codice_competenza_sidi'] != 99){
                                $html .= "\t\t<SELECT name='mm_valore_" . $id_competenza . "'>";
                                $html .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>";

                                foreach ($arr_valori as $k => $valore)
                                {
                                    $sel = $competenza['valore'] == $k ? "selected" : "";

                                    // se nella descrizione della competenza c'e' scritto inglese, allora metto solo la possibilita' di selezionare inglese
                                    $html .= "\t\t\t<OPTION value='" . $k . "' " . $sel . ">" . $valore . "</OPTION>";
                                }
                            }

                            $html .= "\t\t</SELECT>";
                            if ($trentino_abilitato != "SI")
                            {
                                if ($competenza['codice_competenza_sidi'] == 18){
                                    // select con le lingue, che deve essere cmpilata obbligatoriamente se viene compilato il valore della competenza
                                    $html .= "\t\t<SELECT name='mm_testo_" . $id_competenza . "' data-obbligatorio='mm_valore_" . $id_competenza . "'>";
                                    $html .= "\t\t\t<OPTION value=''>--Selezionare lingua--</OPTION>";
                                    foreach ($array_lingue_straniere as $lingua)
                                    {
                                        $sel = $competenza['testo'] == $lingua ? "selected" : "";

                                        // se nella descrizione della competenza c'e' scritto inglese, allora metto solo la possibilita' di selezionare inglese
                                        if (strpos(strtolower($competenza['descrizione']), 'inglese') !== false){
                                            if (strpos(strtolower($lingua), 'inglese') === false){
                                                continue;
                                            }
                                        }

                                        $html .= "\t\t\t<OPTION value='" . $lingua . "' " . $sel . ">" . $lingua . "</OPTION>";
                                    }
                                    $html .= "\t\t</SELECT>\n";
                                } else {
                                    $html .= "\t\t\t<input type='text' name='mm_testo_" . $id_competenza ."' size='50' value='" . decode($competenza['testo']) . "' >\n";
                                }
                            }
                            $html .= "\t\t</td>\n"
                              . "\t</tr>\n";
                        }
                    }

                    $html .= "</table>\n"
                            . "</div>\n";
                    //}}} </editor-fold>
                }
                //}}} </editor-fold>
            }
            elseif ($dati_classe["tipo_indirizzo"] == 6 && $dati_classe["classe"] == '5')
            {
                //{{{ <editor-fold defaultstate="collapsed" desc="scuole elementari">
                if ($browser == 'ie') {
                    $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                } else {
                    $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
                }

                $html .= "<table width='100%'>\n"
                        . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                        . "\t\t<td colspan='3' align='center' class='sottotitolo_testo'>"
                        . "\t\t<b>CERTIFICAZIONE delle COMPETENZE AL TERMINE DELLA SCUOLA PRIMARIA</b>"
                        . "\t\t</td>\n"
                        . "\t</tr>\n";

                $arr_valori = [ 'A' => 'A - Livello avanzato',
                                'B' => 'B - Livello intermedio',
                                'C' => 'C - Livello base',
                                'D' => 'D - Livello iniziale',
                             ];

                foreach ($studente['competenze_scolastiche']['EE'] as $competenza)
                {
                    if ($competenza['ordine_scolastico'] == 'EE'
                                    //&& $competenza['anno_fine'] == 0
                                    )
                    {
                        $id_competenza = $competenza['id_competenza_scolastica'];
                        $fill = !$fill;
                        if ($fill) {
                            $bgcolor = "#91E3DC";
                        } else {
                            $bgcolor = "#66BADC";
                        }

                        $html .= "\t<tr width='35%' style='background-color:" . $bgcolor . ";'>\n"
                                . "\t\t<td class='padding_cella_generica' valign='top'>"
                                . "\t\t" . decode($competenza['descrizione'])
                                . "\t\t</td>\n"
                                . "\t\t<td valign='top' width='20%' class='padding_cella_generica'><center>"
                                . "\t\t" . str_replace("|", "<br>", decode($competenza['competenze_chiave']))
                                . "\t\t</center></td>\n"
                                . "\t\t<td  width='40%' class='padding_cella_generica'>";

                        if ($competenza['codice_competenza_sidi'] != 99){
                            // Select
                            $html .= "\t\t<SELECT name='ee_valore_" . $id_competenza . "'>";
                            $html .= "\t\t\t<OPTION value=''>--Selezionare livello--</OPTION>";

                            foreach ($arr_valori as $k => $valore)
                            {
                                $sel = $competenza['valore'] == $k ? "selected" : "";

                                $html .= "\t\t\t<OPTION value='" . $k . "' " . $sel . ">" . $valore . "</OPTION>";
                            }

                            $html .= "\t\t</SELECT>";
                        }

                        $html .= "\t\t\t<input type='text' name='ee_testo_" . $id_competenza ."' size='50' value='" . decode($competenza['testo']) . "'>\n"
                                . "\t\t</td>\n"
                                . "\t</tr>\n";
                    }
                }

                $html .= "</table>\n"
                        . "</div>\n";

                //}}} </editor-fold>
            }
            //}}} </editor-fold>
        }

        if ($parametro_portfolio == 'SI') {
            //{{{ <editor-fold defaultstate="collapsed" desc="sezione portfolio">
            $fill = !$fill;

            if ($fill) {
                $bgcolor = "#91E3DC";
            } else {
                $bgcolor = "#66BADC";
            }

            if ($browser == 'ie') {
                $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
            } else {
                $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width:' . ($schermo) . 'px;">' . "\n";
            }

            $html .= "<table width='100%'>\n"
                    . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td colspan='4' align='center'>"
                    . "\t\t<b><font color='#000044' size='3'>PORTFOLIO DELLO STUDENTE</font></b>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>A.S.</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Attività</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Descrizione</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Giudizio</b></font>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            foreach ($dati_portfolio as $cont_righe_port => $riga_portfolio) {
                echo "#####################################" . $cont_righe_port;
                //ciclo per pubblicare le righe del portfolio
                if ($riga_portfolio['id_portfolio'] > 0) {
                    //{{{ <editor-fold defaultstate="collapsed" desc="caso di riga popolata">
                    $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<SELECT name='anno_scolastico_" . $cont_righe_port . "'>"
                            . "\t\t\t<OPTION value=''>--Selezionare A.S.--</OPTION>";

                    foreach ($tabelle_portfolio['anno_scolastico'] as $anno_scol_portfolio) {
                        if ($riga_portfolio['anno_scolastico'] == $anno_scol_portfolio) {
                            $html .= "\t\t\t<OPTION selected value='" . $anno_scol_portfolio . "'>" . $anno_scol_portfolio . "</OPTION>";
                        } else {
                            $html .= "\t\t\t<OPTION value='" . $anno_scol_portfolio . "'>" . $anno_scol_portfolio . "</OPTION>";
                        }
                    }

                    $html .= "\t\t</SELECT>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<SELECT name='tipo_" . $cont_righe_port . "'>"
                            . "\t\t\t<OPTION selected value=''>--Selezionare attività--</OPTION>";

                    foreach ($tabelle_portfolio['tipo'] as $tipo_portfolio) {
                        if ($riga_portfolio['id_tipo_portfolio'] == $tipo_portfolio['id_tipo_portfolio']) {
                            $html .= "\t\t\t<OPTION selected value='" . $tipo_portfolio['id_tipo_portfolio'] . "'>" . $tipo_portfolio['descrizione'] . "</OPTION>";
                        } else {
                            $html .= "\t\t\t<OPTION value='" . $tipo_portfolio['id_tipo_portfolio'] . "'>" . $tipo_portfolio['descrizione'] . "</OPTION>";
                        }
                    }

                    $html .= "\t\t</SELECT>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t\t<input type='text' name='descrizione_" . $cont_righe_port . "' size='50' value='" . $riga_portfolio['descrizione'] . "'>\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<SELECT name='giudizio_" . $cont_righe_port . "'>"
                            . "\t\t\t<OPTION selected value=''>--Selezionare giudizio--</OPTION>";

                    foreach ($tabelle_portfolio['giudizio'] as $tipo_portfolio) {
                        if ($riga_portfolio['id_giudizio_portfolio'] == $tipo_portfolio['id_giudizio_portfolio']) {
                            $html .= "\t\t\t<OPTION selected value='" . $tipo_portfolio['id_giudizio_portfolio'] . "'>" . $tipo_portfolio['descrizione'] . "</OPTION>";
                        } else {
                            $html .= "\t\t\t<OPTION value='" . $tipo_portfolio['id_giudizio_portfolio'] . "'>" . $tipo_portfolio['descrizione'] . "</OPTION>";
                        }
                    }

                    $html .= "\t\t</SELECT>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t\t\t<input type='hidden' name='id_portfolio_" . $cont_righe_port . "' value='" . $riga_portfolio["id_portfolio"] . "'>\n";
                    //}}} </editor-fold>
                } else {
                    //{{{ <editor-fold defaultstate="collapsed" desc="caso di riga vuota">
                    $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<SELECT name='anno_scolastico_" . $cont_righe_port . "'>"
                            . "\t\t\t<OPTION selected value=''>--Selezionare A.S.--</OPTION>";
                    foreach ($tabelle_portfolio['anno_scolastico'] as $anno_scol_portfolio) {
                        $html .= "\t\t\t<OPTION value='" . $anno_scol_portfolio . "'>" . $anno_scol_portfolio . "</OPTION>";
                    }
                    $html .= "\t\t</SELECT>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<SELECT name='tipo_" . $cont_righe_port . "'>"
                            . "\t\t\t<OPTION selected value=''>--Selezionare attività--</OPTION>";
                    foreach ($tabelle_portfolio['tipo'] as $tipo_portfolio) {
                        $html .= "\t\t\t<OPTION value='" . $tipo_portfolio['id_tipo_portfolio'] . "'>" . $tipo_portfolio['descrizione'] . "</OPTION>";
                    }
                    $html .= "\t\t</SELECT>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t\t<input type='text' name='descrizione_" . $cont_righe_port . "' size='50' value=''>\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<SELECT name='giudizio_" . $cont_righe_port . "'>"
                            . "\t\t\t<OPTION selected value=''>--Selezionare giudizio--</OPTION>";
                    foreach ($tabelle_portfolio['giudizio'] as $tipo_portfolio) {
                        $html .= "\t\t\t<OPTION value='" . $tipo_portfolio['id_giudizio_portfolio'] . "'>" . $tipo_portfolio['descrizione'] . "</OPTION>";
                    }
                    $html .= "\t\t</SELECT>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n"
                            . "\t\t\t<input type='hidden' name='id_portfolio_'" . $cont_righe_port . " value=''>\n";
                    //}}} </editor-fold>
                }
            }

            $html .= "</table>\n"
                    . "</div>\n";
            //}}} </editor-fold>
        }
        $crediti_visibili = (
                                ($dati_classe["tipo_indirizzo"] == 0 && in_array($dati_classe["classe"],['3','4','5']))
                                ||
                                ($dati_classe["tipo_indirizzo"] == 1 && in_array($dati_classe["classe"],['1','2','3']))
                                ||
                                ($dati_classe["tipo_indirizzo"] == 2 && in_array($dati_classe["classe"],['3','4','5']))
                                ||
                                ($dati_classe["tipo_indirizzo"] == 3 && in_array($dati_classe["classe"],['3','4','5']))
                                ||
                                ($dati_classe["tipo_indirizzo"] == 5 && in_array($dati_classe["classe"],['2','3','4']))
                            );

        if ($parametro_estendi_info_pagelle_crediti == 'SI' && ($periodo == 9 || $periodo == 29) && $crediti_visibili) {
            //{{{ <editor-fold defaultstate="collapsed" desc="sezione crediti">
            $fill = !$fill;
            if ($fill) {
                $bgcolor = "#91E3DC";
            } else {
                $bgcolor = "#66BADC";
            }

            $html .= "<table width='100%'>\n"
                    . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td colspan='5' align='center'>"
                    . "\t\t<b><font color='#000044' size='3'>CREDITI DELLO STUDENTE</font></b>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Media voti</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Crediti Terza</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Crediti Quarta</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center' colspan='2'>"
                    . "\t\t<b><font color='#000044'>Crediti Quinta</b></font>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            $somma_valori = 0;
            $cont_media = 0;

            foreach ($mat_materie as $materia_per_media)
            {
                if ($materia_per_media['in_media_pagelle'] == 'SI' && intval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']) > 0)
                {
                    if ($trentino_abilitato == 'SI'
                        &&
                        verifica_presenza_debito($studente['id_studente'], $materia_per_media['id_materia'])
                        &&
                        floatval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']) < $parametri_voto['voto_minimo_suff']
                    )
                    {
                        $tmp_voto = $parametri_voto['voto_minimo_suff'];
                    }
                    else
                    {
                        $tmp_voto = floatval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']);
                    }
                    $somma_valori += $tmp_voto;
                    $cont_media++;
                }
            }

            if ($cont_media > 0) {
                $risultato_media = round($somma_valori / $cont_media, 2);
            } else {
                $risultato_media = '';
            }

            if ($risultato_media >= $parametri_voto['voto_minimo_suff']) {
                $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                        . "\t\t<td align='center' rowspan='2'>"
                        . "\t\t<b><font color='#000044'>$risultato_media</b></font>"
                        . "\t\t</td>\n";
            } else {
                $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                        . "\t\t<td align='center' rowspan='2'>"
                        . "\t\t<b><font color='#440000'>$risultato_media</b></font>"
                        . "\t\t</td>\n";
            }

            $lista_crediti = calcola_crediti_ammessi($risultato_media, $dati_classe['anno_reale_classe'], 'NO', $studente, $studente['pei'], $studente['bes']);

            switch ($dati_classe['anno_reale_classe']) {
                case '3':
                    $html_temp = '<select name="form_crediti">';
                    $html_temp .= '<option selected>0</option>';

                    if (is_array($lista_crediti)) {
                        foreach ($lista_crediti as $valore) {
                            if ($valore == $studente['crediti_terza']) {
                                $html_temp .= '<option selected>' . $valore . '</option>';
                            } else {
                                $html_temp .= '<option>' . $valore . '</option>';
                            }
                        }
                    } else {
                        $html_temp .= '<option selected>0</option>';
                    }

                    $html_temp .= '</select>';
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $html_temp . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['crediti_quarta'] . " (" . $studente['crediti_reintegrati_quarta'] . ")</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center' colspan='2'>"
                            . "\t\t<b><font color='#000044'>" . $studente['crediti_quinta'] . " (" . $studente['crediti_finali_agg'] . ")</b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";
                    break;
                case '4':
                    $html_temp = '<select name="form_crediti">';
                    $html_temp .= '<option selected>0</option>';

                    if (is_array($lista_crediti)) {
                        foreach ($lista_crediti as $valore) {
                            if ($valore == $studente['crediti_quarta']) {
                                $html_temp .= '<option selected>' . $valore . '</option>';
                            } else {
                                $html_temp .= '<option>' . $valore . '</option>';
                            }
                        }
                    } else {
                        $html_temp .= '<option selected>0</option>';
                    }

                    $html_temp .= '</select>';
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['crediti_terza'] . " </b></font>"
                            . "\t\t<b><font color='#000044'>Integrazione credito: <select name='crediti_integrazione_terza'></b></font>";
                    if($studente['crediti_reintegrati_terza'] == 1){
                        $html .= "\t\t\t<option value='0'>0</option>";
                        $html .= "\t\t\t<option selected value='1'>1</option>";
                    }else{
                        $html .= "\t\t\t<option value='0'>0</option>";
                        $html .= "\t\t\t<option value='1'>1</option>";
                    }
                    $html .= "\t\t</select>";

                    $html .= "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $html_temp . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center' colspan='2'>"
                            . "\t\t<b><font color='#000044'>" . $studente['crediti_quinta'] . " (" . $studente['crediti_finali_agg'] . ")</b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";
                    break;
                case '5':
                    $html_temp = '<select name="form_crediti">';
                    $html_temp .= '<option selected>0</option>';

                    if (is_array($lista_crediti)) {
                        foreach ($lista_crediti as $valore) {
                            if ($valore == $studente['crediti_quinta']) {
                                $html_temp .= '<option selected>' . $valore . '</option>';
                            } else {
                                $html_temp .= '<option>' . $valore . '</option>';
                            }
                        }
                    } else {
                        $html_temp .= '<option selected>0</option>';
                    }

                    $html_temp .= '</select>';
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['crediti_terza'] . " (" . $studente['crediti_reintegrati_terza'] . ")</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['crediti_quarta'] . " </b></font>"
                            . "\t\t<b><font color='#000044'>Integrazione credito: <select name='crediti_integrazione_quarta'></b></font>";
                    if($studente['crediti_reintegrati_quarta'] == 1){
                        $html .= "\t\t\t<option value='0'>0</option>";
                        $html .= "\t\t\t<option selected value='1'>1</option>";
                    }else{
                        $html .= "\t\t\t<option value='0'>0</option>";
                        $html .= "\t\t\t<option value='1'>1</option>";
                    }
                    $html .= "\t\t</select>";
                    $html .= "\t\t</td>\n"
                            . "\t\t<td align='center' colspan='2'>"
                            . "\t\t<b><font color='#000044'>" . $html_temp . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";
                    break;
                default:
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'></b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'></b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center' colspan='2'>"
                            . "\t\t<b><font color='#000044'></b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";

                    break;
            }

            $html .= "\t</tr>\n"
                    . "\t<tr style='background-color:" . $bgcolor . ";'>\n";

            switch ($dati_classe['anno_reale_classe']) {
                case '3':
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<textarea name='form_motivazione_crediti' rows=3 cols=50 >" . $studente['motivi_crediti_terza'] . "</textarea>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_quarta'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_quinta'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_agg'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";
                    break;
                case '4':
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_terza'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<textarea name='form_motivazione_crediti' rows=3 cols=50 >" . $studente['motivi_crediti_quarta'] . "</textarea>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_quinta'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_agg'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";
                    break;
                case '5':
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_terza'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_quarta'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<textarea name='form_motivazione_crediti' rows=3 cols=50 >" . $studente['motivi_crediti_quinta'] . "</textarea>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'>" . $studente['motivi_crediti_agg'] . "</b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";
                    break;
                default:
                    $html .= "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'></b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t<b><font color='#000044'></b></font>"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center' colspan='2'>"
                            . "\t\t<b><font color='#000044'></b></font>"
                            . "\t\t</td>\n"
                            . "\t</tr>\n";

                    break;
            }

            $html .= "\t</tr>\n"
                    . "</table>\n";

            if ($trentino_abilitato != 'SI' && $giudizio_sospeso_6_in_condotta == 'SI') {
                // funzione javascript per bloccare il valore piu' alto della fascia del credito impostabile se il voto di condotta non e' uguale o superiore a 9
                $html .= "<script>
                    const selectVotoCondotta = document.getElementsByName('voto_pagellina_" . $id_materia_condotta . "')[0];
                    const formCrediti = document.getElementsByName('form_crediti')[0];

                    selectVotoCondotta.addEventListener('change', function() {
                        const voto = parseInt(selectVotoCondotta.value);
                        const crediti = formCrediti.options;
                        if (voto >= 9){
                            crediti[1].disabled = false;
                            crediti[2].disabled = false;
                        } else if (isNaN(voto)){
                            crediti[1].disabled = true;
                            crediti[2].disabled = true;
                            crediti[0].selected = true;
                        } else {
                            crediti[1].disabled = false;
                            crediti[2].disabled = true;
                            if (crediti[2].selected) {
                                crediti[1].selected = true;
                            }
                        }
                    });

                    selectVotoCondotta.dispatchEvent(new Event('change'));
                </script>";
            }
            //}}} </editor-fold>
        }

        if ($parametro_debiti == 'SI') {
            //{{{ <editor-fold defaultstate="collapsed" desc="sezione debiti studente per trentino">
            $fill = !$fill;

            if ($fill) {
                $bgcolor = "#91E3DC";
            } else {
                $bgcolor = "#66BADC";
            }

            if ($browser == 'ie') {
                $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width: 100%;">' . "\n";
            } else {
                $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width: 100%;">' . "\n";
            }

            $html .= "<table width='100%'>\n"
                    . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td colspan='5' align='center'>"
                    . "\t\t<b><font color='#004400' size='3'>CARENZE RECUPERATE</font></b>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td align='center' rowspan='2'>"
                    . "\t\t<b><font color='#000044'>A.S.</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center' rowspan='2'>"
                    . "\t\t<b><font color='#000044'>Materia</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center' rowspan='2'>"
                    . "\t\t<b><font color='#000044'>Tipo Carenza</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center' colspan='2'>"
                    . "\t\t<b><font color='#000044'>Appello</b></font>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Tipo</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Data</b></font>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            foreach ($dati_debiti as $cont_righe_debiti_recuperati => $riga_debito_recuperato) {
                //ciclo per stampare tutti e soli i debiti recuperati
                if ($riga_debito_recuperato['debito_recuperato'] == 'SI') {
                    //{{{ <editor-fold defaultstate="collapsed" desc="caso di riga popolata">
                    $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t" . $riga_debito_recuperato['anno_scolastico'] . "\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t" . $riga_debito_recuperato['descrizione_materia'] . "\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>";
                    switch ($riga_debito_recuperato['tipo_carenza']) {
                        case 'carenza':
                            $html .="\t\tCARENZA\n";
                            break;
                        case 'carenza_grave':
                            $html .="\t\tCARENZA GRAVE\n";
                            break;
                        default:
                            $html .="&nbsp";
                            break;
                    }

                    $html .= "\t\t" . $riga_debito_recuperato['tipo_debito'] . "\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t" . $riga_debito_recuperato['tipo_appello'] . "\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t" . date('d/m/Y', $riga_debito_recuperato['data_recupero_debito']) . "\n"
                            . "\t\t</td>\n"
                            . "\t\t</tr>\n"
                    ;
                    //}}} </editor-fold>
                }
            }
            $html .= "</table>\n";

            $fill = !$fill;
            if ($fill) {
                $bgcolor = "#91E3DC";
            } else {
                $bgcolor = "#66BADC";
            }

            $html .= "<table width='100%'>\n"
                    . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td colspan='7' align='center'>"
                    . "\t\t<b><font color='#990000' size='3'>CARENZE DA RECUPERARE</font></b>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            $html .= "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>A.S.</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Materia</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Tipo Carenza</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Recuperato</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Appello</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Data Recupero</b></font>"
                    . "\t\t</td>\n"
                    . "\t\t<td align='center'>"
                    . "\t\t<b><font color='#000044'>Elimina</b></font>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            foreach ($dati_debiti as $cont_righe_debiti_recuperati => $riga_debito_recuperato) {
                //ciclo per stampare tutti e soli i debiti recuperati
                if ($riga_debito_recuperato['debito_recuperato'] == 'NO') {
                    //{{{ <editor-fold defaultstate="collapsed" desc="caso di riga popolata">
                    $html .="\t<tr style='background-color:" . $bgcolor . ";'>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t" . $riga_debito_recuperato['anno_scolastico'] . "\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>"
                            . "\t\t" . $riga_debito_recuperato['descrizione_materia'] . "\n"
                            . "\t\t</td>\n"
                            . "\t\t<td align='center'>\n";

                    /*
                      . "\t\t<select name='tipo_carenza_".$riga_debito_recuperato['id_debito']."'>\n";
                      switch ($riga_debito_recuperato['tipo_carenza'])
                      {
                      case 'carenza':
                      $html_to_return .="\t\t<option value=''>--</option>\n"
                      . "\t\t<option selected value='carenza'>Carenza</option>\n"
                      . "\t\t<option value='carenza_grave'>Carenza Grave</option>\n";
                      break;
                      case 'carenza_grave':
                      $html_to_return .="\t\t<option value=''>--</option>\n"
                      . "\t\t<option value='carenza'>Carenza</option>\n"
                      . "\t\t<option selected value='carenza_grave'>Carenza Grave</option>\n";
                      break;
                      default:
                      $html_to_return .="\t\t<option value=''>--</option>\n"
                      . "\t\t<option value='carenza'>Carenza</option>\n"
                      . "\t\t<option value='carenza_grave'>Carenza Grave</option>\n";
                      break;
                      }
                      $html_to_return .="\t\t</select>\n"

                     */
                    switch ($riga_debito_recuperato['tipo_carenza']) {
                        case 'carenza':
                            $html .="\t\tCARENZA\n";
                            break;
                        case 'carenza_grave':
                            $html .="\t\tCARENZA GRAVE\n";
                            break;
                        default:
                            $html .="&nbsp";
                            break;
                    }

                    $html .="\t\t" . $riga_debito_recuperato['tipo_debito'] . "</td>\n"
                            . "\t\t<td align='center'>";
                    $html .="\t\t<input type='checkbox' name='recupero_debito[]' value=" . $riga_debito_recuperato['id_debito'] . ">\n";
                    $html .="\t\t</td>\n"
                            . "\t\t<td align='center'>";
                    $html .="\t\t<select name='tipo_appello_debito_" . $riga_debito_recuperato['id_debito'] . "'>\n";

                    if ($riga_debito_recuperato['tipo_appello'] == 'PRIMO') {
                        $html .= "\t\t<OPTION selected value='PRIMO'>PRIMO</OPTION>\n";
                    } else {
                        $html .= "\t\t<OPTION value='PRIMO'>PRIMO</OPTION>\n";
                    }
                    if ($riga_debito_recuperato['tipo_appello'] == 'SECONDO') {
                        $html .= "\t\t<OPTION selected value='SECONDO'>SECONDO</OPTION>\n";
                    } else {
                        $html .= "\t\t<OPTION value='SECONDO'>SECONDO</OPTION>\n";
                    }
                    /*if ($riga_debito_recuperato['tipo_appello'] == 'TERZO') {
                        $html .= "\t\t<OPTION selected value='TERZO'>TERZO</OPTION>\n";
                    } else {
                        $html .= "\t\t<OPTION value='TERZO'>TERZO</OPTION>\n";
                    }
                    if ($riga_debito_recuperato['tipo_appello'] == 'QUARTO') {
                        $html .= "\t\t<OPTION selected value='QUARTO'>QUARTO</OPTION>\n";
                    } else {
                        $html .= "\t\t<OPTION value='QUARTO'>QUARTO</OPTION>\n";
                        if ($riga_debito_recuperato['tipo_appello'] == 'QUINTO') {
                            $html .= "\t\t<OPTION selected value='QUINTO'>QUINTO</OPTION>\n";
                        } else {
                            $html .= "\t\t<OPTION value='QUINTO'>QUINTO</OPTION>\n";
                        }
                    }*/
                    if ($riga_debito_recuperato['tipo_appello'] == 'NON_SUPERATO') {
                        $html .= "\t\t<OPTION selected value='NON_SUPERATO'>NON SUPERATO</OPTION>\n";
                    } else {
                        $html .= "\t\t<OPTION value='NON_SUPERATO'>NON SUPERATO</OPTION>\n";
                    }
                    if ($riga_debito_recuperato['tipo_appello'] == 'NESSUNO') {
                        $html .= "\t\t<OPTION selected value='NESSUNO'>NESSUN APPELLO</OPTION>\n";
                    } else {
                        $html .= "\t\t<OPTION value='NESSUNO'>NESSUN APPELLO</OPTION>\n";
                    }
                    $html .="\t\t</select>\n";
                    $html .="\t\t</td>\n"
                            . "\t\t<td align='center'>";
                    $params = array(
                        'prefix'       => 'data_recupero_' . $riga_debito_recuperato['id_debito'] . '_',
                        'start_year'   => $anno_fine - 10,
                        'end_year'     => $anno_fine,
                        'time'         => $riga_debito_recuperato['data_recupero_debito'],
                        'month_format' => "%m",
                        'field_order'  => "DMY"
                    );
                    $html .= smarty_function_html_select_date($params);
                    $html .="\t\t</td>\n"
                            . "\t\t<td align='center'>";

                    if ($riga_debito_recuperato['anno_scolastico'] == ($anno_fine - 1) . '/' . $anno_fine) {
                        $html .="\t\t<input type='checkbox' name='elimina_debito[]' value=" . $riga_debito_recuperato['id_debito'] . ">\n";
                    } else {
                        $html .="&nbsp;";
                    }

                    $html .="\t\t</td>\n"
                            . "\t\t</tr>\n"
                    ;
                    //}}} </editor-fold>
                }
            }

            for ($cont = 1; $cont < 6; $cont++) {
                /* {{{ */
                $html .="\t<tr style='background-color:" . $bgcolor . ";'>\n"
                        . "\t\t<td align='center'>\n"
                        . "\t\t" . ($anno_fine - 1) . '/' . $anno_fine . "\n"
                        . "\t\t</td>\n"
                        . "\t\t<td align='center'>\n"
                        . "\t\t<select name='descrizione_materia_debito_new_{$cont}'>\n"
                        . "\t\t<option value=''>--</option>\n";
                foreach ($mat_materie as $indice_materia => $materia) {
                    if (is_numeric($indice_materia)) {
                        $html .="\t\t<option value='" . $indice_materia . "##@@##" . $materia['nome_materia_breve'] . "'>" . $materia['nome_materia_breve'] . "</option>\n";
                    }
                }
                $html .="\t\t</select>\n"
                        . "\t\t</td>\n"
                        . "\t\t<td align='center'>\n"
                        . "\t\t<select name='tipo_carenza_new_{$cont}'>\n"
                        . "\t\t<option value=''>--</option>\n"
                        . "\t\t<option value='carenza'>CARENZA</option>\n"
                        . "\t\t<option value='carenza_grave'>CARENZA GRAVE</option>\n"
                        . "\t\t</select>\n"
                        . "\t\t<select name='tipo_debito_new_{$cont}'>\n"
                        . "\t\t<option value='CON VERIFICA'>CON VERIFICA</option>\n"
                        . "\t\t<option value='CON RECUPERO AUTONOMO'>CON RECUPERO AUTONOMO</option>\n"
                        . "\t\t</select>\n"
                        . "\t\t</td>\n"
                        . "\t\t<td align='center'>&nbsp;"
                        . "\t\t</td>\n"
                        . "\t\t<td align='center'>&nbsp;"
                        . "\t\t</td>\n"
                        . "\t\t<td align='center'>&nbsp;"
                        . "\t\t</td>\n"
                        . "\t\t<td align='center'>&nbsp;"
                        . "\t\t</td>\n"
                        . "\t\t</tr>\n"
                ;

                /* }}} */
            }

            $html .= "</table>\n"
                    . "</div>\n";
            //}}} </editor-fold>
        }

        $fill = !$fill;

        if ($fill)
        {
            $bgcolor = "#bfdaff";
        }
        else
        {
            $bgcolor = "#afc4ff";
        }

        if ($browser == 'ie') {
            $html .= '<div style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width: 100%;">' . "\n";
        } else {
            $html .= '<div style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width: 100%;">' . "\n";
        }


        // Gestione del campo "Consiglio Orientativo Trentino" da usare solo per le terze medie del Trentino
        // Indirizzo Secondaria I Grado
        // - Classi Terze
        // - Trentino
        // dal 18/05/2021 usata su tutte le scuole (e non solo nel trentino)
        if ($dati_classe['tipo_indirizzo'] == 4 && $dati_classe["classe"] == 3
                && $trentino_abilitato == 'SI'
            )
        {
            // Javascript per evitare la selezione di più di 4 consigli orientativi
            $html .= "<script>function conta_selezionati(obj) "
                    . "{ "
                    . " if((document.querySelectorAll('input[name=\"consiglio_orientativo_trentino[]\"]:checked').length) > 4) "
                    . "{ alert('ATTENZIONE! \\n Non è possibile selezionare più di 4 elementi'); obj.checked = false; "
                    . "}"
                    . "}</script>";

            //{{{ <editor-fold defaultstate="collapsed" desc="Consiglio Orientativo Trentino">

            if ($browser == 'ie') {
                $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * ($row_height + 6)) . 'px;left:0px; width: 100%;">' . "\n";
            } else {
                $html .= '<div class="div_' . $pref_classe . 'tabellone_top" style="position: static;top:' . ($row_start + $cont_row * $row_height ) . 'px;left:0px; width: 100%;">' . "\n";
            }

            $html .=  "<table width='100%' style='background-color:" . $bgcolor . ";'>\n"
                    . "<tr><td><br></td></tr>"
                    . "\t<tr style='background-color:" . $bgcolor . ";'>\n"
                    . "\t\t<td align='center' colspan='2'>"
                    . "\t\t<b><font color='#004400' size='3'>CONSIGLIO ORIENTATIVO</font></b>"
                    . "\t\t</td>\n"
                    . "\t</tr>\n";

            foreach ($valori_consiglio_orientativo_trentino as $valore_singolo)
            {
                $checked = "";
                if (in_array($valore_singolo['id_consiglio_orientativo_trentino'], explode(',', $studente['consiglio_orientativo_trentino'])))
                {
                    $checked = "CHECKED";
                }

                $html .= "<tr><td width='50%' align='right'>";
                $html .= "<input onclick='conta_selezionati(this);' type='checkbox' " . $checked . " name='consiglio_orientativo_trentino[]' value='" . $valore_singolo['id_consiglio_orientativo_trentino'] . "'></td>";
                $html .= "<td>" .  $valore_singolo['descrizione'] . "</td><tr>";

            }

            $html .=  "\t</tr>\n"
                    . "<tr><td><br></td></tr>"
                    . "</table>\n";

            $fill = !$fill;

            if ($fill) {
                $bgcolor = "#91E3DC";
            } else {
                $bgcolor = "#66BADC";
            }
            //}}} </editor-fold>
        }

        if ($periodo == 9 || $periodo == 29) {
            $esito = (strpos(strtolower($studente['esito_corrente_calcolato']), 'in corso') === false) ? $studente['esito_corrente_calcolato'] : 'Non ancora definito';

            $html .= "<div align='center' class='padding8' style='background-color:" . $bgcolor . "'>";
            $html .= "\tEsito: <b style='font-size: 110%;'>" . $esito . " </b>";
            $html .= "</div>";
        }

        $html .= '<table width="100%" border=\'1\'>' . "\n"
                . "\t<tr style='background-color:" . $bgcolor . ";'>\n";
        if ($abilita_pulsante_salva)
        {
            $html .= "\t\t<td align='center' style='border: none; width: 33%;'>"
                    . "<input type='button' value='Salva e torna al tabellone' onclick=\"salvaPagellinaTornaTabellone(this);\" class='sottotitolo_testo_bold'>\n"
                    . "</td>\n";
            $html .= "\t\t<td align='center' style='border: none; width: 33%;'>"
                    . "<input type='button' value='Salva' "
                    .   "onclick=\"salvaPagellina(this)\" "
                    .   "class='sottotitolo_testo_bold'>\n"
                    . "</td>\n";
        }
        $html .= "</form>\n"
                . "\t\t<td align='center' style='border: none; width: 33%;' class='padding8'>"
                . "<input type='button' value='Annulla' onclick=\"document.forms['form_edit'].submit();\" class='sottotitolo_testo_bold'>\n"
                . "</td>\n"
                . "\t</tr>\n"
                . "</table>\n"
                . "</div>\n";

    }

    return $html;
}
