<?php

function estrai_voti_per_statistica($start_interval, $end_interval, $ordinamento) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti inseriti dai docenti in un periodo specificato">
    $query = "SELECT
					utenti.cognome,
					utenti.nome,
                                        utenti.id_utente,
					(
						SELECT COUNT(voti.id_voto) FROM voti
                        WHERE voti.id_professore = utenti.id_utente
                            AND voti.flag_canc = 0
                            AND voti.data >= {$start_interval}
                            AND voti.data <= {$end_interval}
					) AS voti_inseriti_totali,
					(
						SELECT COUNT(voti.id_voto) FROM voti
                        WHERE voti.id_professore = utenti.id_utente
                            AND voti.flag_canc = 0
                            AND voti.tipo = '0'
                            AND voti.data >= {$start_interval}
                            AND voti.data <= {$end_interval}
					) AS voti_scritti_inseriti,
					(
						SELECT COUNT(voti.id_voto) FROM voti
                        WHERE voti.id_professore = utenti.id_utente
                            AND voti.flag_canc = 0
                            AND voti.tipo = '1'
                            AND voti.data >= {$start_interval}
                            AND voti.data <= {$end_interval}
					) AS voti_orali_inseriti,
					(
						SELECT COUNT(voti.id_voto) FROM voti
                        WHERE voti.id_professore = utenti.id_utente
                            AND voti.flag_canc = 0
                            AND voti.tipo = '2'
                            AND voti.data >= {$start_interval}
                            AND voti.data <= {$end_interval}
					) AS voti_pratici_inseriti
				FROM utenti
				WHERE utenti.flag_canc = 0
					AND utenti.tipo_utente = 'P'";

    if ($ordinamento == 'numero_voti_totali') {
        $query .= " ORDER BY
						voti_inseriti_totali,
						utenti.cognome,
						utenti.nome";
    } else {
        $query .= " ORDER BY
						utenti.cognome,
						utenti.nome";
    }

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            //inizializzo l'intervallo di tempo di accettabilità dei voti
            $voti[$cont] = pg_fetch_assoc($result, $cont);

            foreach ($voti[$cont] as $key => $value) {
                $voti[$cont][$key] = decode($value);
            }
        }
    }

    return $voti;
    //}}} </editor-fold>
}

function estrai_riepilogo_voti_studenti_classe($id_classe, $id_materia, $start_interval, $end_interval, $corsi_abbinati = "NO") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di tutti gli studenti in una materia di una classe in un periodo specificato">
if ($corsi_abbinati == "SI")
{
    $query = "SELECT
					distinct(voti.id_voto),
                    voti.id_studente,
					voti.data,
					voti.note,
					voti.tipo,
					voti.voto,
					studenti_completi.cognome,
					studenti_completi.nome,
					studenti_completi.registro,
					studenti_completi.id_classe,
					significati_voti.codice,
					significati_voti.valore
				FROM
					voti,
					studenti_completi,
					materie,
					significati_voti,
					tipi_voto,
                    materia_riferimento_studenti_corsi
				WHERE
                    (
                        voti.id_materia = '$id_materia'
                        OR voti.id_materia in (select id_materia from materie where materie.id_materia_riferimento = '$id_materia')
                        OR voti.id_materia in
                        (
                            SELECT materia_riferimento_studenti_corsi.id_corso
                            FROM
                                materia_riferimento_studenti_corsi,
                                studenti_completi
                            WHERE
                                studenti_completi.id_classe = '$id_classe'
                                AND materia_riferimento_studenti_corsi.id_studente = studenti_completi.id_studente
                                AND materia_riferimento_studenti_corsi.id_materia_riferimento = '$id_materia'
                        )
                    )
					AND voti.data >= '$start_interval'
					AND voti.data <= '$end_interval'
					AND studenti_completi.id_studente = voti.id_studente
					AND studenti_completi.id_classe = '$id_classe'
					AND significati_voti.voto = voti.voto
					AND significati_voti.id_tipo_voto = materie.tipo_valutazione::integer
					AND significati_voti.id_tipo_voto = tipi_voto.id_tipo_voto::integer
					AND tipi_voto.flag_canc = 0
					AND voti.flag_canc = 0
					AND materie.id_materia = voti.id_materia
					AND materie.flag_canc = 0
				ORDER BY
					studenti_completi.registro,
					voti.id_studente,
					voti.tipo,
					voti.data,
                    voti.id_voto";
}
else
{
    $query = "SELECT
					voti.id_studente,
					voti.id_voto,
					voti.data,
					voti.note,
					voti.tipo,
					voti.voto,
                    voti.id_professore,
					studenti_completi.cognome,
					studenti_completi.nome,
					studenti_completi.registro,
					studenti_completi.id_classe,
					significati_voti.codice,
					significati_voti.valore
				FROM
					voti,
					studenti_completi,
					materie,
					significati_voti,
					tipi_voto
				WHERE voti.id_materia = '$id_materia'
					AND voti.data >= '$start_interval'
					AND voti.data <= '$end_interval'
					AND studenti_completi.id_studente = voti.id_studente
					AND studenti_completi.id_classe = '$id_classe'
					AND significati_voti.voto = voti.voto
					AND significati_voti.id_tipo_voto = materie.tipo_valutazione::integer
					AND significati_voti.id_tipo_voto = tipi_voto.id_tipo_voto::integer
					AND tipi_voto.flag_canc = 0
					AND voti.flag_canc = 0
					AND materie.id_materia = voti.id_materia
					AND materie.flag_canc = 0
				ORDER BY
					studenti_completi.registro,
					voti.id_studente,
					voti.tipo,
					voti.data,
                    voti.id_voto";
}
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            //inizializzo l'intervallo di tempo di accettabilità dei voti
            $voti[$cont] = pg_fetch_assoc($result, $cont);
            $voti[$cont][0] = pg_fetch_result($result, $cont, "id_voto");
            $voti[$cont][1] = pg_fetch_result($result, $cont, "voto");
            $voti[$cont][2] = decode(pg_fetch_result($result, $cont, "note"));
            $voti[$cont][3] = pg_fetch_result($result, $cont, "tipo");
            $voti[$cont][4] = pg_fetch_result($result, $cont, "data");
            $voti[$cont][5] = pg_fetch_result($result, $cont, "id_studente");
            $voti[$cont][6] = decode(pg_fetch_result($result, $cont, "nome"));
            $voti[$cont][7] = decode(pg_fetch_result($result, $cont, "cognome"));
            $voti[$cont][8] = pg_fetch_result($result, $cont, "registro");
            $voti[$cont][9] = $voti[$cont]["codice"];
            $voti[$cont][10] = $numero;
            $voti[$cont][12] = $voti[$cont]["valore"];
            $voti[$cont][13] = pg_fetch_result($result, $cont, "id_professore");

            $voti[$cont]['nome'] = $voti[$cont][6];
            $voti[$cont]['cognome'] = $voti[$cont][7];

            $today = getdate($voti[$cont][4]);
            $voti[$cont][11] = $today['mday'] . "." . $today['mon'];
        }
    }

    return $voti;

    //}}} </editor-fold>
}

function estrai_tabellone_voti_studenti_classe_per_multivoto($id_classe, $id_materia, $start_interval, $end_interval, $id_professore = 0) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di tutti gli studenti di una classe in una materia in un periodo specificato">
    $dati_multivoto = [
        'dati_agg' => [
            'max_unici'   => 1, // colonne di voti unici da visualizzare (sempre almeno 1, per inserimento)
            'max_scritti' => 1, // colonne di voti scritti da visualizzare (sempre almeno 1, per inserimento)
            'max_orali'   => 1, // colonne di voti orali da visualizzare (sempre almeno 1, per inserimento)
            'max_pratici' => 1  // colonne di voti pratici da visualizzare (sempre almeno 1, per inserimento)
        ]
    ];

    $query_studenti = "SELECT id_studente, registro, cognome, nome, esonero_religione
						FROM studenti_completi
                        WHERE id_classe = {$id_classe}
						ORDER BY registro";

    $result_studenti = pgsql_query($query_studenti) or die("Invalid $query_studenti");
    $numero_studenti = pg_num_rows($result_studenti);

    if ($numero_studenti > 0) {
        for ($cont_stud = 0; $cont_stud < $numero_studenti; $cont_stud++) {
            $dati_studente = pg_fetch_assoc($result_studenti, $cont_stud);

            foreach ($dati_studente as $key => $value) {
                $dati_studente[$key] = decode($value);
            }

            $id_studente = $dati_studente['id_studente'];
            $dati_multivoto['studenti'][$id_studente]['dati_studente'] = $dati_studente;

            //estrazione voti dello studente
            $query_voti = "SELECT id_voto, voto, tipo, data, note, id_materia
							FROM voti
							WHERE flag_canc = 0
                                AND id_studente = {$id_studente}
                                AND id_materia = {$id_materia}
                                AND data >= {$start_interval}
								AND data <= {$end_interval} ";

            if ($id_professore > 0) {
                $query_voti .= " AND id_professore = {$id_professore}";
            }

            $query_voti .= " ORDER BY tipo";

            $result_voti = pgsql_query($query_voti) or die("Invalid $query_voti");
            $numero_voti = pg_num_rows($result_voti);

            if ($numero_voti > 0) {
                $max_unici_stud = 1;
                $max_scritti_stud = 1;
                $max_orali_stud = 1;
                $max_pratici_stud = 1;

                for ($cont_voti = 0; $cont_voti < $numero_voti; $cont_voti++) {
                    //ciclo sui voti dello studente
                    $dati_voto = pg_fetch_assoc($result_voti, $cont_voti);

                    foreach ($dati_voto as $key => $value) {
                        $dati_voto[$key] = decode($value);
                    }

                    //{{{ <editor-fold defaultstate="collapsed" desc="in base al tipo di voto, aumento i totali dei voti per tipo dello studente e metto il voto nel corrispondente array">
                    $voto_unico_abilitato = estrai_voto_unico_abbinamento((int)$id_materia, (int)$id_classe);

                    if ($voto_unico_abilitato == '1') {
                        $max_unici_stud++;
                        $dati_multivoto['studenti'][$id_studente]['unici'][] = $dati_voto;
                    } else {
                        if ($dati_voto['tipo'] == 0) {   //voto scritto
                            $max_scritti_stud++;
                            $dati_multivoto['studenti'][$id_studente]['scritti'][] = $dati_voto;
                        } elseif ($dati_voto['tipo'] == 1) {  //voto orale
                            $max_orali_stud++;
                            $dati_multivoto['studenti'][$id_studente]['orali'][] = $dati_voto;
                        } elseif ($dati_voto['tipo'] == 2) {  //voto pratico
                            $max_pratici_stud++;
                            $dati_multivoto['studenti'][$id_studente]['pratici'][] = $dati_voto;
                        }
                    }
                    //}}} </editor-fold>
                }

                //aggiorno i totali delle colonne
                if ($max_unici_stud > $dati_multivoto['dati_agg']['max_unici']) {
                    $dati_multivoto['dati_agg']['max_unici'] = $max_unici_stud;
                }

                if ($max_scritti_stud > $dati_multivoto['dati_agg']['max_scritti']) {
                    $dati_multivoto['dati_agg']['max_scritti'] = $max_scritti_stud;
                }

                if ($max_orali_stud > $dati_multivoto['dati_agg']['max_orali']) {
                    $dati_multivoto['dati_agg']['max_orali'] = $max_orali_stud;
                }

                if ($max_pratici_stud > $dati_multivoto['dati_agg']['max_pratici']) {
                    $dati_multivoto['dati_agg']['max_pratici'] = $max_pratici_stud;
                }
            }
        }
    }

    return $dati_multivoto;
    //}}} </editor-fold>
}

/**
 * Funzione per estrarre i voti di tutti gli studenti in una materia di una
 * classe in un periodo specificato
 *
 * @param type $id_classe
 * @param type $id_materia
 * @param type $data_inizio
 * @param type $data_fine
 * @param type $filtro_tipo_voto
 * @param type $id_professore
 * @param type $ordinamento
 * @param type $id_campo_libero
 * @return type
 */
function estrai_tabellone_voti_studenti_classe($id_classe, $id_materia, $data_inizio, $data_fine, $filtro_tipo_voto = 'X', $id_professore = 0, $ordinamento = '', $id_campo_libero = null) {
    //{{{ <editor-fold defaultstate="collapsed">
    $id_classe = (int) $id_classe;
    $id_materia = (int) $id_materia;
    $data_inizio = (int) $data_inizio;
    $data_fine = (int) $data_fine;
    $id_professore = (int) $id_professore;
    $id_campo_libero = (int) $id_campo_libero;

    $array_voti = [];

    $query = "SELECT
                    voti.id_studente,
                    voti.id_voto,
                    voti.id_professore,
                    voti.data,
                    voti.note,
                    voti.tipo,
                    voti.tipo_aggiuntivo,
                    voti.voto,
                    studenti_completi.cognome,
                    studenti_completi.nome,
                    studenti_completi.registro,
                    studenti_completi.id_classe,
					significati_voti.codice,
					significati_voti.valore
                FROM
                    voti,
                    studenti_completi,
                    materie,
                    significati_voti,
                    tipi_voto
                WHERE ";

    if ($id_professore > 0) {
        $query .= " voti.id_professore = '$id_professore'
					AND ";
    }

    if (is_numeric($filtro_tipo_voto)) {
        $query .= " voti.tipo = '{$filtro_tipo_voto}'
					AND ";
    }

    if ($id_campo_libero > 0) {
        $query .= " voti.id_voto IN (
                        SELECT id_oggetto FROM valori_campi_liberi
                        JOIN campi_liberi ON valori_campi_liberi.id_campo_libero = campi_liberi.id_campo_libero
                        JOIN campi_liberi_classe_materia ON valori_campi_liberi.id_campo_libero = campi_liberi_classe_materia.id_campo_libero
                        WHERE campi_liberi.id_campo_libero = {$id_campo_libero}
                            AND campi_liberi.tipo LIKE '%-OBIETTIVI_VOTI-%'
                            AND campi_liberi.flag_canc = 0
                            AND valori_campi_liberi.flag_canc = 0
                            AND (
                                campi_liberi_classe_materia.id_classe = {$id_classe}
                                OR campi_liberi_classe_materia.id_classe IN (
                                    SELECT -1 * id_indirizzo FROM classi_complete
                                    WHERE id_classe = {$id_classe}
                                )
                                OR campi_liberi_classe_materia.id_classe = 0
                            )
                            AND (
                                campi_liberi_classe_materia.id_materia = {$id_materia}
                                OR campi_liberi_classe_materia.id_materia = 0
                            )
                    )
					AND ";
    }

    $query .= " voti.id_materia = '{$id_materia}'
				AND voti.data >= '{$data_inizio}'
                AND voti.data <= '{$data_fine}'
				AND studenti_completi.id_studente = voti.id_studente
                AND studenti_completi.id_classe = '{$id_classe}'
				AND voti.flag_canc = 0
                AND significati_voti.voto = voti.voto
                AND significati_voti.id_tipo_voto = materie.tipo_valutazione::integer
                AND significati_voti.id_tipo_voto = tipi_voto.id_tipo_voto::integer
                AND tipi_voto.flag_canc = 0
                AND materie.id_materia = '{$id_materia}'
				AND materie.flag_canc = 0 ";

    if ($ordinamento == 'voto_piu_recente') {
        $query .= " ORDER BY voti.data desc,
					studenti_completi.registro,
					voti.id_studente,
					voti.tipo";
    } else {
        $query .= " ORDER BY studenti_completi.registro,
					voti.id_studente,
					voti.data,
					voti.data_inserimento,
					voti.tipo";
    }

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
			$competenze = [];
            $dati_voto = pg_fetch_assoc($result, $cont);

            foreach ($dati_voto as $key => $value) {
                $dati_voto[$key] = decode($value);
            }

            $campi_liberi = estrai_valori_campi_liberi_materia_studente_per_certificati($dati_voto['id_voto']);

            if (is_array($campi_liberi) and count($campi_liberi) > 0) {
                foreach ($campi_liberi as $key => $value) {
					if (strpos($value['tipo'],'-OBIETTIVI_VOTI-') !== false) {
						$campi_liberi[$key]['id_obiettivo'] = $value['id_campo_libero'];
					}
                }
                foreach ($campi_liberi as $key => $value) {
					if (strpos($value['tipo'],'-COMPETENZE_VOTI-') !== false) {
						$competenze[] = [
                            'id_competenza' => $value['id_campo_libero'],
                            'id_valore'     => $value['id_valore_campo_libero'],
                            'id_select'     => $value['id_valore_precomp']
                        ];
                    }
                }

                $dati_voto['campi_liberi'] = $campi_liberi;
                $dati_voto['competenze'] = $competenze;
            }

            $array_voti[$dati_voto['id_studente']][] = $dati_voto;
        }

        if (is_array($array_voti) && $ordinamento == 'voto_piu_recente') {
            $array_voti = array_reverse($array_voti, true);
        }
    }

    return $array_voti;
    //}}} </editor-fold>
}

function estrai_tabellone_temporale_voti_studenti_classe($id_classe, $id_materia, $start_interval, $end_interval, $filtro_tipo_voto = 'X', $id_professore = 0, $ordinamento = '') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Riorganizza l'array del tabellone dei voti su base giornaliera">
    $voti = estrai_tabellone_voti_studenti_classe($id_classe, $id_materia, $start_interval, $end_interval, $filtro_tipo_voto, $id_professore, $ordinamento);

    if (is_array($voti) && count($voti) > 0) {
        foreach ($voti as $id_studente => $voti_studente) {
            if (is_array($voti_studente) && count($voti_studente) > 0) {
                foreach ($voti_studente as $voto) {
                    $dati[date('d/m/Y', $voto['data'])][$id_studente][] = $voto;
                }
            }
        }
    }

    return $dati;
    //}}} </editor-fold>
}

/**
 * Estrae le medie voto degli studenti
 *
 * @param int $id_classe
 * @param int $id_materia
 * @param int $start_interval
 * @param int $end_interval
 * @param string $filtro_tipo_voto
 * @param int $id_professore
 * @return array
 */
function estrai_tabellone_temporale_medie_studenti_classe($id_classe, $id_materia, $start_interval, $end_interval, $filtro_tipo_voto = 'X', $id_professore = 0)
{
/*{{{ */

    $medie_voto = [];
    $voti = estrai_tabellone_voti_studenti_classe($id_classe, $id_materia, $start_interval, $end_interval, $filtro_tipo_voto, $id_professore);

    // ho trovato dei voti
    if (is_array($voti) && count($voti) > 0) {

        // ciclo i voti per studente
        foreach ($voti as $id_studente => $voti_studente) {

            // trovo voti per studente
            if (is_array($voti_studente) && count($voti_studente) > 0) {

                // per ogni voto
                foreach ($voti_studente as $voto) {

                    $tipo_voto = $voto['tipo'];

                    if (is_numeric($voto['voto'])) {
                        switch ($tipo_voto) {
                           case 0:

                               $medie_voto[$id_studente]['numero']['scritto']++;
                               $medie_voto[$id_studente]['somma']['scritto'] += $voto['voto'];
                               break;
                           case 1:

                               $medie_voto[$id_studente]['numero']['orale']++;
                               $medie_voto[$id_studente]['somma']['orale'] += $voto['voto'];
                               break;
                           case 2:

                               $medie_voto[$id_studente]['numero']['pratico']++;
                               $medie_voto[$id_studente]['somma']['pratico'] += $voto['voto'];
                               break;

                        }
                        $medie_voto[$id_studente]['numero']['totale']++;
                        $medie_voto[$id_studente]['somma']['totale'] += $voto['voto'];
                    }
                }
            }
            $medie_voto[$id_studente]['medie'] = calcola_media_voti_studente_singola_materia($id_studente, $id_materia, $start_interval, $end_interval, 'XX', 2);
        }
    }

    // calcolo le medie voto per obiettivi
    $medie_obiettivi = estrai_obiettivi_medie_studenti($id_classe, $id_materia, $start_interval, $end_interval);

    foreach ($medie_obiettivi as $id => $media_studente) {
        foreach ($media_studente as $media_campo_libero) {
            $medie_voto[$id]['media_campi_liberi'][$media_campo_libero['id_obiettivo']] = $media_campo_libero['valore'];
        }
    }

    return $medie_voto;
	/*}}}*/
}

function calcola_media_voti_studente_singola_materia($id_studente, $id_materia, $start_interval, $end_interval, $arrotondamento, $arrotondamento_cifre, $corsi_abbinati = "NO", $tipo_voto_stampa = '', $tipo_media = 'ARITMETICA') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una materia di uno studente per il periodo interessato">

    if($corsi_abbinati == "SI")
    {
         $query = "SELECT
                        voti.tipo,
                        voti.voto,
                        pesi.peso
                    FROM
                        voti LEFT JOIN pesi on voti.id_peso = pesi.id_peso,
                        materie,
                        studenti
                    WHERE
                        voti.data >= {$start_interval}
                        AND voti.data <= {$end_interval}
                        AND voti.id_studente = {$id_studente}
                        AND
                            (
                                voti.id_materia = {$id_materia}
                                OR voti.id_materia in (select id_materia from materie where materie.id_materia_riferimento = '{$id_materia}')
                                OR voti.id_materia in
                                (
                                    SELECT materia_riferimento_studenti_corsi.id_corso
                                    FROM
                                        materia_riferimento_studenti_corsi
                                    WHERE
                                        materia_riferimento_studenti_corsi.id_studente = {$id_studente}
                                        AND materia_riferimento_studenti_corsi.id_materia_riferimento = '{$id_materia}'
                                )
                            )
                        AND voti.flag_canc = 0
                        AND studenti.id_studente = {$id_studente}
                        AND studenti.flag_canc = 0
                        AND materie.id_materia = {$id_materia}
                        AND materie.flag_canc = 0
                    ORDER BY
                        voti.tipo,
                        voti.data";
    }
    else
    {
        $query = "SELECT voti.tipo,
                        voti.voto,
                        pesi.peso
                    FROM voti LEFT JOIN pesi on voti.id_peso = pesi.id_peso,
                        materie,
                        studenti
                    WHERE voti.data >= {$start_interval}
                        AND voti.data <= {$end_interval}
                        AND voti.id_studente = {$id_studente}
                        AND voti.id_materia = {$id_materia}
                        AND voti.flag_canc = 0
                        AND studenti.id_studente = {$id_studente}
                        AND studenti.flag_canc = 0
                        AND materie.id_materia = {$id_materia}
                        AND materie.flag_canc = 0
                    ORDER BY voti.tipo, voti.data";
    }
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $tot_voti = 0;
    $tot_scritto = 0;
    $tot_orale = 0;
    $tot_pratico = 0;
    $cont_voti_scritti = 0;
    $cont_voti_orali = 0;
    $cont_voti_pratici = 0;
    $cont_tot_voti = 0;
    $media_scritto = 0;
    $media_pratico = 0;
    $media_orale = 0;
    $media_totale = 0;

    $tot_scritto_ponderata = $tot_orale_ponderata = $tot_pratico_ponderata = $tot_totale_ponderata = 0;
    $tot_peso_scritto = $tot_peso_orale = $tot_peso_pratico = $tot_peso_totale = 0;

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $array_voti[$cont] = pg_fetch_assoc($result, $cont);

            if (is_numeric($array_voti[$cont]["voto"])) {
                $tot_voti += $array_voti[$cont]["voto"];
                $cont_tot_voti++;

                if ( ($tot_peso_totale != 'N' or $tot_peso_totale == '0') and $array_voti[$cont]['peso'] != '')
                {
                    $tot_totale_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                    $tot_peso_totale += $array_voti[$cont]['peso'];
                }
                else {
                    $tot_peso_totale = 'N';
                }

                switch ($array_voti[$cont]["tipo"]) {
                    case "0":
                        $cont_voti_scritti++;
                        $tot_scritto += $array_voti[$cont]["voto"];

                        if ( ($tot_peso_scritto != 'N' or $tot_peso_scritto == '0') and $array_voti[$cont]['peso'] != '')
                        {
                            $tot_scritto_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                            $tot_peso_scritto += $array_voti[$cont]['peso'];
                        }
                        else {
                            $tot_peso_scritto = 'N';
                        }

                        break;
                    case "1":
                        $cont_voti_orali++;
                        $tot_orale += $array_voti[$cont]["voto"];

                        if ( ($tot_peso_orale != 'N' or $tot_peso_orale == '0') and $array_voti[$cont]['peso'] != '')
                        {
                            $tot_orale_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                            $tot_peso_orale += $array_voti[$cont]['peso'];
                        }
                        else {
                            $tot_peso_orale = 'N';
                        }

                        break;
                    case "2":
                        $cont_voti_pratici++;
                        $tot_pratico += $array_voti[$cont]["voto"];

                        if ( ($tot_peso_pratico != 'N' or $tot_peso_pratico == '0') and $array_voti[$cont]['peso'] != '')
                        {
                            $tot_pratico_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                            $tot_peso_pratico += $array_voti[$cont]['peso'];
                        }
                        else {
                            $tot_peso_pratico = 'N';
                        }

                        break;
                }
            }
        }

        $media_scritto = 0;
        $media_orale = 0;
        $media_pratico = 0;
        $media_totale = 0;

        $media_scritto_ponderata = 0;
        $media_orale_ponderata = 0;
        $media_pratico_ponderata = 0;
        $media_totale_ponderata = 0;

        if ($cont_voti_scritti > 0) {
            $media_scritto = round(($tot_scritto / $cont_voti_scritti), $arrotondamento_cifre);
        }

        if ($cont_voti_orali > 0) {
            $media_orale = round(($tot_orale / $cont_voti_orali), $arrotondamento_cifre);
        }

        if ($cont_voti_pratici > 0) {
            $media_pratico = round(($tot_pratico / $cont_voti_pratici), $arrotondamento_cifre);
        }

        if ($cont_tot_voti > 0) {
            $media_totale = round(($tot_voti / $cont_tot_voti), $arrotondamento_cifre);
        }


        if ($tot_peso_scritto != 'N') {
            $media_scritto_ponderata = (round($tot_scritto_ponderata/$tot_peso_scritto,$arrotondamento_cifre) > 0 ? (string)round($tot_scritto_ponderata/$tot_peso_scritto,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_voti_scritti == '0') {
                $media_scritto_ponderata = '--';
            }
            else {
                $media_scritto_ponderata = 'NC';
            }
        }

        if ($tot_peso_orale != 'N') {
            $media_orale_ponderata = (round($tot_orale_ponderata/$tot_peso_orale,$arrotondamento_cifre) > 0 ? (string)round($tot_orale_ponderata/$tot_peso_orale,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_voti_orali == '0') {
                $media_orale_ponderata = '--';
            }
            else {
                $media_orale_ponderata = 'NC';
            }
        }

        if ($tot_peso_pratico != 'N') {
            $media_pratico_ponderata = (round($tot_pratico_ponderata/$tot_peso_pratico,$arrotondamento_cifre) > 0 ? (string)round($tot_pratico_ponderata/$tot_peso_pratico,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_voti_pratici == '0') {
                $media_pratico_ponderata = '--';
            }
            else {
                $media_pratico_ponderata = 'NC';
            }
        }

        if ($tot_peso_totale != 'N') {
            $media_totale_ponderata = (round($tot_totale_ponderata/$tot_peso_totale,$arrotondamento_cifre) > 0 ? (string)round($tot_totale_ponderata/$tot_peso_totale,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_tot_voti == '0') {
                $media_totale_ponderata = '--';
            }
            else {
                $media_totale_ponderata = 'NC';
            }
        }


        $dati_materia = estrai_dati_materia($id_materia);
        $significati_voto = estrai_significati_voti($dati_materia[13]);

        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento scritto">
        if ($arrotondamento != "XX" && $cont_voti_scritti > 0) {
            $media_scritto = round(($tot_scritto / $cont_voti_scritti), 2);
            $voto_scritto_temp = intval($tot_scritto / $cont_voti_scritti);
            $resto_voto_scritto_temp = ((($tot_scritto / $cont_voti_scritti) - $voto_scritto_temp) * 100);

            $voto_scritto_pond_temp = intval($tot_scritto_ponderata/$tot_peso_scritto);
            $resto_voto_scritto_pond_temp = ((($tot_scritto_ponderata/$tot_peso_scritto) - $voto_scritto_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_scritto_temp >= 0 && $resto_voto_scritto_temp < 12.5) {
                $voto_scritto_temp = $voto_scritto_temp;
            }

            if ($resto_voto_scritto_temp >= 12.5 && $resto_voto_scritto_temp < 37.5) {
                $voto_scritto_temp = $voto_scritto_temp + 0.25;
            }

            if ($resto_voto_scritto_temp >= 37.5 && $resto_voto_scritto_temp < 62.5) {
                $voto_scritto_temp = $voto_scritto_temp + 0.5;
                $voto_scritto_temp = $voto_scritto_temp . "0";
            }

            if ($resto_voto_scritto_temp >= 62.5 && $resto_voto_scritto_temp < 87.5) {
                $voto_scritto_temp = $voto_scritto_temp + 0.75;
            }

            if ($resto_voto_scritto_temp >= 87.5 && $resto_voto_scritto_temp < 100) {
                $voto_scritto_temp = $voto_scritto_temp + 1;
            }


            if ($resto_voto_scritto_pond_temp >= 0 && $resto_voto_scritto_pond_temp < 12.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp;
            }

            if ($resto_voto_scritto_pond_temp >= 12.5 && $resto_voto_scritto_pond_temp < 37.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.25;
            }

            if ($resto_voto_scritto_pond_temp >= 37.5 && $resto_voto_scritto_pond_temp < 62.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.5;
                $voto_scritto_pond_temp = $voto_scritto_pond_temp . "0";
            }

            if ($resto_voto_scritto_pond_temp >= 62.5 && $resto_voto_scritto_pond_temp < 87.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.75;
            }

            if ($resto_voto_scritto_pond_temp >= 87.5 && $resto_voto_scritto_pond_temp < 100) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if ($resto_voto_scritto_temp >= 0 && $resto_voto_scritto_temp < 25) {
                $voto_scritto_temp = $voto_scritto_temp;
            }

            if ($resto_voto_scritto_temp >= 25 && $resto_voto_scritto_temp < 75) {
                $voto_scritto_temp = $voto_scritto_temp + 0.5;
                $voto_scritto_temp = $voto_scritto_temp . "0";
            }

            if ($resto_voto_scritto_temp >= 75 && $resto_voto_scritto_temp < 100) {
                $voto_scritto_temp = $voto_scritto_temp + 1;
            }


            if ($resto_voto_scritto_pond_temp >= 0 && $resto_voto_scritto_pond_temp < 25) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp;
            }

            if ($resto_voto_scritto_pond_temp >= 25 && $resto_voto_scritto_pond_temp < 75) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.5;
                $voto_scritto_pond_temp = $voto_scritto_pond_temp . "0";
            }

            if ($resto_voto_scritto_pond_temp >= 75 && $resto_voto_scritto_pond_temp < 100) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_scritto_temp >= 0 && $resto_voto_scritto_temp < 50) {
                $voto_scritto_temp = $voto_scritto_temp;
            }

            if ($resto_voto_scritto_temp >= 50 && $resto_voto_scritto_temp < 100) {
                $voto_scritto_temp = $voto_scritto_temp + 1;
            }


            if ($resto_voto_scritto_pond_temp >= 0 && $resto_voto_scritto_pond_temp < 50) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp;
            }

            if ($resto_voto_scritto_pond_temp >= 50 && $resto_voto_scritto_pond_temp < 100) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 1;
            }
        }

        if ($arrotondamento != "XX" && $cont_voti_scritti > 0) {
            $media_scritto = $voto_scritto_temp;

            if ($tot_peso_scritto != 'N') {
                $media_scritto_ponderata = $voto_scritto_pond_temp;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento orale">
        if ($arrotondamento != "XX" && $cont_voti_orali > 0) {
            $media_orale = round(($tot_orale / $cont_voti_orali), 2);
            $voto_orale_temp = intval($tot_orale / $cont_voti_orali);
            $resto_voto_orale_temp = (($tot_orale / $cont_voti_orali) - $voto_orale_temp) * 100;

            $voto_orale_pond_temp = intval($tot_orale_ponderata/$tot_peso_orale);
            $resto_voto_orale_pond_temp = ((($tot_orale_ponderata/$tot_peso_orale) - $voto_orale_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_orale_temp >= 0 && $resto_voto_orale_temp < 12.5) {
                $voto_orale_temp = $voto_orale_temp;
            }

            if ($resto_voto_orale_temp >= 12.5 && $resto_voto_orale_temp < 37.5) {
                $voto_orale_temp = $voto_orale_temp + 0.25;
            }

            if ($resto_voto_orale_temp >= 37.5 && $resto_voto_orale_temp < 62.5) {
                $voto_orale_temp = $voto_orale_temp + 0.5;
                $voto_orale_temp = $voto_orale_temp . "0";
            }

            if ($resto_voto_orale_temp >= 62.5 && $resto_voto_orale_temp < 87.5) {
                $voto_orale_temp = $voto_orale_temp + 0.75;
            }

            if ($resto_voto_orale_temp >= 87.5 && $resto_voto_orale_temp < 100) {
                $voto_orale_temp = $voto_orale_temp + 1;
            }


            if ($resto_voto_orale_pond_temp >= 0 && $resto_voto_orale_pond_temp < 12.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp;
            }

            if ($resto_voto_orale_pond_temp >= 12.5 && $resto_voto_orale_pond_temp < 37.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.25;
            }

            if ($resto_voto_orale_pond_temp >= 37.5 && $resto_voto_orale_pond_temp < 62.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.5;
                $voto_orale_pond_temp = $voto_orale_pond_temp . "0";
            }

            if ($resto_voto_orale_pond_temp >= 62.5 && $resto_voto_orale_pond_temp < 87.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.75;
            }

            if ($resto_voto_orale_pond_temp >= 87.5 && $resto_voto_orale_pond_temp < 100) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if ($resto_voto_orale_temp >= 0 && $resto_voto_orale_temp < 25) {
                $voto_orale_temp = $voto_orale_temp;
            }

            if ($resto_voto_orale_temp >= 25 && $resto_voto_orale_temp < 75) {
                $voto_orale_temp = $voto_orale_temp + 0.5;
                $voto_orale_temp = $voto_orale_temp . "0";
            }

            if ($resto_voto_orale_temp >= 75 && $resto_voto_orale_temp < 100) {
                $voto_orale_temp = $voto_orale_temp + 1;
            }


            if ($resto_voto_orale_pond_temp >= 0 && $resto_voto_orale_pond_temp < 25) {
                $voto_orale_pond_temp = $voto_orale_pond_temp;
            }

            if ($resto_voto_orale_pond_temp >= 25 && $resto_voto_orale_pond_temp < 75) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.5;
                $voto_orale_pond_temp = $voto_orale_pond_temp . "0";
            }

            if ($resto_voto_orale_pond_temp >= 75 && $resto_voto_orale_pond_temp < 100) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_orale_temp >= 0 && $resto_voto_orale_temp < 50) {
                $voto_orale_temp = $voto_orale_temp;
            }
            if ($resto_voto_orale_temp >= 50 && $resto_voto_orale_temp < 100) {
                $voto_orale_temp = $voto_orale_temp + 1;
            }


            if ($resto_voto_orale_pond_temp >= 0 && $resto_voto_orale_pond_temp < 50) {
                $voto_orale_pond_temp = $voto_orale_pond_temp;
            }

            if ($resto_voto_orale_pond_temp >= 50 && $resto_voto_orale_pond_temp < 100) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 1;
            }
        }

        if ($arrotondamento != "XX" && $cont_voti_orali > 0) {
            $media_orale = $voto_orale_temp;

            if ($tot_peso_orale != 'N') {
                $media_orale_ponderata = $voto_orale_pond_temp;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento pratico">
        if ($arrotondamento != "XX" && $cont_voti_pratici > 0) {
            $media_pratico = round(($tot_pratico / $cont_voti_pratici), 2);
            $voto_pratico_temp = intval($tot_pratico / $cont_voti_pratici);
            $resto_voto_pratico_temp = (($tot_pratico / $cont_voti_pratici) - $voto_pratico_temp) * 100;

            $voto_pratico_pond_temp = intval($tot_pratico_ponderata/$tot_peso_pratico);
            $resto_voto_pratico_pond_temp = ((($tot_pratico_ponderata/$tot_peso_pratico) - $voto_pratico_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_pratico_temp >= 0 && $resto_voto_pratico_temp < 12.5) {
                $voto_pratico_temp = $voto_pratico_temp;
            }
            if ($resto_voto_pratico_temp >= 12.5 && $resto_voto_pratico_temp < 37.5) {
                $voto_pratico_temp = $voto_pratico_temp + 0.25;
            }
            if ($resto_voto_pratico_temp >= 37.5 && $resto_voto_pratico_temp < 62.5) {
                $voto_pratico_temp = $voto_pratico_temp + 0.5;
                $voto_pratico_temp = $voto_pratico_temp . "0";
            }
            if ($resto_voto_pratico_temp >= 62.5 && $resto_voto_pratico_temp < 87.5) {
                $voto_pratico_temp = $voto_pratico_temp + 0.75;
            }
            if ($resto_voto_pratico_temp >= 87.5 && $resto_voto_pratico_temp < 100) {
                $voto_pratico_temp = $voto_pratico_temp + 1;
            }

            if ($resto_voto_pratico_pond_temp >= 0 && $resto_voto_pratico_pond_temp < 12.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp;
            }
            if ($resto_voto_pratico_pond_temp >= 12.5 && $resto_voto_pratico_pond_temp < 37.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.25;
            }
            if ($resto_voto_pratico_pond_temp >= 37.5 && $resto_voto_pratico_pond_temp < 62.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.5;
                $voto_pratico_pond_temp = $voto_pratico_pond_temp . "0";
            }
            if ($resto_voto_pratico_pond_temp >= 62.5 && $resto_voto_pratico_pond_temp < 87.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.75;
            }
            if ($resto_voto_pratico_pond_temp >= 87.5 && $resto_voto_pratico_pond_temp < 100) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if (($resto_voto_pratico_temp >= 0) && ($resto_voto_pratico_temp < 25)) {
                $voto_pratico_temp = $voto_pratico_temp;
            }
            if (($resto_voto_pratico_temp >= 25) && ($resto_voto_pratico_temp < 75)) {
                $voto_pratico_temp = $voto_pratico_temp + 0.5;
                $voto_pratico_temp = $voto_pratico_temp . "0";
            }
            if (($resto_voto_pratico_temp >= 75) && ($resto_voto_pratico_temp < 100)) {
                $voto_pratico_temp = $voto_pratico_temp + 1;
            }


            if ($resto_voto_pratico_pond_temp >= 0 && $resto_voto_pratico_pond_temp < 25) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp;
            }
            if ($resto_voto_pratico_pond_temp >= 25 && $resto_voto_pratico_pond_temp < 75) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.5;
                $voto_pratico_pond_temp = $voto_pratico_pond_temp . "0";
            }
            if ($resto_voto_pratico_pond_temp >= 75 && $resto_voto_pratico_pond_temp < 100) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_pratico_temp >= 0 && $resto_voto_pratico_temp < 50) {
                $voto_pratico_temp = $voto_pratico_temp;
            }
            if ($resto_voto_pratico_temp >= 50 && $resto_voto_pratico_temp < 100) {
                $voto_pratico_temp = $voto_pratico_temp + 1;
            }

            if ($resto_voto_pratico_pond_temp >= 0 && $resto_voto_pratico_pond_temp < 50) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp;
            }
            if ($resto_voto_pratico_pond_temp >= 50 && $resto_voto_pratico_pond_temp < 100) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 1;
            }
        }

        if ($arrotondamento != "XX" && $cont_voti_pratici > 0) {
            $media_pratico = $voto_pratico_temp;

            if ($tot_peso_pratico != 'N') {
                $media_pratico_ponderata = $voto_pratico_pond_temp;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento media totale">
        if ($arrotondamento != "XX" && $cont_tot_voti > 0) {
            $media_totale = round(($tot_voti / $cont_tot_voti), 2);
            $voto_totale_temp = intval($tot_voti / $cont_tot_voti);
            $resto_voto_totale_temp = (($tot_voti / $cont_tot_voti) - $voto_totale_temp) * 100;

            $voto_totale_pond_temp = intval($tot_totale_ponderata/$tot_peso_totale);
            $resto_voto_totale_pond_temp = ((($tot_totale_ponderata/$tot_peso_totale) - $voto_totale_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_totale_temp >= 0 && $resto_voto_totale_temp < 12.5) {
                $voto_totale_temp = $voto_totale_temp;
            }

            if (($resto_voto_totale_temp >= 12.5) && ($resto_voto_totale_temp < 37.5)) {
                $voto_totale_temp = $voto_totale_temp + 0.25;
            }

            if (($resto_voto_totale_temp >= 37.5) && ($resto_voto_totale_temp < 62.5)) {
                $voto_totale_temp = $voto_totale_temp + 0.5;
                $voto_totale_temp = $voto_totale_temp . "0";
            }

            if (($resto_voto_totale_temp >= 62.5) && ($resto_voto_totale_temp < 87.5)) {
                $voto_totale_temp = $voto_totale_temp + 0.75;
            }

            if (($resto_voto_totale_temp >= 87.5) && ($resto_voto_totale_temp < 100)) {
                $voto_totale_temp = $voto_totale_temp + 1;
            }


            if ($resto_voto_totale_pond_temp >= 0 && $resto_voto_totale_pond_temp < 12.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp;
            }
            if ($resto_voto_totale_pond_temp >= 12.5 && $resto_voto_totale_pond_temp < 37.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.25;
            }
            if ($resto_voto_totale_pond_temp >= 37.5 && $resto_voto_totale_pond_temp < 62.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.5;
                $voto_totale_pond_temp = $voto_totale_pond_temp . "0";
            }
            if ($resto_voto_totale_pond_temp >= 62.5 && $resto_voto_totale_pond_temp < 87.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.75;
            }
            if ($resto_voto_totale_pond_temp >= 87.5 && $resto_voto_totale_pond_temp < 100) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if (($resto_voto_totale_temp >= 0) && ($resto_voto_totale_temp < 25)) {
                $voto_totale_temp = $voto_totale_temp;
            }

            if (($resto_voto_totale_temp >= 25) && ($resto_voto_totale_temp < 75)) {
                $voto_totale_temp = $voto_totale_temp + 0.5;
                $voto_totale_temp = $voto_totale_temp . "0";
            }

            if (($resto_voto_totale_temp >= 75) && ($resto_voto_totale_temp < 100)) {
                $voto_totale_temp = $voto_totale_temp + 1;
            }


            if ($resto_voto_totale_pond_temp >= 0 && $resto_voto_totale_pond_temp < 25) {
                $voto_totale_pond_temp = $voto_totale_pond_temp;
            }
            if ($resto_voto_totale_pond_temp >= 25 && $resto_voto_totale_pond_temp < 75) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.5;
                $voto_totale_pond_temp = $voto_totale_pond_temp . "0";
            }
            if ($resto_voto_totale_pond_temp >= 75 && $resto_voto_totale_pond_temp < 100) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_totale_temp >= 0 && $resto_voto_totale_temp < 50) {
                $voto_totale_temp = $voto_totale_temp;
            }
            if ($resto_voto_totale_temp >= 50 && $resto_voto_totale_temp < 100) {
                $voto_totale_temp = $voto_totale_temp + 1;
            }

            if ($resto_voto_totale_pond_temp >= 0 && $resto_voto_totale_pond_temp < 50) {
                $voto_totale_pond_temp = $voto_totale_pond_temp;
            }
            if ($resto_voto_totale_pond_temp >= 50 && $resto_voto_totale_pond_temp < 100) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 1;
            }
        }

        if (($arrotondamento != "XX") && ($cont_tot_voti > 0)) {
            $media_totale = $voto_totale_temp;

            if ($tot_peso_totale != 'N') {
                $media_totale_ponderata = $voto_totale_pond_temp;
            }
        }
        //}}} </editor-fold>

        foreach ($significati_voto as $singolo_sv) {
            if ($media_scritto == $singolo_sv["voto"] && $media_scritto != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_scritto = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_scritto = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_scritto = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_scritto = $singolo_sv["valore_pagella"];
                        break;
                }
            }

            if ($media_orale == $singolo_sv["voto"] && $media_orale != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_orale = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_orale = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_orale = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_orale = $singolo_sv["valore_pagella"];
                        break;
                }
            }

            if ($media_pratico == $singolo_sv["voto"] && $media_pratico != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_pratico = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_pratico = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_pratico = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_pratico = $singolo_sv["valore_pagella"];
                        break;
                }
            }

            if ($media_totale == $singolo_sv["voto"] && $media_totale != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_totale = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_totale = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_totale = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_totale = $singolo_sv["valore_pagella"];
                        break;
                }
            }


            if ($media_scritto_ponderata == $singolo_sv["voto"] && $media_scritto_ponderata != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_scritto_ponderata = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_scritto_ponderata = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_scritto_ponderata = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_scritto_ponderata = $singolo_sv["valore_pagella"];
                        break;
                }
            }

            if ($media_orale_ponderata == $singolo_sv["voto"] && $media_orale_ponderata != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_orale_ponderata = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_orale_ponderata = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_orale_ponderata = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_orale_ponderata = $singolo_sv["valore_pagella"];
                        break;
                }
            }

            if ($media_pratico_ponderata == $singolo_sv["voto"] && $media_pratico_ponderata != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_pratico_ponderata = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_pratico_ponderata = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_pratico_ponderata = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_pratico_ponderata = $singolo_sv["valore_pagella"];
                        break;
                }
            }

            if ($media_totale_ponderata == $singolo_sv["voto"] && $media_totale_ponderata != 0) {
                switch ($tipo_voto_stampa) {
                    case "scheda":
                        $media_totale_ponderata = $singolo_sv["voto"];
                        break;
                    case "codice":
                        $media_totale_ponderata = $singolo_sv["codice"];
                        break;
                    case "valore_descrizione":
                        $media_totale_ponderata = $singolo_sv["valore"];
                        break;
                    case "valore_pagella":
                        $media_totale_ponderata = $singolo_sv["valore_pagella"];
                        break;
                }
            }


        }


        $media_scritto_ret = 0;
        $media_orale_ret = 0;
        $media_pratico_ret = 0;
        $media_totale_ret = 0;

        if ($tipo_media == 'ARITMETICA') {
            $media_scritto_ret= $media_scritto;
            $media_orale_ret = $media_orale;
            $media_pratico_ret = $media_pratico;
            $media_totale_ret = $media_totale;
        }
        elseif ($tipo_media == 'PONDERATA') {
            $media_scritto_ret= $media_scritto_ponderata;
            $media_orale_ret = $media_orale_ponderata;
            $media_pratico_ret = $media_pratico_ponderata;
            $media_totale_ret = $media_totale_ponderata;
        }
        else {
            $media_scritto_ret= $media_scritto;
            $media_orale_ret = $media_orale;
            $media_pratico_ret = $media_pratico;
            $media_totale_ret = $media_totale;
        }

        $risultato = [
            'scritto' => $media_scritto_ret,
            'orale'   => $media_orale_ret,
            'pratico' => $media_pratico_ret,
            'totale'  => $media_totale_ret,
            'cont_scritto'  => $cont_voti_scritti,
            'cont_orale'    => $cont_voti_orali,
            'cont_pratico'  => $cont_voti_pratici,
            'cont_totale'   => $cont_tot_voti
        ];
    } else {
        $risultato = [
            'scritto' => '--',
            'orale'   => '--',
            'pratico' => '--',
            'totale'  => '--',
            'cont_scritto'  => 0,
            'cont_orale'    => 0,
            'cont_pratico'  => 0,
            'cont_totale'   => 0
        ];
    }

    return $risultato;
    //}}} </editor-fold>
}

function calcola_media_voti_studente_classe($id_studente, $id_classe, $multi_classe, $start_interval, $end_interval, $arrotondamento, $arrotondamento_cifre, $corsi_abbinati = "NO", $tipo_voto_stampa = '', $tipo_media = 'ARITMETICA') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una materia di uno studente per il periodo interessato">

    $materie = [];

	if($multi_classe == "SI")
	{
        $classi = estrai_multi_classi_da_classe($id_classe);
        foreach ($classi as $classe)
        {
            $materie_tmp = [];
            $materie_tmp = estrai_materie_classe($classe['id_classe'], $corsi_abbinati);
            foreach ($materie_tmp as $materia)
            {
                $materie[$materia['id_materia']] = $materia['id_materia'];
            }
        }
	}
	else
	{
		$materie_tmp = estrai_materie_classe($id_classe, $corsi_abbinati);
        foreach ($materie_tmp as $materia)
        {
            $materie[$materia['id_materia']] = $materia['id_materia'];
        }
	}

    $stringa_materie = implode("', '", $materie);

    if($corsi_abbinati == "SI")
    {
         $query = "SELECT
                        voti.tipo,
                        voti.voto,
                        pesi.peso
                    FROM
                        voti LEFT JOIN pesi on voti.id_peso = pesi.id_peso,
                        materie,
                        studenti
                    WHERE
                        voti.data >= {$start_interval}
                        AND voti.data <= {$end_interval}
                        AND voti.id_studente = {$id_studente}
                        AND
                            (
                                voti.id_materia IN ('{$stringa_materie}')
                                OR voti.id_materia in (select id_materia from materie where materie.id_materia_riferimento IN ('{$stringa_materie}'))
                                OR voti.id_materia in
                                (
                                    SELECT materia_riferimento_studenti_corsi.id_corso
                                    FROM
                                        materia_riferimento_studenti_corsi
                                    WHERE
                                        materia_riferimento_studenti_corsi.id_studente = {$id_studente}
                                        AND materia_riferimento_studenti_corsi.id_materia_riferimento IN ('{$stringa_materie}')
                                )
                            )
                        AND voti.flag_canc = 0
                        AND studenti.id_studente = {$id_studente}
                        AND studenti.flag_canc = 0
                        AND materie.id_materia IN ('{$stringa_materie}')
                        AND materie.flag_canc = 0
                    ORDER BY
                        voti.tipo,
                        voti.data";
    }
    else
    {
        $query = "SELECT voti.tipo, voti.voto, pesi.peso
                    FROM voti LEFT JOIN pesi on voti.id_peso = pesi.id_peso, materie, studenti
                    WHERE voti.data >= {$start_interval}
                        AND voti.data <= {$end_interval}
                        AND voti.id_studente = {$id_studente}
                        AND voti.id_materia IN ('{$stringa_materie}')
                        AND voti.flag_canc = 0
                        AND studenti.id_studente = {$id_studente}
                        AND studenti.flag_canc = 0
                        AND materie.id_materia IN ('{$stringa_materie}')
                        AND materie.flag_canc = 0
                    ORDER BY voti.tipo, voti.data";
    }
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $tot_voti = 0;
    $tot_scritto = 0;
    $tot_orale = 0;
    $tot_pratico = 0;
    $cont_voti_scritti = 0;
    $cont_voti_orali = 0;
    $cont_voti_pratici = 0;
    $cont_tot_voti = 0;
    $media_scritto = 0;
    $media_pratico = 0;
    $media_orale = 0;
    $media_totale = 0;
    $media_scritto_ponderata = 0;
    $media_orale_ponderata = 0;
    $media_pratico_ponderata = 0;
    $media_totale_ponderata = 0;

    $tot_scritto_ponderata = $tot_orale_ponderata = $tot_pratico_ponderata = $tot_totale_ponderata = 0;
    $tot_peso_scritto = $tot_peso_orale = $tot_peso_pratico = $tot_peso_totale = 0;

    if ($numero > 0)
    {
        for ($cont = 0; $cont < $numero; $cont++) {
            $array_voti[$cont] = pg_fetch_assoc($result, $cont);

            if (is_numeric($array_voti[$cont]["voto"])) {
                $tot_voti += $array_voti[$cont]["voto"];
                $cont_tot_voti++;

                if ( ($tot_peso_totale != 'N' or $tot_peso_totale == '0') and $array_voti[$cont]['peso'] != '')
                {
                    $tot_totale_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                    $tot_peso_totale += $array_voti[$cont]['peso'];
                }
                else {
                    $tot_peso_totale = 'N';
                }

                switch ($array_voti[$cont]["tipo"]) {
                    case "0":
                        $cont_voti_scritti++;
                        $tot_scritto += $array_voti[$cont]["voto"];

                        if ( ($tot_peso_scritto != 'N' or $tot_peso_scritto == '0') and $array_voti[$cont]['peso'] != '')
                        {
                            $tot_scritto_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                            $tot_peso_scritto += $array_voti[$cont]['peso'];
                        }
                        else {
                            $tot_peso_scritto = 'N';
                        }

                        break;
                    case "1":
                        $cont_voti_orali++;
                        $tot_orale += $array_voti[$cont]["voto"];

                        if ( ($tot_peso_orale != 'N' or $tot_peso_orale == '0') and $array_voti[$cont]['peso'] != '')
                        {
                            $tot_orale_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                            $tot_peso_orale += $array_voti[$cont]['peso'];
                        }
                        else {
                            $tot_peso_orale = 'N';
                        }

                        break;
                    case "2":
                        $cont_voti_pratici++;
                        $tot_pratico += $array_voti[$cont]["voto"];

                        if ( ($tot_peso_pratico != 'N' or $tot_peso_pratico == '0') and $array_voti[$cont]['peso'] != '')
                        {
                            $tot_pratico_ponderata += $array_voti[$cont]['voto']*$array_voti[$cont]['peso'];
                            $tot_peso_pratico += $array_voti[$cont]['peso'];
                        }
                        else {
                            $tot_peso_pratico = 'N';
                        }

                        break;
                }
            }
        }

        if ($cont_voti_scritti > 0) {
            $media_scritto = round(($tot_scritto / $cont_voti_scritti), $arrotondamento_cifre);
        }

        if ($cont_voti_orali > 0) {
            $media_orale = round(($tot_orale / $cont_voti_orali), $arrotondamento_cifre);
        }

        if ($cont_voti_pratici > 0) {
            $media_pratico = round(($tot_pratico / $cont_voti_pratici), $arrotondamento_cifre);
        }

        if ($cont_tot_voti > 0) {
            $media_totale = round(($tot_voti / $cont_tot_voti), $arrotondamento_cifre);
        }


        if ($tot_peso_scritto != 'N') {
            $media_scritto_ponderata = (round($tot_scritto_ponderata/$tot_peso_scritto,$arrotondamento_cifre) > 0 ? (string)round($tot_scritto_ponderata/$tot_peso_scritto,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_voti_scritti == '0') {
                $media_scritto_ponderata = '--';
            }
            else {
                $media_scritto_ponderata = 'NC';
            }
        }

        if ($tot_peso_orale != 'N') {
            $media_orale_ponderata = (round($tot_orale_ponderata/$tot_peso_orale,$arrotondamento_cifre) > 0 ? (string)round($tot_orale_ponderata/$tot_peso_orale,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_voti_orali == '0') {
                $media_orale_ponderata = '--';
            }
            else {
                $media_orale_ponderata = 'NC';
            }
        }

        if ($tot_peso_pratico != 'N') {
            $media_pratico_ponderata = (round($tot_pratico_ponderata/$tot_peso_pratico,$arrotondamento_cifre) > 0 ? (string)round($tot_pratico_ponderata/$tot_peso_pratico,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_voti_pratici == '0') {
                $media_pratico_ponderata = '--';
            }
            else {
                $media_pratico_ponderata = 'NC';
            }
        }

        if ($tot_peso_totale != 'N') {
            $media_totale_ponderata = (round($tot_totale_ponderata/$tot_peso_totale,$arrotondamento_cifre) > 0 ? (string)round($tot_totale_ponderata/$tot_peso_totale,$arrotondamento_cifre) : '');
        }
        else { // quando tot peso è 0 o N
            if ($cont_tot_voti == '0') {
                $media_totale_ponderata = '--';
            }
            else {
                $media_totale_ponderata = 'NC';
            }
        }

        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento scritto">
        if ($arrotondamento != "XX" && $cont_voti_scritti > 0) {
            $media_scritto = round(($tot_scritto / $cont_voti_scritti), 2);
            $voto_scritto_temp = intval($tot_scritto / $cont_voti_scritti);
            $resto_voto_scritto_temp = ((($tot_scritto / $cont_voti_scritti) - $voto_scritto_temp) * 100);

            $voto_scritto_pond_temp = intval($tot_scritto_ponderata/$tot_peso_scritto);
            $resto_voto_scritto_pond_temp = ((($tot_scritto_ponderata/$tot_peso_scritto) - $voto_scritto_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_scritto_temp >= 0 && $resto_voto_scritto_temp < 12.5) {
                $voto_scritto_temp = $voto_scritto_temp;
            }
            if ($resto_voto_scritto_temp >= 12.5 && $resto_voto_scritto_temp < 37.5) {
                $voto_scritto_temp = $voto_scritto_temp + 0.25;
            }
            if ($resto_voto_scritto_temp >= 37.5 && $resto_voto_scritto_temp < 62.5) {
                $voto_scritto_temp = $voto_scritto_temp + 0.5;
                $voto_scritto_temp = $voto_scritto_temp . "0";
            }
            if ($resto_voto_scritto_temp >= 62.5 && $resto_voto_scritto_temp < 87.5) {
                $voto_scritto_temp = $voto_scritto_temp + 0.75;
            }
            if ($resto_voto_scritto_temp >= 87.5 && $resto_voto_scritto_temp < 100) {
                $voto_scritto_temp = $voto_scritto_temp + 1;
            }


            if ($resto_voto_scritto_pond_temp >= 0 && $resto_voto_scritto_pond_temp < 12.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp;
            }
            if ($resto_voto_scritto_pond_temp >= 12.5 && $resto_voto_scritto_pond_temp < 37.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.25;
            }
            if ($resto_voto_scritto_pond_temp >= 37.5 && $resto_voto_scritto_pond_temp < 62.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.5;
                $voto_scritto_pond_temp = $voto_scritto_pond_temp . "0";
            }
            if ($resto_voto_scritto_pond_temp >= 62.5 && $resto_voto_scritto_pond_temp < 87.5) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.75;
            }
            if ($resto_voto_scritto_pond_temp >= 87.5 && $resto_voto_scritto_pond_temp < 100) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if ($resto_voto_scritto_temp >= 0 && $resto_voto_scritto_temp < 25) {
                $voto_scritto_temp = $voto_scritto_temp;
            }
            if ($resto_voto_scritto_temp >= 25 && $resto_voto_scritto_temp < 75) {
                $voto_scritto_temp = $voto_scritto_temp + 0.5;
                $voto_scritto_temp = $voto_scritto_temp . "0";
            }
            if ($resto_voto_scritto_temp >= 75 && $resto_voto_scritto_temp < 100) {
                $voto_scritto_temp = $voto_scritto_temp + 1;
            }

            if ($resto_voto_scritto_pond_temp >= 0 && $resto_voto_scritto_pond_temp < 25) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp;
            }
            if ($resto_voto_scritto_pond_temp >= 25 && $resto_voto_scritto_pond_temp < 75) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 0.5;
                $voto_scritto_pond_temp = $voto_scritto_pond_temp . "0";
            }
            if ($resto_voto_scritto_pond_temp >= 75 && $resto_voto_scritto_pond_temp < 100) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_scritto_temp >= 0 && $resto_voto_scritto_temp < 50) {
                $voto_scritto_temp = $voto_scritto_temp;
            }
            if ($resto_voto_scritto_temp >= 50 && $resto_voto_scritto_temp < 100) {
                $voto_scritto_temp = $voto_scritto_temp + 1;
            }

            if ($resto_voto_scritto_pond_temp >= 0 && $resto_voto_scritto_pond_temp < 50) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp;
            }
            if ($resto_voto_scritto_pond_temp >= 50 && $resto_voto_scritto_pond_temp < 100) {
                $voto_scritto_pond_temp = $voto_scritto_pond_temp + 1;
            }
        }

        if ($arrotondamento != "XX" && $cont_voti_scritti > 0) {
            $media_scritto = $voto_scritto_temp;

            if ($tot_peso_scritto != 'N') {
                $media_scritto_ponderata = $voto_scritto_pond_temp;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento orale">
        if ($arrotondamento != "XX" && $cont_voti_orali > 0) {
            $media_orale = round(($tot_orale / $cont_voti_orali), 2);
            $voto_orale_temp = intval($tot_orale / $cont_voti_orali);
            $resto_voto_orale_temp = (($tot_orale / $cont_voti_orali) - $voto_orale_temp) * 100;

            $voto_orale_pond_temp = intval($tot_orale_ponderata/$tot_peso_orale);
            $resto_voto_orale_pond_temp = ((($tot_orale_ponderata/$tot_peso_orale) - $voto_orale_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_orale_temp >= 0 && $resto_voto_orale_temp < 12.5) {
                $voto_orale_temp = $voto_orale_temp;
            }
            if ($resto_voto_orale_temp >= 12.5 && $resto_voto_orale_temp < 37.5) {
                $voto_orale_temp = $voto_orale_temp + 0.25;
            }
            if ($resto_voto_orale_temp >= 37.5 && $resto_voto_orale_temp < 62.5) {
                $voto_orale_temp = $voto_orale_temp + 0.5;
                $voto_orale_temp = $voto_orale_temp . "0";
            }
            if ($resto_voto_orale_temp >= 62.5 && $resto_voto_orale_temp < 87.5) {
                $voto_orale_temp = $voto_orale_temp + 0.75;
            }
            if ($resto_voto_orale_temp >= 87.5 && $resto_voto_orale_temp < 100) {
                $voto_orale_temp = $voto_orale_temp + 1;
            }


            if ($resto_voto_orale_pond_temp >= 0 && $resto_voto_orale_pond_temp < 12.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp;
            }
            if ($resto_voto_orale_pond_temp >= 12.5 && $resto_voto_orale_pond_temp < 37.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.25;
            }
            if ($resto_voto_orale_pond_temp >= 37.5 && $resto_voto_orale_pond_temp < 62.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.5;
                $voto_orale_pond_temp = $voto_orale_pond_temp . "0";
            }
            if ($resto_voto_orale_pond_temp >= 62.5 && $resto_voto_orale_pond_temp < 87.5) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.75;
            }
            if ($resto_voto_orale_pond_temp >= 87.5 && $resto_voto_orale_pond_temp < 100) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if ($resto_voto_orale_temp >= 0 && $resto_voto_orale_temp < 25) {
                $voto_orale_temp = $voto_orale_temp;
            }

            if ($resto_voto_orale_temp >= 25 && $resto_voto_orale_temp < 75) {
                $voto_orale_temp = $voto_orale_temp + 0.5;
                $voto_orale_temp = $voto_orale_temp . "0";
            }

            if ($resto_voto_orale_temp >= 75 && $resto_voto_orale_temp < 100) {
                $voto_orale_temp = $voto_orale_temp + 1;
            }


            if ($resto_voto_orale_pond_temp >= 0 && $resto_voto_orale_pond_temp < 25) {
                $voto_orale_pond_temp = $voto_orale_pond_temp;
            }
            if ($resto_voto_orale_pond_temp >= 25 && $resto_voto_orale_pond_temp < 75) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 0.5;
                $voto_orale_pond_temp = $voto_orale_pond_temp . "0";
            }
            if ($resto_voto_orale_pond_temp >= 75 && $resto_voto_orale_pond_temp < 100) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_orale_temp >= 0 && $resto_voto_orale_temp < 50) {
                $voto_orale_temp = $voto_orale_temp;
            }
            if ($resto_voto_orale_temp >= 50 && $resto_voto_orale_temp < 100) {
                $voto_orale_temp = $voto_orale_temp + 1;
            }

            if ($resto_voto_orale_pond_temp >= 0 && $resto_voto_orale_pond_temp < 50) {
                $voto_orale_pond_temp = $voto_orale_pond_temp;
            }
            if ($resto_voto_orale_pond_temp >= 50 && $resto_voto_orale_pond_temp < 100) {
                $voto_orale_pond_temp = $voto_orale_pond_temp + 1;
            }
        }

        if ($arrotondamento != "XX" && $cont_voti_orali > 0) {
            $media_orale = $voto_orale_temp;

            if ($tot_peso_orale != 'N') {
                $media_orale_ponderata = $voto_orale_pond_temp;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento pratico">
        if ($arrotondamento != "XX" && $cont_voti_pratici > 0) {
            $media_pratico = round(($tot_pratico / $cont_voti_pratici), 2);
            $voto_pratico_temp = intval($tot_pratico / $cont_voti_pratici);
            $resto_voto_pratico_temp = (($tot_pratico / $cont_voti_pratici) - $voto_pratico_temp) * 100;

            $voto_pratico_pond_temp = intval($tot_pratico_ponderata/$tot_peso_pratico);
            $resto_voto_pratico_pond_temp = ((($tot_pratico_ponderata/$tot_peso_pratico) - $voto_pratico_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_pratico_temp >= 0 && $resto_voto_pratico_temp < 12.5) {
                $voto_pratico_temp = $voto_pratico_temp;
            }
            if ($resto_voto_pratico_temp >= 12.5 && $resto_voto_pratico_temp < 37.5) {
                $voto_pratico_temp = $voto_pratico_temp + 0.25;
            }
            if ($resto_voto_pratico_temp >= 37.5 && $resto_voto_pratico_temp < 62.5) {
                $voto_pratico_temp = $voto_pratico_temp + 0.5;
                $voto_pratico_temp = $voto_pratico_temp . "0";
            }
            if ($resto_voto_pratico_temp >= 62.5 && $resto_voto_pratico_temp < 87.5) {
                $voto_pratico_temp = $voto_pratico_temp + 0.75;
            }
            if ($resto_voto_pratico_temp >= 87.5 && $resto_voto_pratico_temp < 100) {
                $voto_pratico_temp = $voto_pratico_temp + 1;
            }

            if ($resto_voto_pratico_pond_temp >= 0 && $resto_voto_pratico_pond_temp < 12.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp;
            }
            if ($resto_voto_pratico_pond_temp >= 12.5 && $resto_voto_pratico_pond_temp < 37.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.25;
            }
            if ($resto_voto_pratico_pond_temp >= 37.5 && $resto_voto_pratico_pond_temp < 62.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.5;
                $voto_pratico_pond_temp = $voto_pratico_pond_temp . "0";
            }
            if ($resto_voto_pratico_pond_temp >= 62.5 && $resto_voto_pratico_pond_temp < 87.5) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.75;
            }
            if ($resto_voto_pratico_pond_temp >= 87.5 && $resto_voto_pratico_pond_temp < 100) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if (($resto_voto_pratico_temp >= 0) && ($resto_voto_pratico_temp < 25)) {
                $voto_pratico_temp = $voto_pratico_temp;
            }
            if (($resto_voto_pratico_temp >= 25) && ($resto_voto_pratico_temp < 75)) {
                $voto_pratico_temp = $voto_pratico_temp + 0.5;
                $voto_pratico_temp = $voto_pratico_temp . "0";
            }
            if (($resto_voto_pratico_temp >= 75) && ($resto_voto_pratico_temp < 100)) {
                $voto_pratico_temp = $voto_pratico_temp + 1;
            }

            if ($resto_voto_pratico_pond_temp >= 0 && $resto_voto_pratico_pond_temp < 25) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp;
            }
            if ($resto_voto_pratico_pond_temp >= 25 && $resto_voto_pratico_pond_temp < 75) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 0.5;
                $voto_pratico_pond_temp = $voto_pratico_pond_temp . "0";
            }
            if ($resto_voto_pratico_pond_temp >= 75 && $resto_voto_pratico_pond_temp < 100) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_pratico_temp >= 0 && $resto_voto_pratico_temp < 50) {
                $voto_pratico_temp = $voto_pratico_temp;
            }
            if ($resto_voto_pratico_temp >= 50 && $resto_voto_pratico_temp < 100) {
                $voto_pratico_temp = $voto_pratico_temp + 1;
            }

            if ($resto_voto_pratico_pond_temp >= 0 && $resto_voto_pratico_pond_temp < 50) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp;
            }
            if ($resto_voto_pratico_pond_temp >= 50 && $resto_voto_pratico_pond_temp < 100) {
                $voto_pratico_pond_temp = $voto_pratico_pond_temp + 1;
            }
        }

        if ($arrotondamento != "XX" && $cont_voti_pratici > 0) {
            $media_pratico = $voto_pratico_temp;

            if ($tot_peso_pratico != 'N') {
                $media_pratico_ponderata = $voto_pratico_pond_temp;
            }
        }
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="arrotondamento media totale">
        if ($arrotondamento != "XX" && $cont_tot_voti > 0) {
            $media_totale = round(($tot_voti / $cont_tot_voti), 2);
            $voto_totale_temp = intval($tot_voti / $cont_tot_voti);
            $resto_voto_totale_temp = (($tot_voti / $cont_tot_voti) - $voto_totale_temp) * 100;

            $voto_totale_pond_temp = intval($tot_totale_ponderata/$tot_peso_totale);
            $resto_voto_totale_pond_temp = ((($tot_totale_ponderata/$tot_peso_totale) - $voto_totale_pond_temp) * 100);
        }

        if ($arrotondamento == "25") {
            if ($resto_voto_totale_temp >= 0 && $resto_voto_totale_temp < 12.5) {
                $voto_totale_temp = $voto_totale_temp;
            }

            if (($resto_voto_totale_temp >= 12.5) && ($resto_voto_totale_temp < 37.5)) {
                $voto_totale_temp = $voto_totale_temp + 0.25;
            }

            if (($resto_voto_totale_temp >= 37.5) && ($resto_voto_totale_temp < 62.5)) {
                $voto_totale_temp = $voto_totale_temp + 0.5;
                $voto_totale_temp = $voto_totale_temp . "0";
            }

            if (($resto_voto_totale_temp >= 62.5) && ($resto_voto_totale_temp < 87.5)) {
                $voto_totale_temp = $voto_totale_temp + 0.75;
            }

            if (($resto_voto_totale_temp >= 87.5) && ($resto_voto_totale_temp < 100)) {
                $voto_totale_temp = $voto_totale_temp + 1;
            }

            if ($resto_voto_totale_pond_temp >= 0 && $resto_voto_totale_pond_temp < 12.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp;
            }
            if ($resto_voto_totale_pond_temp >= 12.5 && $resto_voto_totale_pond_temp < 37.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.25;
            }
            if ($resto_voto_totale_pond_temp >= 37.5 && $resto_voto_totale_pond_temp < 62.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.5;
                $voto_totale_pond_temp = $voto_totale_pond_temp . "0";
            }
            if ($resto_voto_totale_pond_temp >= 62.5 && $resto_voto_totale_pond_temp < 87.5) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.75;
            }
            if ($resto_voto_totale_pond_temp >= 87.5 && $resto_voto_totale_pond_temp < 100) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "50") {
            if (($resto_voto_totale_temp >= 0) && ($resto_voto_totale_temp < 25)) {
                $voto_totale_temp = $voto_totale_temp;
            }
            if (($resto_voto_totale_temp >= 25) && ($resto_voto_totale_temp < 75)) {
                $voto_totale_temp = $voto_totale_temp + 0.5;
                $voto_totale_temp = $voto_totale_temp . "0";
            }
            if (($resto_voto_totale_temp >= 75) && ($resto_voto_totale_temp < 100)) {
                $voto_totale_temp = $voto_totale_temp + 1;
            }


            if ($resto_voto_totale_pond_temp >= 0 && $resto_voto_totale_pond_temp < 25) {
                $voto_totale_pond_temp = $voto_totale_pond_temp;
            }
            if ($resto_voto_totale_pond_temp >= 25 && $resto_voto_totale_pond_temp < 75) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 0.5;
                $voto_totale_pond_temp = $voto_totale_pond_temp . "0";
            }
            if ($resto_voto_totale_pond_temp >= 75 && $resto_voto_totale_pond_temp < 100) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 1;
            }
        }

        if ($arrotondamento == "100") {
            if ($resto_voto_totale_temp >= 0 && $resto_voto_totale_temp < 50) {
                $voto_totale_temp = $voto_totale_temp;
            }
            if ($resto_voto_totale_temp >= 50 && $resto_voto_totale_temp < 100) {
                $voto_totale_temp = $voto_totale_temp + 1;
            }

            if ($resto_voto_totale_pond_temp >= 0 && $resto_voto_totale_pond_temp < 50) {
                $voto_totale_pond_temp = $voto_totale_pond_temp;
            }
            if ($resto_voto_totale_pond_temp >= 50 && $resto_voto_totale_pond_temp < 100) {
                $voto_totale_pond_temp = $voto_totale_pond_temp + 1;
            }
        }

        if (($arrotondamento != "XX") && ($cont_tot_voti > 0)) {
            $media_totale = $voto_totale_temp;

            if ($tot_peso_totale != 'N') {
                $media_totale_ponderata = $voto_totale_pond_temp;
            }
        }
        //}}} </editor-fold>

        $media_scritto_ret = 0;
        $media_orale_ret = 0;
        $media_pratico_ret = 0;
        $media_totale_ret = 0;

        if ($tipo_media == 'ARITMETICA') {
            $media_scritto_ret= $media_scritto;
            $media_orale_ret = $media_orale;
            $media_pratico_ret = $media_pratico;
            $media_totale_ret = $media_totale;
        }
        elseif ($tipo_media == 'PONDERATA') {
            $media_scritto_ret= $media_scritto_ponderata;
            $media_orale_ret = $media_orale_ponderata;
            $media_pratico_ret = $media_pratico_ponderata;
            $media_totale_ret = $media_totale_ponderata;
        }
        else {
            $media_scritto_ret= $media_scritto;
            $media_orale_ret = $media_orale;
            $media_pratico_ret = $media_pratico;
            $media_totale_ret = $media_totale;
        }

        $risultato = [
            'scritto' => $media_scritto_ret,
            'orale'   => $media_orale_ret,
            'pratico' => $media_pratico_ret,
            'totale'  => $media_totale_ret
        ];
    } else {
        $risultato = "Nessuna media";
    }

    return $risultato;
    //}}} </editor-fold>
}

function media_da_statistiche($id_studente, $id_materia, $id_classe) {
    /*{{{ */
	$media = "SELECT data,
                    date_part('epoch', data)::int AS unixtime,
                    indirizzo,
                    classe,
                    (SELECT (classe::text || 'ª '::text) || sezione::text
                     FROM public.classi
                     WHERE id_classe = t.classe ) AS nome_classe,
                    materia,
                    (SELECT nome_materia_breve
                    FROM public.materie
                    WHERE id_materia = t.materia ) AS nome_materia,
                    media
                FROM statistiche.ag_indirizzi_classi_materie_studenti t
                WHERE materia = {$id_materia}
                    AND classe = {$id_classe}
                    AND studente = {$id_studente}
                ORDER BY data ASC";

    $result = pgsql_query($media) or die("Invalid $media");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $array_media = pg_fetch_all($result);
    }

    return $array_media;
	/*}}}*/
}

function estrai_riepilogo_voti_studente_singola_materia($id_studente, $id_materia, $sort_order = 'DESC') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una materia di uno studente">
    $order = $sort_order == 'DESC' ? 'DESC' : 'ASC';

    $query = "SELECT id_voto, data, voti.note, tipo, voto, nome, cognome
                FROM voti
                INNER JOIN utenti ON id_utente = id_professore
                WHERE id_studente = {$id_studente}
                    AND id_materia = {$id_materia}
                    AND voti.flag_canc = 0
                ORDER BY data {$order}";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $dati_materia = estrai_dati_materia($id_materia);
        $significati_voti = estrai_significati_voti($dati_materia['tipo_valutazione']);
        $significati_chiave = [];

        foreach ($significati_voti as $significato) {
            $significati_chiave[$significato['voto']] = $significato;
        }

        for ($cont = 0; $cont < $numero; $cont++) {
            //inizializzo l'intervallo di tempo di accettabilità dei voti
            $array_voti[$cont] = pg_fetch_assoc($result, $cont);

            foreach ($array_voti[$cont] as $key => $value) {

                if ($key == 'voto') {
                    $array_voti[$cont]['voto_codice'] = $significati_chiave[$value]['codice'];
                    $array_voti[$cont]['voto_valore'] = $significati_chiave[$value]['valore'];

                    if (is_null($array_voti[$cont]['voto_valore'])) {
                        $array_voti[$cont]['voto_valore'] = $array_voti[$cont]['voto'];
                    }
                } elseif ($key == 'tipo') {
                    switch ($value) {
                        case '0':
                            $array_voti[$cont]['tipo_tradotto'] = 'Scritto';
                            break;
                        case '1':
                            $array_voti[$cont]['tipo_tradotto'] = 'Orale';
                            break;
                        case '2':
                            $array_voti[$cont]['tipo_tradotto'] = 'Pratico';
                            break;
                    }
                } else {
                    $array_voti[$cont][$key] = decode($value);
                }
            }
        }
    }

    return $array_voti;
    //}}} </editor-fold>
}

function estrai_voti_studente_materie($id_stud, $data_start, $data_end, $id_classe, $classi = "TUTTE", $current_key = '') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di uno studente per tutte le materie">
    //questo primo ciclo serve per avere un'entry anche se non ci sono di voti
    $classi_studente = estrai_classi_studente($id_stud);

    if($classi == 'TUTTE_CON_CORSI')
    {
        $estrai_corsi = 'SI';
    }
    else
    {
        $estrai_corsi = 'NO';
    }

    foreach ($classi_studente as $classe) {
        $id_classe_tmp = $classe["id_classe"];
        $dati_materie = estrai_materie_classe($id_classe_tmp, $estrai_corsi);

        if (is_array($dati_materie)) {
            foreach ($dati_materie as $materia) {
                $voti[$materia["id_materia"]]["ignora"]["usare"] = "NO";
                $voti[$materia["id_materia"]]["ignora"]["id_voto"] = "NON CONSIDERARE";
                $voti[$materia["id_materia"]]["ignora"]["descrizione_materia"] = $materia[2];
                $voti[$materia["id_materia"]]["ignora"]["descrizione_materia_straniera"] = $materia['descrizione_materia_straniera'];
                $voti[$materia["id_materia"]]["ignora"]["in_media_pagelle"] = $materia['in_media_pagelle'];
                $voti[$materia["id_materia"]]["ignora"]["id_materia_riferimento"] = $materia['id_materia_riferimento'];
            }
        }
    }

    if ($classi == "TUTTE") {
        foreach ($classi_studente as $classe) {
            $id_classe = $classe["id_classe"];
            $dati_materie = estrai_materie_classe($id_classe);

            if (is_array($dati_materie)) {
                foreach ($dati_materie as $materia) {
                    $voti[$materia["id_materia"]]["ignora"]["usare"] = "SI";
                    $voti[$materia["id_materia"]]["ignora"]["id_voto"] = "NON CONSIDERARE";
                    $voti[$materia["id_materia"]]["ignora"]["descrizione_materia"] = $materia[2];
                    $voti[$materia["id_materia"]]["ignora"]["descrizione_materia_straniera"] = $materia['descrizione_materia_straniera'];
                    $voti[$materia["id_materia"]]["ignora"]["in_media_pagelle"] = $materia['in_media_pagelle'];
                    $voti[$materia["id_materia"]]["ignora"]["id_materia_riferimento"] = $materia['id_materia_riferimento'];
                }
            }
        }
    }
    elseif ($classi == "TUTTE_CON_CORSI")
    {
        foreach ($classi_studente as $classe)
        {
            $id_classe = $classe["id_classe"];
            $dati_materie = estrai_materie_classe($id_classe, $estrai_corsi);

            if (is_array($dati_materie))
            {
                foreach ($dati_materie as $materia)
                {
                    $voti[$materia["id_materia"]]["ignora"]["usare"] = "SI";
                    $voti[$materia["id_materia"]]["ignora"]["id_voto"] = "NON CONSIDERARE";
                    $voti[$materia["id_materia"]]["ignora"]["descrizione_materia"] = $materia[2];
                    $voti[$materia["id_materia"]]["ignora"]["descrizione_materia_straniera"] = $materia['descrizione_materia_straniera'];
                    $voti[$materia["id_materia"]]["ignora"]["in_media_pagelle"] = $materia['in_media_pagelle'];
                    $voti[$materia["id_materia"]]["ignora"]["id_materia_riferimento"] = $materia['id_materia_riferimento'];
                }
            }
        }
    }
    else
    {
        $dati_materie = estrai_materie_classe($id_classe);

        foreach ($dati_materie as $materia)
        {
            $voti[$materia["id_materia"]]["ignora"]["usare"] = "SI";
            $voti[$materia["id_materia"]]["ignora"]["id_voto"] = "NON CONSIDERARE";
            $voti[$materia["id_materia"]]["ignora"]["descrizione_materia"] = $materia[2];
            $voti[$materia["id_materia"]]["ignora"]["descrizione_materia_straniera"] = $materia['descrizione_materia_straniera'];
            $voti[$materia["id_materia"]]["ignora"]["in_media_pagelle"] = $materia['in_media_pagelle'];
            $voti[$materia["id_materia"]]["ignora"]["id_materia_riferimento"] = $materia['id_materia_riferimento'];
        }
    }

    $query = "SELECT
					voti.id_voto,
					voti.id_studente,
					voti.id_materia,
                    voti.id_professore,
					voti.data,
					voti.note,
					voti.tipo,
					voti.voto,
					voti.immagine_voto,
                                        voti.data_modifica,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.ordinamento,
                    pesi.peso,
                    pesi.descrizione as descrizione_peso,
                    pesi.peso_default,
                    utenti.cognome||' '||utenti.nome as professore,
                    abbinamenti_tag.id_tag
				FROM voti
				INNER JOIN materie ON materie.id_materia = voti.id_materia
                LEFT JOIN pesi ON pesi.id_peso = voti.id_peso
                LEFT JOIN utenti ON utenti.id_utente = voti.id_professore
                LEFT JOIN abbinamenti_tag ON abbinamenti_tag.id_abbinato = voti.id_voto
                    AND abbinamenti_tag.tipo_abbinato = 'voto'
                    AND abbinamenti_tag.flag_canc = 0
				WHERE voti.id_studente = '$id_stud'
					AND voti.data >= '$data_start'
					AND voti.data <= '$data_end'
					AND voti.flag_canc = 0
					AND materie.flag_canc = 0
				ORDER BY
					materie.ordinamento,
					materie.descrizione,
					voti.data";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $cont_voti = 0;
    $id_materia = -2;

    //corretti i posti in cui viene chiamata la funzione inserendo $current_key, non ancora adeguate le stampe e la funzione di manutenzione che chiama questa funzione
    if(strlen($current_key) > 0){
        $parametri_prese_visioni = [];
        $parametri_prese_visioni['tipo_oggetto'] = 'voto';
        $parametri_prese_visioni['id_studente'] = $id_stud;

        $elenco_prese_visioni = nextapi_call("prese_visioni/elenco_prese_visioni_filtrate", "GET", $parametri_prese_visioni, $current_key);

        /*$sql = "SELECT
                        prese_visioni.id_oggetto as id_voto,
                        prese_visioni.id_parente,
                        prese_visioni.data_presa_visione,
                        parenti.cognome || ' ' || parenti.nome as nome_parente
                    FROM
                        prese_visioni
                        INNER JOIN parenti on parenti.id_parente = prese_visioni.id_parente
                    WHERE
                        prese_visioni.tipo_oggetto = 'voto'
                        AND
                        prese_visioni.id_studente = " . $id_stud . "
                        AND
                        prese_visioni.id_parente IN (
                                        SELECT id_parente
                                            FROM
                                                parenti_studenti
                                            WHERE
                                                id_studente = " . $id_stud . ") ";

        $res = pgsql_query($sql);
        $elenco_prese_visioni = pg_fetch_all($res);*/

        $lista_indicizzata_prese_visioni = [];
        $lista_indicizzata_date_prese_visioni = [];
        foreach ($elenco_prese_visioni as $key_voto => $prese_visioni_voto) {
            foreach ($prese_visioni_voto as $key => $value){
                $lista_indicizzata_prese_visioni[$value['id_voto']][$value['id_parente']]['nome'] = decode($value['name']);
                $lista_indicizzata_prese_visioni[$value['id_voto']][$value['id_parente']]['data'] = date('d/m/Y H:i', $value['data_presa_visione']);
                if($value['data_presa_visione'] > $lista_indicizzata_date_prese_visioni[$value['id_voto']]['data_ts']){
                    $lista_indicizzata_date_prese_visioni[$value['id_voto']]['data_ts'] = $value['data_presa_visione'];
                }
            }
        }
    }


    if ($numero > 0) {
        //scorro i risultati ottenuti e li inserisco nella matrice
        for ($cont = 0; $cont < $numero; $cont++) {
            $data_tmp = pg_fetch_result($result, $cont, "data");
            $id_materia = pg_fetch_result($result, $cont, "id_materia");
            $id_voto = pg_fetch_result($result, $cont, "id_voto");
            $data_modifica = pg_fetch_result($result, $cont, "data_modifica");

            $today = getdate($data_tmp);
            $dati_materia = estrai_dati_materia($id_materia);

            //nell'elemento 4 dell'array c'è ciò che visualizzo all'interno della griglia annuale
            $voti[$id_materia][$id_voto]["id_voto"] = pg_fetch_result($result, $cont, "id_voto");
            $voti[$id_materia][$id_voto]["id_materia"] = pg_fetch_result($result, $cont, "id_materia");
            $voti[$id_materia][$id_voto]["id_professore"] = pg_fetch_result($result, $cont, "id_professore");
            $voti[$id_materia][$id_voto]["tipo"] = pg_fetch_result($result, $cont, "tipo");
            $voti[$id_materia][$id_voto]["voto"] = pg_fetch_result($result, $cont, "voto");
            $voti[$id_materia][$id_voto]["note"] = decode(pg_fetch_result($result, $cont, "note"));
            $voti[$id_materia][$id_voto]["data"] = pg_fetch_result($result, $cont, "data");
            $voti[$id_materia][$id_voto]["codice_materia"] = pg_fetch_result($result, $cont, "codice");
            $voti[$id_materia][$id_voto]["descrizione_materia"] = decode(pg_fetch_result($result, $cont, "descrizione"));
            $voti[$id_materia][$id_voto]["descrizione_materia_straniera"] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
            $voti[$id_materia][$id_voto]["anno"] = $today['year'];
            $voti[$id_materia][$id_voto]["mese"] = $today['mon'];
            $voti[$id_materia][$id_voto]["giorno"] = $today['mday'];
            $voti[$id_materia][$id_voto]["peso"] = pg_fetch_result($result, $cont, "peso");
            $voti[$id_materia][$id_voto]["descrizione_peso"] = decode(pg_fetch_result($result, $cont, "descrizione_peso"));
            $voti[$id_materia][$id_voto]["peso_default"] = pg_fetch_result($result, $cont, "peso_default");
            $voti[$id_materia][$id_voto]["professore"] = decode(pg_fetch_result($result, $cont, "professore"));
            $voti[$id_materia][$id_voto]["id_tag"] = pg_fetch_result($result, $cont, "id_tag");

            //estraggo il significato del voto numerico e lo sostituisco
            $id_tipo_voto = $dati_materia[13];
            $voto = $voti[$id_materia][$id_voto]["voto"];
            $significati_voto = estrai_significati_voti_specifici($voto, $id_tipo_voto);
            $voti[$id_materia][$id_voto]["codice_voto"] = $significati_voto["codice"];
            $voti[$id_materia][$id_voto]["valore_voto"] = $significati_voto["valore"];

            //aggiungo le prese visioni se sono presenti
            if(isset($lista_indicizzata_prese_visioni[$id_voto])){
                if($lista_indicizzata_date_prese_visioni[$id_voto]['data_ts'] >= $data_modifica){
                    $voti[$id_materia][$id_voto]["lista_prese_visioni"] = $lista_indicizzata_prese_visioni[$id_voto];
                }else{
                    $voti[$id_materia][$id_voto]["lista_prese_visioni"] = [];
                }
            }else{
                $voti[$id_materia][$id_voto]["lista_prese_visioni"] = [];
            }

            $cont_voti = $cont_voti + 1;
        }
    }

    file_put_contents('/tmp/prese_visioni_voti.txt', print_r($voti,true));
    file_put_contents('/tmp/prese_visioni_base_voti.txt', print_r($elenco_prese_visioni,true));
    return $voti;
    //}}} </editor-fold>
}

function estrai_voti_studente($id_stud, $id_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di uno studente">
    $dati_materia = estrai_dati_materia($id_materia);

    $array_voti[5][0][50] = "Gennaio";
    $array_voti[6][0][50] = "Febbraio";
    $array_voti[7][0][50] = "Marzo";
    $array_voti[8][0][50] = "Aprile";
    $array_voti[9][0][50] = "Maggio";
    $array_voti[10][0][50] = "Giugno";
    $array_voti[11][0][50] = "Luglio";
    $array_voti[12][0][50] = "Agosto";
    $array_voti[1][0][50] = "Settembre";
    $array_voti[2][0][50] = "Ottobre";
    $array_voti[3][0][50] = "Novembre";
    $array_voti[4][0][50] = "Dicembre";

    global $db_key, $db_official;

    if ($db_key != $db_official) {
        $array_anno_attuale = explode("_", substr($db_key, 10));
        $anno_inizio = $array_anno_attuale[0];
        $anno_fine = $array_anno_attuale[1];
    } else {
        $anno_scolastico_settato = explode("/", estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"));
        $anno_inizio = $anno_scolastico_settato[0];
        $anno_fine = $anno_scolastico_settato[1];
    }

    $annoattuale = $anno_fine;
    $anno = $annoattuale;

    //creo una matrice tridimensionale per inserire in ogni giorno dell'anno tutti i voti
    for ($mese = 1; $mese < 13; $mese++) {
        $mese_tmp = $mese < 5 ? $mese + 8 : $mese - 4;

        if ($mese >= 9) {
            $anno = $annoattuale - 1;
        }

        if (checkdate($mese_tmp, "28", $anno)) {
            $giornoacc = "28";
        }

        if (checkdate($mese_tmp, "29", $anno)) {
            $giornoacc = "29";
        }

        if (checkdate($mese_tmp, "30", $anno)) {
            $giornoacc = "30";
        }

        if (checkdate($mese_tmp, "31", $anno)) {
            $giornoacc = "31";
        }

        $array_voti[$mese][32][0] = $anno;
        $array_voti[$mese][33][0] = $giornoacc;
        $array_voti[$mese][34][0] = $mese;

        //inserisco in ogni giorno del mese una lineetta alitrimenti non si vedono le celle
        for ($day = 0; $day < $giornoacc; $day++) {
            $array_voti[$mese][$day][3] = "-";
        }
    }
    //inserisco la numerazione dei giorni nella prima riga della matrice
    for ($day = 1; $day <= 31; $day++) {
        if ($day < 10) {
            $array_voti[0][$day][0] = "0" . $day;
        } else {
            $array_voti[0][$day][0] = $day;
        }
    }

    $array_voti[0][0][0] = "";

    $query = "SELECT
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					utenti.nome AS nomeprof,
					studenti.nome,
					studenti.cognome,
					voti.id_voto,
					voti.id_studente,
					voti.data,
					voti.note,
					voti.tipo,
					voti.voto,
					voti.id_professore,
					voti.immagine_voto
				FROM studenti
                INNER JOIN (
                    utenti INNER JOIN (
                        materie INNER JOIN voti ON materie.id_materia = voti.id_materia
                    )
                    ON utenti.id_utente = voti.id_professore
                )
                ON studenti.id_studente = voti.id_studente
				WHERE voti.id_studente = {$id_stud}
					AND materie.id_materia = {$id_materia}
					AND utenti.tipo_utente = 'P'
					AND studenti.flag_canc = 0
					AND voti.flag_canc = 0
					AND materie.flag_canc = 0
				ORDER BY voti.tipo";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        //inizializzo l'intervallo di tempo di accettabilità dei voti
        global $db_key, $db_official;

        if ($db_key != $db_official) {
            $array_anno_attuale = explode("_", substr($db_key, 10));
            $anno_inizio = $array_anno_attuale[0];
            $anno_fine = $array_anno_attuale[1];
        } else {
            $anno_scolastico_settato = explode("/", estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"));
            $anno_inizio = $anno_scolastico_settato[0];
            $anno_fine = $anno_scolastico_settato[1];
        }

        $start_interval = mktime(1, 1, 1, 9, 1, $anno_inizio);
        $end_interval = mktime(1, 1, 1, 8, 31, $anno_fine);

        //scorro i risultati ottenuti e li inserisco nella matrice
        for ($cont = 0; $cont < $numero; $cont++) {
            $data_tmp = pg_fetch_result($result, $cont, "data");

            $today = getdate($data_tmp);
            $mese_tmp = $today['mon'];
            $giorno_tmp = $today['mday'] - 1;
            $anno_tmp = $today['year'];

            if (($data_tmp >= $start_interval) && ($data_tmp <= $end_interval)) {
                //ordinamento dei mesi da settembre ad agosto
                if ($mese_tmp > 8) {
                    $mese_tmp = $mese_tmp - 8;
                } else {
                    $mese_tmp = $mese_tmp + 4;
                }

                // inserisco la possibilità di tre valori diversi per lo stesso giorno che diventeranno tre bottoni in visualizzazione
                if (($array_voti[$mese_tmp][$giorno_tmp][15] != "") && ($array_voti[$mese_tmp][$giorno_tmp][15] != "vuoto")) {
                    $indice_interno = 31;
                } else {
                    $array_voti[$mese_tmp][$giorno_tmp][31] = "vuoto";
                    if ($array_voti[$mese_tmp][$giorno_tmp][0] != "") {
                        $indice_interno = 15;
                    } else {
                        $array_voti[$mese_tmp][$giorno_tmp][15] = "vuoto";
                        $indice_interno = 0;
                    }
                }

                //nell'elemento 4 dell'array c'è ciò che visualizzo all'interno della griglia annuale
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 0] = pg_fetch_result($result, $cont, "id_voto");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 1] = pg_fetch_result($result, $cont, "cognome");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 2] = pg_fetch_result($result, $cont, "tipo");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 3] = pg_fetch_result($result, $cont, "voto");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 4] = decode(pg_fetch_result($result, $cont, "note"));
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 5] = pg_fetch_result($result, $cont, "data");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 6] = pg_fetch_result($result, $cont, "codice");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 7] = pg_fetch_result($result, $cont, "descrizione");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 27] = pg_fetch_result($result, $cont, "descrizione_materia_straniera");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 8] = pg_fetch_result($result, $cont, "nomeprof");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 9] = pg_fetch_result($result, $cont, "nome");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 13] = pg_fetch_result($result, $cont, "immagine_voto");
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 10] = $anno_tmp;
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 11] = $mese_tmp;
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 12] = $giorno_tmp;

                //estraggo il significato del voto numerico e lo sostituisco
                $id_tipo_voto = $dati_materia[13];
                $voto = $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 3];
                $significati_voto = estrai_significati_voti_specifici($voto, $id_tipo_voto);
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 3] = $significati_voto["codice"];
                $array_voti[$mese_tmp][$giorno_tmp][$indice_interno + 14] = $significati_voto["valore"];
            }
        }
    }

    return $array_voti;
    //}}} </editor-fold>
}

function estrai_voto($id_voto) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre il voto ATTENZIONE NON CONSIDERA IL FLAG CANC">
    $query = "SELECT voti.*,
                    materie.descrizione as descrizione_materia,
                    materie.nome_materia_breve as nome_materia_breve,
                    studenti.cognome || ' ' || studenti.nome as descrizione_studente,
                    abbinamenti_tag.id_tag
                FROM voti
                LEFT JOIN materie on materie.id_materia = voti.id_materia
                LEFT JOIN studenti on studenti.id_studente = voti.id_studente
                LEFT JOIN abbinamenti_tag ON abbinamenti_tag.id_abbinato = voti.id_voto
                    AND abbinamenti_tag.tipo_abbinato = 'voto'
                    AND abbinamenti_tag.flag_canc = 0
                WHERE id_voto = {$id_voto}
        ";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0)
    {
        /*$voto[0] = pg_fetch_result($result, 0, "id_voto");
        $voto[1] = pg_fetch_result($result, 0, "id_studente");
        $voto[2] = pg_fetch_result($result, 0, "id_classe");
        $voto[3] = pg_fetch_result($result, 0, "id_professore");
        $voto[4] = pg_fetch_result($result, 0, "id_materia");
        $voto[5] = pg_fetch_result($result, 0, "tipo");
        $voto[6] = pg_fetch_result($result, 0, "voto");
        $voto[7] = decode(pg_fetch_result($result, 0, "note"));
        $voto[8] = pg_fetch_result($result, 0, "data");
        $voto[9] = pg_fetch_result($result, 0, "inviosms");
        $voto[11] = pg_fetch_result($result, 0, "immagine_voto");
        $data = getdate($voto[8]);
        $voto[10] = $data["mday"] . "/" . $data["mon"] . "/" . $data["year"];*/
        $voto = pg_fetch_assoc($result,0);
        $voto['data_tradotta'] = date('d/m/Y', $voto['data']);


        switch ($voto['tipo'])
        {
            case "0":
                $voto['tipo_tradotto'] = "Scritto";
                break;
            case "1":
                $voto['tipo_tradotto'] = "Orale";
                break;
            case "2":
                $voto['tipo_tradotto'] = "Pratico";
                break;
        }

        foreach ($voto as $key => $value)
        {
            $voto[$key] = decode($value);
        }
    }

    return $voto;
    //}}} </editor-fold>
}

/**
 * Inserisce un nuovo voto
 *
 * @param integer $id_stud
 * @param integer $id_prof
 * @param integer $id_materia
 * @param integer $id_classe
 * @param integer $tipo_voto
 * @param mixed   $voto
 * @param string  $note_voto
 * @param type    $data
 * @param integer $current_user
 * @param integer $tipo_aggiuntivo
 * @param integer $id_obiettivo
 * @return integer
 */
function inserisci_voto($id_stud, $id_prof, $id_materia, $id_classe, $tipo_voto, $voto, $note_voto, $data, $current_user, $current_key, $tipo_aggiuntivo = '', $id_obiettivo = 0, $competenze = null, $proprieta = null, $id_peso = null) {
    //{{{ <editor-fold defaultstate="collapsed">
    $note_voto = encode($note_voto);
    $voto_unico_normale_attivita_didattica = estrai_parametri_singoli('VOTO_UNICO_NORMALE_ATTIVITA_DIDATTICA');
    $id_voto = null;
    $id_peso = (is_null($id_peso)) ? 'null' : $id_peso;
    if ($id_peso == 'null')
    {
        $peso_default = estrai_peso_default('voti');
        $id_peso = (isset($peso_default['id_peso'])) ? $peso_default['id_peso'] : $id_peso;
    }

    if ($voto_unico_normale_attivita_didattica == 'SI_MATERIA' || $voto_unico_normale_attivita_didattica == 'SI_ABBINAMENTO') {
        //{{{ <editor-fold defaultstate="collapsed" desc="controllo per voto unico: deduco che tipo di voto assegnare ignorando quello che viene passato">
        $voto_unico_materia = estrai_voto_unico_abbinamento((int)$id_materia, (int)$id_classe, (int)$id_prof);

        if ($voto_unico_materia == '1') {
            $query = "SELECT scritto, orale, pratico FROM materie
                      WHERE id_materia = " . $id_materia;

            $result = pgsql_query($query) or die("Invalid $query");
            $numero = pg_num_rows($result);

            if ($numero == 1) {
                $dati_materia = pg_fetch_assoc($result, 0);

                if ($dati_materia['scritto'] == '1') {
                    $tipo_voto = 0;
                } elseif ($dati_materia['orale'] == '1') {
                    $tipo_voto = 1;
                } elseif ($dati_materia['pratico'] == '1') {
                    $tipo_voto = 2;
                }
            }
        }
        //}}} </editor-fold>
    }
    $dati_utente = estrai_utente($current_user);
    if($dati_utente['tipo_utente'] != 'A'){
        $stato_voto = [];
        $parametri_voto = [];

        switch(intval($tipo_voto)){
            case 0:
                $parametri_voto['tipo'] = 'scritto';
                break;
            case 1:
                $parametri_voto['tipo'] = 'orale';
                break;
            case 2:
                $parametri_voto['tipo'] = 'pratico';
                break;
        }
        $parametri_voto['data'] = $data;

        $stato_voto = nextapi_call("voti/verifica_data", "GET", $parametri_voto, $current_key);

    }else{
        $stato_voto['ins']['valore'] = 'SI';
    }

    if($stato_voto['ins']['valore'] == 'SI'){

        $data_inizio_lezioni = estrai_parametri_singoli('DATA_INIZIO_LEZIONI', $id_classe, 'classe');

        if ($voto != 'X' && $tipo_voto >= 0 && $voto !== '' && $data >= $data_inizio_lezioni && date('Y-m-d', $data) <= date('Y-m-d', time())) {
            $data_insert = mktime(12, 0, 0, date('n', $data), date('j', $data), date('Y', $data));

            if (is_numeric($voto)) {
                $votoA = explode('.', $voto);
                $voto = strlen($votoA[1]) == 1 ? $voto . '0' : $voto;
            }

            $query = "INSERT INTO voti (
                        id_studente,
                        id_professore,
                        id_materia,
                        id_classe,
                        tipo,
                        voto,
                        note,
                        data,
                        tipo_aggiuntivo,
                        inviosms,
                        id_peso
                      ) VALUES (
                        {$id_stud},
                        {$id_prof},
                        {$id_materia},
                        {$id_classe},
                        '{$tipo_voto}',
                        '{$voto}',
                        '{$note_voto}',
                        {$data_insert},
                        '{$tipo_aggiuntivo}',
                        '0',
                        {$id_peso}
                      )
                      RETURNING id_voto";

            $result = pgsql_query($query) or die("Invalid $query");
            $numero = pg_num_rows($result);

            if ($numero > 0) {
                $id_voto = pg_fetch_result($result, 0, "id_voto");
            }
              file_put_contents('/tmp/id_voto.txt', print_r($id_voto,true));

            // Se ci sono gli obiettivi crea il riferimento tra voto e obiettivo
            if ($id_voto > 0 && $id_obiettivo > 0) {
                inserisci_valore_campo_libero(null, $id_voto, $id_obiettivo, 'VOTO', 'TESTO', $current_user);
            }

                    if ($id_voto > 0 && is_array($competenze) && count($competenze > 0)) {
                            foreach ($competenze as $competenza) {
                            inserisci_valore_campo_libero(null, $id_voto, $competenza['id_competenza'], $competenza['id_select'], 'PRECOMPILATO', $current_user);
                            }
                    }

                    if ($id_voto > 0 && is_array($proprieta) && count($proprieta > 0)) {
                            foreach ($proprieta as $proprieta_s) {
                            inserisci_valore_campo_libero(null, $id_voto, $proprieta_s['id_proprieta'], $proprieta_s['valore'], 'TESTO', $current_user);
                            }
                    }

            inserisci_log(["id_voto" => $id_voto], "voti", $current_user, "INTERFACCIA", "INSERIMENTO");

            $anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
            $parametri = [
                "id_oggetto"            =>  $id_voto,
                "tipo_oggetto"          =>  "voti",
                "anno_riferimento"      =>  $anno_scolastico_attuale,
                "id_studenti"             =>  ["0"        =>  $id_stud],
                "notifica_studenti"     =>  "SI",
                "notifica_parenti"      =>  "SI",
                "notifica_docenti"      =>  "NO",
                "notifica_dirigenti"    =>  "NO"
            ];

            $notifiche = nextapi_call("notifiche/inserisci", "POST", $parametri, $current_key);
        }
    }

    return $id_voto;
    //}}} </editor-fold>
}

function modifica_voto($id_voto, $tipo_voto, $voto, $note_voto, $id_professore, $datanuova, $current_user, $current_key, $tipo_aggiuntivo = '', $competenze = null, $proprieta = null, $id_peso = null, $id_materia_nuova = null) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare i dati di un voto">
    $note = encode($note_voto);
    $voto_unico = estrai_parametri_singoli('VOTO_UNICO_NORMALE_ATTIVITA_DIDATTICA');
    $id_voto = (int) $id_voto;
    $dati_voto = estrai_voto($id_voto);
    $id_peso = (is_null($id_peso)) ? 'null' : $id_peso;
    $set_materia = '';

    if ($id_materia_nuova != null){
        $set_materia = " id_materia = " . $id_materia_nuova . ", ";
    }

    if ($voto_unico == 'SI_MATERIA' || $voto_unico == 'SI_ABBINAMENTO') {
        //{{{ <editor-fold defaultstate="collapsed" desc="controllo per voto unico: deduco che tipo di voto assegnare ignorando quello che viene passato">
        $query_select = "SELECT DISTINCT
								scritto,
								orale,
								pratico,
								voto_unico_normale_attivita
							FROM
								classi_prof_materie,
								voti,
								materie
							WHERE voti.flag_canc = 0
								AND	materie.flag_canc = 0
								AND voti.id_classe = classi_prof_materie.id_classe
								AND voti.id_professore = classi_prof_materie.id_professore
								AND voti.id_materia = classi_prof_materie.id_materia
								AND voti.id_materia = materie.id_materia
								AND id_voto = " . $id_voto;

        $result = pgsql_query($query_select) or die("Invalid $query_select");
        $dati_materia = pg_fetch_assoc($result, 0);

        if ($dati_materia['voto_unico_normale_attivita'] == '1') {
            if ($dati_materia['scritto'] == '1') {
                $tipo_voto = '0';
            } elseif ($dati_materia['orale'] == '1') {
                $tipo_voto = '1';
            } elseif ($dati_materia['pratico'] == '1') {
                $tipo_voto = '2';
            }
        }
        //}}} </editor-fold>
    }

    $dati_utente = estrai_utente($current_user);
    if($dati_utente['tipo_utente'] != 'A'){
        $stato_voto = [];
        $parametri_voto = [];

        switch(intval($tipo_voto)){
            case 0:
                $parametri_voto['tipo'] = 'scritto';
                break;
            case 1:
                $parametri_voto['tipo'] = 'orale';
                break;
            case 2:
                $parametri_voto['tipo'] = 'pratico';
                break;
        }
        $parametri_voto['data'] = $dati_voto['data'];

        $stato_voto = nextapi_call("voti/verifica_data", "GET", $parametri_voto, $current_key);

    }else{
        $stato_voto['mod']['valore'] = 'SI';
    }

    if($stato_voto['mod']['valore'] == 'SI'){
        // voto vuoto, tipo_voto vuoto o voto in data futura: errore
        $data_inizio_lezioni = estrai_parametri_singoli('DATA_INIZIO_LEZIONI', $dati_voto['id_classe'], 'classe');

        if ($voto === "" || $tipo_voto === "" || ($datanuova != "no" && $datanuova < $data_inizio_lezioni && $datanuova > time())) {
            return;
        }

        // Necessario perchè dal Registro il valore del voto arriva come float
        if (is_numeric($voto)) {
            $votoA = explode('.', $voto);
            $voto = strlen($votoA[1]) == 1 ? $voto . '0' : $voto;
        }

        if ($datanuova != "no") {
            $query = "UPDATE voti
                      SET tipo = '{$tipo_voto}',
                        {$set_materia}
                        tipo_aggiuntivo = '{$tipo_aggiuntivo}',
                        voto = '{$voto}',
                        note = '{$note}',
                        data = {$datanuova},
                        id_professore = {$id_professore},
                        id_peso = {$id_peso}
                      WHERE id_voto={$id_voto}";
        } else {
            $query = "UPDATE voti
                      SET tipo = '{$tipo_voto}',
                        {$set_materia}
                        tipo_aggiuntivo = '{$tipo_aggiuntivo}',
                        voto = '{$voto}',
                        note = '{$note}',
                        id_professore = {$id_professore},
                        id_peso = {$id_peso}
                      WHERE id_voto = {$id_voto}";
        }

        pgsql_query($query) or die("Invalid $query");

         if ($id_voto > 0 && is_array($competenze) && count($competenze > 0)) {
                foreach ($competenze as $competenza) {
                        inserisci_valore_campo_libero($competenza['id_valore'], $id_voto, $competenza['id_competenza'], $competenza['id_select'], 'PRECOMPILATO', $current_user);

                }
        }
        if ($id_voto > 0 && is_array($proprieta) && count($proprieta > 0)) {
                foreach ($proprieta as $proprieta_s) {
                        inserisci_valore_campo_libero($proprieta_s['id_valore'], $id_voto, $proprieta_s['id_proprieta'], $proprieta_s['valore'], 'TESTO', $current_user);
                }
        }

        inserisci_log(["id_voto" => $id_voto], "voti", $current_user, "INTERFACCIA", "MODIFICA");


        $anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
        $parametri = [
                "id_oggetto"            =>  $id_voto,
                "tipo_oggetto"          =>  "voti",
                "anno_riferimento"      =>  $anno_scolastico_attuale,
                "id_studenti"             =>  ["0"        =>  $dati_voto['id_studente']],
                "notifica_studenti"     =>  "SI",
                "notifica_parenti"      =>  "SI",
                "notifica_docenti"      =>  "NO",
                "notifica_dirigenti"    =>  "NO"
            ];

        $notifiche = nextapi_call("notifiche/inserisci", "POST", $parametri, $current_key);
    }

    return $id_voto;
    //}}} </editor-fold>
}

/**
 * Funzione per eliminare un voto
 *
 * @param integer $id_voto
 * @param integer $current_user
 * @return integer
 */
function elimina_voto($id_voto, $current_user, $current_key) {
    /* {{{ */
    $dati_voto = estrai_voto($id_voto);

    $dati_utente = estrai_utente($current_user);
    if($dati_utente['tipo_utente'] != 'A'){
        $stato_voto = [];
        $parametri_voto = [];

        switch(intval($dati_voto['tipo'])){
            case 0:
                $parametri_voto['tipo'] = 'scritto';
                break;
            case 1:
                $parametri_voto['tipo'] = 'orale';
                break;
            case 2:
                $parametri_voto['tipo'] = 'pratico';
                break;
        }
        $parametri_voto['data'] = $dati_voto['data'];

        $stato_voto = nextapi_call("voti/verifica_data", "GET", $parametri_voto, $current_key);
    }else{
        $stato_voto['del']['valore'] = 'SI';
    }

    if($stato_voto['del']['valore'] == 'SI'){
        $query = "UPDATE voti SET flag_canc = " . time() . " WHERE id_voto = {$id_voto}";

        pgsql_query($query) or die("Invalid $query");

        $anno_scolastico_attuale = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
        $parametri = [
                "id_oggetto"            =>  $id_voto,
                "tipo_oggetto"          =>  "voti",
                "anno_riferimento"      =>  $anno_scolastico_attuale,
                "id_studenti"             =>  ["0"        =>  $dati_voto['id_studente']],
                "notifica_studenti"     =>  "SI",
                "notifica_parenti"      =>  "SI",
                "notifica_docenti"      =>  "NO",
                "notifica_dirigenti"    =>  "NO"
            ];

        $notifiche = nextapi_call("notifiche/inserisci", "POST", $parametri, $current_key);

        inserisci_log(["id_voto" => $id_voto], "voti", $current_user, "INTERFACCIA", "ELIMINAZIONE");
        return true;
    }else{
        $error = $stato_voto['del']['messaggio'];
        return $error;
    }
    /* }}} */
}

function estrai_tabellone_temporale_annotazioni_studenti_classe($id_classe, $id_materia, $data_inizio, $data_fine) {
    /* {{{ */
    $annotazioni = estrai_annotazioni_studente($id_classe, 'classe', $id_materia, null, $data_inizio, $data_fine);

    if (is_array($annotazioni) && count($annotazioni) > 0) {
        foreach ($annotazioni as $annotazione) {
            $dati[date('d/m/Y', $annotazione['data'])][$annotazione['id_studente']][] = $annotazione;
        }
    }

    return $dati;
    /* }}} */
}

function estrai_tabellone_voti_stile_registro_cartaceo($id_materia, $id_classe, $periodo = 'tutto anno') {
    /* {{{ */
    global $current_user;

    if (is_numeric($periodo) && $periodo > 8 && $periodo < 13) {
        $data_rif = estrai_parametri_singoli("DATA_INIZIO_LEZIONI", $id_classe, 'classe');
        $anno = date('Y', $data_rif);
        $inizio_lezioni = mktime(0, 0, 0, intval($periodo), 1, intval($anno));
        $fine_lezioni = mktime(0, 0, 0, intval($periodo) + 1, 1, intval($anno));
    } elseif (is_numeric($periodo) && $periodo > 0 && $periodo < 7) {
        $data_rif = estrai_parametri_singoli("DATA_FINE_LEZIONI", $id_classe, 'classe');
        $anno = date('Y', $data_rif);
        $inizio_lezioni = mktime(0, 0, 0, intval($periodo), 1, intval($anno));
        $fine_lezioni = mktime(0, 0, 0, intval($periodo) + 1, 1, intval($anno));
    } else {
        switch ($periodo) {
            case 'primo':
                $inizio_lezioni = estrai_parametri_singoli("DATA_INIZIO_LEZIONI", $id_classe, 'classe');
                $fine_lezioni = estrai_parametri_singoli("DATA_INIZIO_SECONDO_QUADRIMESTRE", $id_classe, 'classe');
                break;
            case 'secondo':
                $inizio_lezioni = estrai_parametri_singoli("DATA_INIZIO_SECONDO_QUADRIMESTRE", $id_classe, 'classe');
                $fine_lezioni = estrai_parametri_singoli("DATA_FINE_LEZIONI", $id_classe, 'classe');
                break;
            default:
                $inizio_lezioni = estrai_parametri_singoli("DATA_INIZIO_LEZIONI", $id_classe, 'classe');
                $fine_lezioni = estrai_parametri_singoli("DATA_FINE_LEZIONI", $id_classe, 'classe');
                break;
        }
    }

    $studenti = estrai_studenti_classe_registro($id_classe);
    $orario = estrai_orario_classe($id_classe, $inizio_lezioni, $fine_lezioni, $id_materia);
    $firme = estrai_tabellone_temporale_firme_classe_materia($id_classe, $id_materia, $inizio_lezioni, $fine_lezioni);

    if (is_array($orario)) {
        foreach ($orario as $lezione) {
            $mese = date('m', $lezione["data_inizio"]);
            $giorno = date('d', $lezione["data_inizio"]);
            $tabellone_registro['date'][$mese][$giorno] = 'X';
            $tabellone_registro['timestamp'][$mese][$giorno] = $lezione["data_inizio"];
            $orario_indicizzato[date('mdY', $lezione["data_inizio"])][] = $lezione; //da usare per il calcolo delle assenze alle lezioni
            $anno = date('Y', $lezione["data_inizio"]);
            $chiave_giorno = "$giorno/$mese/$anno";
            $found = false;

            if (is_array($firme[$chiave_giorno])) {
                //Marco tutte le ore del giorno per vedere se sono state firmate dal professore corrente
                foreach ($firme[$chiave_giorno] as $firma) {
                    if (intval($firma['data']) >= intval($lezione["data_inizio"]) && intval($firma['data']) <= intval($lezione["data_fine"]) && $firma['id_professore'] == $current_user) {
                        $found = true;
                        $firma_trovata = $firma;
                    }
                }

                if (!$found) {
                    // Eseguo una seconda ricerca meno restrittiva nel caso abbia firmato un'altro (la faccio separata perche se ha firmato il prof principale devo usare lui)
                    foreach ($firme[$chiave_giorno] as $firma) {
                        if (intval($firma['data']) >= intval($lezione["data_inizio"]) && intval($firma['data']) <= intval($lezione["data_fine"])) {
                            $found = true;
                            $firma_trovata = $firma;
                        }
                    }
                }
            }

            if ($found) {
                $tabellone_registro['firme'][$mese][$giorno][] = $firma_trovata;
            } else {
                $tabellone_registro['firme'][$mese][$giorno][] = [
                    'data'          => $lezione["data_inizio"],
                    'id_professore' => -1
                ];
            }
        }
    }

    $voti = estrai_tabellone_temporale_voti_studenti_classe($id_classe, $id_materia, $inizio_lezioni, $fine_lezioni, 1); // Solo voti orali
    $scritti = estrai_tabellone_temporale_voti_studenti_classe($id_classe, $id_materia, $inizio_lezioni, $fine_lezioni, 0); // Scritti
    $pratici = estrai_tabellone_temporale_voti_studenti_classe($id_classe, $id_materia, $inizio_lezioni, $fine_lezioni, 2); // Pratici
    $medie_voto = estrai_tabellone_temporale_medie_studenti_classe($id_classe, $id_materia, $inizio_lezioni, $fine_lezioni);
    $annotazioni = estrai_tabellone_temporale_annotazioni_studenti_classe($id_classe, $id_materia, $inizio_lezioni, $fine_lezioni);
    $assenze = estrai_tabellone_temporale_assenze_studenti_classe_periodo_per_registro_voti($id_classe, $orario_indicizzato, $inizio_lezioni, $fine_lezioni, $current_key);
    $data_corrente = $inizio_lezioni;

    do {
        $mese = date('m', $data_corrente);
        $giorno = date('d', $data_corrente);
        $anno = date('Y', $data_corrente);
        $chiave_giorno = "$giorno/$mese/$anno";

        if (is_array($studenti) && count($studenti) > 0) {
            foreach ($studenti as $studente) {
                $id_studente = $studente['id_studente'];
                $tabellone_registro['studenti'][$id_studente]['nome'] = $studente['cognome'] . ' ' . $studente['nome'];
                $tabellone_registro['studenti'][$id_studente]['esito'] = $studente['esito'];
                $tabellone_registro['studenti'][$id_studente]['registro'] = $studente['registro'];
                $tabellone_registro['studenti'][$id_studente]['esonero_religione'] = $studente['esonero_religione'];
                $tabellone_registro['studenti'][$id_studente]['esonero_ed_fisica'] = $studente['esonero_ed_fisica'];
                $tabellone_registro['studenti'][$id_studente]['medie_voto'] = $medie_voto[$id_studente];

                if (is_array($voti[$chiave_giorno][$id_studente]) && count($voti[$chiave_giorno][$id_studente]) > 0) {
                    $tabellone_registro['date'][$mese][$giorno] = 'V';
                    $tabellone_registro['studenti'][$id_studente]['voti'][$mese][$giorno] = $voti[$chiave_giorno][$id_studente];
                }

                if (is_array($assenze[$chiave_giorno][$id_studente]) && count($assenze[$chiave_giorno][$id_studente]) > 0) {
                    $tabellone_registro['date'][$mese][$giorno] = 'V';
                    $tabellone_registro['studenti'][$id_studente]['assenze'][$mese][$giorno] = $assenze[$chiave_giorno][$id_studente];
                }

                if (is_array($annotazioni[$chiave_giorno][$id_studente]) && count($annotazioni[$chiave_giorno][$id_studente]) > 0) {
                    $tabellone_registro['date'][$mese][$giorno] = 'V';
                    $tabellone_registro['studenti'][$id_studente]['annotazioni'][$mese][$giorno] = $annotazioni[$chiave_giorno][$id_studente];
                }

                if (is_array($scritti[$chiave_giorno][$id_studente]) && count($scritti[$chiave_giorno][$id_studente]) > 0) {
                    $tabellone_registro['date_scritti'][$mese][$giorno] = 'V';
                    $tabellone_registro['studenti'][$id_studente]['scritti'][$mese][$giorno] = $scritti[$chiave_giorno][$id_studente];
                }

                if (is_array($pratici[$chiave_giorno][$id_studente]) && count($pratici[$chiave_giorno][$id_studente]) > 0) {
                    $tabellone_registro['date_pratici'][$mese][$giorno] = 'V';
                    $tabellone_registro['studenti'][$id_studente]['pratici'][$mese][$giorno] = $pratici[$chiave_giorno][$id_studente];
                }
            }
        }

        $data_corrente = mktime(0, 0, 0, intval($mese), intval($giorno) + 1, intval($anno));
    } while ($data_corrente <= $fine_lezioni);

    $cont_col = 0;
    $cont_col_val = 0;

    if (is_array($tabellone_registro['date'])) {
        foreach ($tabellone_registro['date'] as $mese) {
            foreach ($mese as $giorno => $valore) {
                $cont_col++;
                if ($valore == 'V') {
                    $cont_col_val++;
                }
            }
        }
    }

    $tabellone_registro['cols'] = $cont_col;
    $tabellone_registro['cols_val'] = $cont_col_val;

    $cont_col = 0;

    if (is_array($tabellone_registro['date_scritti'])) {
        foreach ($tabellone_registro['date_scritti'] as $mese) {
            foreach ($mese as $giorno) {
                $cont_col++;
            }
        }
    }

    $tabellone_registro['cols_scritti'] = $cont_col;

    $cont_col = 0;

    if (is_array($tabellone_registro['date_pratici'])) {
        foreach ($tabellone_registro['date_pratici'] as $mese) {
            foreach ($mese as $giorno) {
                $cont_col++;
            }
        }
    }

    $tabellone_registro['cols_pratici'] = $cont_col;

    // carico la lista degli obiettivi per usarla nelle colonne delle medie
    $obiettivi =  estrai_campi_liberi_classe_materia($id_classe, $id_materia, 'OBIETTIVI_VOTI');
    foreach ($obiettivi as $obiettivo) {
        $tabellone_registro['obiettivi'][$obiettivo['id_campo_libero']] = $obiettivo['nome'];
    }

    return $tabellone_registro;
    /* }}} */
}

/**
 * Estrae elenco obiettivi voti
 *
 * @param integer $id_classe
 * @param integer $id_materia
 * @return array
 */
function estrai_obiettivi_voti($id_classe, $id_materia) {
    /* {{{ */
    $sql = "SELECT DISTINCT t.id, t.nome, t.descrizione, t.ordinamento FROM (
                SELECT cl.id_campo_libero AS id, cl.nome, cl.descrizione, cl.ordinamento
                FROM campi_liberi cl, campi_liberi_classe_materia clcm
                WHERE clcm.id_campo_libero = cl.id_campo_libero
                    AND (
                        clcm.id_classe = {$id_classe}
                        OR clcm.id_classe IN (
                            SELECT -1 * id_indirizzo FROM classi_complete
                            WHERE id_classe = {$id_classe}
                        )
                        OR clcm.id_classe = 0
                    )
                    AND (
                        clcm.id_materia = {$id_materia}
                        OR clcm.id_materia = 0
                    )
                    AND cl.tipo LIKE '%-OBIETTIVI_VOTI-%'
                    AND cl.flag_canc = 0
            ) t
            ORDER BY t.ordinamento";

    $res = pgsql_query($sql);
    $array = pg_fetch_all($res);

    foreach ($array as $key => $value) {
        $array[$key]['nome'] = decode($value['nome']);
        $array[$key]['descrizione'] = decode($value['descrizione']);
    }

    return $array;
    /* }}} */
}

/**
 * Estrae le medie dei voti degli studenti di una determinata classe per una
 * determinata materia di un determinato periodo.
 * NOTA: non tiene conto dei voti non numerici (A, NC, ...)
 *
 * @param integer $id_classe
 * @param integer $id_materia
 * @param integer $data_inizio
 * @param integer $data_fine
 * @return array
 */
function estrai_obiettivi_medie_studenti($id_classe = 0, $id_materia = 0, $data_inizio = 0, $data_fine = 0) {
    /* {{{ */
    $sql = "SELECT
                voti.id_studente,
                round(avg(voti.voto::float)::numeric, 2) AS valore,
                valori_campi_liberi.id_campo_libero AS id_obiettivo
            FROM
                voti,
                studenti_completi,
                materie,
                valori_campi_liberi
            WHERE voti.id_materia = {$id_materia}
                AND voti.data >= {$data_inizio}
                AND voti.data <= {$data_fine}
                AND substring(voti.voto FROM '[0-9]+')::integer > 0 -- Esclude voti non numerici
                AND studenti_completi.id_studente = voti.id_studente
                AND studenti_completi.id_classe = {$id_classe}
                AND voti.id_voto = valori_campi_liberi.id_oggetto
                AND valori_campi_liberi.id_campo_libero IN (
                    SELECT id_campo_libero
                    FROM campi_liberi
                    WHERE tipo LIKE '%-OBIETTIVI_VOTI-%'
                )
                AND voti.flag_canc = 0
                AND materie.id_materia = {$id_materia}
                AND materie.flag_canc = 0
                AND valori_campi_liberi.flag_canc = 0
            GROUP BY voti.id_studente, valori_campi_liberi.id_campo_libero";

    $res = pgsql_query($sql);
    $array = pg_fetch_all($res);
    $medie = [];

    foreach ($array as $value) {
        if (!isset($medie[$value['id_studente']])) {
            $medie[$value['id_studente']] = [];
        }

        $medie[$value['id_studente']][] = [
            'id_obiettivo' => $value['id_obiettivo'],
            'valore'       => $value['valore'],
        ];
    }

    return $medie;
    /* }}} */
}

/**
 * Controlla se ci sono materie con obiettivi
 *
 * @param array $materie
 * @return array
 */
function check_lista_obiettivi($materie) {
	/*{{{ */
    $obiettivi = [];

	if (is_array($materie)) {
        $sql = "SELECT clcm.id_campo_libero, clcm.id_materia
				FROM campi_liberi cl, campi_liberi_classe_materia clcm
				WHERE clcm.id_campo_libero = cl.id_campo_libero
					AND (
						clcm.id_materia IN (" . implode(",", $materie) . ")
						OR clcm.id_materia = 0
                	)
					AND cl.tipo LIKE '%-OBIETTIVI_VOTI-%'
					AND cl.flag_canc = 0";

        $res = pgsql_query($sql);
        $obiettivi = pg_fetch_all($res);
    }

    return $obiettivi;
	/*}}}*/
}


/**
 * Estrae elenco competenze voti con relativi valori precomp da usare
 *
 * @param integer $id_classe
 * @param integer $id_materia
 * @return array
 */
function estrai_competenze_voti($id_classe, $id_materia, $tipo = 'VOTI') {
    /* {{{ */
    $sql = "SELECT cl.id_campo_libero AS id, cl.nome, cl.descrizione
            FROM campi_liberi cl, campi_liberi_classe_materia clcm
            WHERE clcm.id_campo_libero = cl.id_campo_libero
				AND (
                    clcm.id_classe = {$id_classe}
                    OR clcm.id_classe IN (
                        SELECT -1 * id_indirizzo FROM classi_complete
                        WHERE id_classe = {$id_classe}
                    )
                    OR clcm.id_classe = 0
                )
                AND (
                    clcm.id_materia = {$id_materia}
                    OR clcm.id_materia = 0
                )
                AND cl.tipo LIKE '%-COMPETENZE_{$tipo}-%'
                AND cl.flag_canc = 0
            ORDER BY cl.ordinamento";

    $res = pgsql_query($sql);
    $array = pg_fetch_all($res);
	$lista_valori = estrai_valori_precomp();

    foreach ($array as $key => $value) {
        $array[$key]['nome'] = decode($value['nome']);
        $array[$key]['descrizione'] = decode($value['descrizione']);

        foreach ($lista_valori[$value['id']] as $id_valore_precomp => $valore_precomp) {
            $array[$key]['valori'][] = [
                'id'          => $id_valore_precomp,
                'codice'      => $valore_precomp['codice'],
                'descrizione' => $valore_precomp['descrizione'],
                'valore'      => $valore_precomp['valore']
            ];
        }
    }

    return $array;
    /* }}} */
}

/**
 * Controlla se ci sono materie con competenze
 *
 * @param array $materie
 * @return array
 *
 */
function check_lista_competenze($materie) {
	/*{{{ */
    $competenze = [];

	if (is_array($materie)) {
        $sql = "SELECT clcm.id_campo_libero, clcm.id_materia
				FROM campi_liberi cl, campi_liberi_classe_materia clcm
				WHERE clcm.id_campo_libero = cl.id_campo_libero
					AND (
						clcm.id_materia IN (" . implode(",", $materie) . ")
						OR clcm.id_materia = 0
					)
					AND cl.tipo LIKE '%-COMPETENZE_%'
					AND cl.flag_canc = 0";

        $res = pgsql_query($sql);
        $competenze = pg_fetch_all($res);
    }

    return $competenze;
	/*}}}*/
}

/**
 * Verifica se obiettivi o competenze sono attivati per l'abbinamento classe/materia
 *
 * @param int $id_materia
 * @param int $id_classe
 * @param string $tipo "COMPETENZE_VOTI", "OBIETTIVI_VOTI"
 * @return boolean
 */
function check_abbinamenti_competenze_obiettivi($id_materia, $id_classe, $tipo = null) {
    /*{{{ */
	$stato = false;
    if ($tipo) {

        $query = "SELECT clcm.id_campo_libero, clcm.id_materia
                    FROM campi_liberi cl,
                         campi_liberi_classe_materia clcm
                    WHERE clcm.id_campo_libero = cl.id_campo_libero
                         AND clcm.id_materia IN ({$id_materia},0)
                         AND (
                            clcm.id_classe IN ({$id_classe}, 0) OR
                            clcm.id_classe IN (
                                SELECT -1 * id_indirizzo FROM classi_complete
                                WHERE id_classe = {$id_classe}
                            )
                         )
                         AND cl.flag_canc = 0
                         AND cl.tipo LIKE '%-" . strtoupper($tipo) . "-%'";
        $res = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($res);

        $stato = $numero > 0 ? true : false;
    }

    return $stato;
	/*}}}*/
}

/**
 * Estrae la lista di obiettivi e competenze di un singolo abbinamento classe-materia
 *
 * @param int $id_materia
 * @param int $id_classe
 * @param string $tipo "COMPETENZE_VOTI", "OBIETTIVI_VOTI"
 * @return array
 */
function estrai_obiettivi_competenze($id_materia, $id_classe, $tipo = null)
{
    /*{{{ */
	if ($tipo) {
        $query = "SELECT cl.id_campo_libero,
                        cl.nome,
                        cl.descrizione,
                        cl.descrizione_estesa
                    FROM campi_liberi cl,
                         campi_liberi_classe_materia clcm
                    WHERE clcm.id_campo_libero = cl.id_campo_libero
                         AND clcm.id_materia IN ({$id_materia},0)
                         AND (
                            clcm.id_classe IN ({$id_classe}, 0) OR
                            clcm.id_classe IN (
                                SELECT -1 * id_indirizzo FROM classi_complete
                                WHERE id_classe = {$id_classe}
                            )
                         )
                         AND cl.flag_canc = 0
                         AND cl.tipo LIKE '%-" . strtoupper($tipo) . "-%'
                    ORDER BY cl.nome,
                        cl.descrizione";

        $res = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($res);

        $elenco = $numero > 0 ? pg_fetch_all($res) : [];
    }

    return $elenco;
	/*}}}*/
}

/**
 * Estrae la lista di obiettivi e competenze di un singolo abbinamento classe-docente
 * filtrato per le materie del docente nella classe o tutte (se coordinatore)
 * Include anche:
 *      - le materie della classe a cui sono abbinati obiettivi/competenze
 *      - le classi in cui sono utilizzati gli obiettivi/competenze
 *
 * @param int $id_materia
 * @param int $id_classe
 * @param string $tipo "COMPETENZE_VOTI", "OBIETTIVI_VOTI"
 * @return array
 */
function estrai_obiettivi_competenze_docente($id_professore, $id_classe, $tipo = null, $coordinatore = 'NO')
{
    /*{{{ */
	if ($tipo) {

        // estraggo le materie abbinate al professore
        $query = "SELECT DISTINCT m.id_materia, m.descrizione FROM classi_prof_materie c, materie m
                    WHERE c.id_materia = m.id_materia AND id_classe = {$id_classe} ";
        if ($coordinatore == 'NO') {
            $query .= " AND id_professore = {$id_professore}";
        }
        $res = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($res);

        $id_materie_docente = $numero > 0 ? pg_fetch_all_columns($res, 0) : [];
        $desc_materie_docente = $numero > 0 ? pg_fetch_all_columns($res, 1) : [];

        // estraggo la lista di obiettivi/competenze
        $query = "SELECT cl.id_campo_libero,
                        cl.nome,
                        cl.descrizione,
                        cl.descrizione_estesa,
                        clcm.id_materia,
                        m.descrizione as desc_materia,
                        clcm.id_classe
                    FROM campi_liberi cl,
                         campi_liberi_classe_materia clcm
                         LEFT JOIN materie m ON clcm.id_materia = m.id_materia
                    WHERE clcm.id_campo_libero = cl.id_campo_libero
                         AND (
                            clcm.id_classe IN ({$id_classe}, 0) OR
                            clcm.id_classe IN (
                                SELECT -1 * id_indirizzo FROM classi_complete
                                WHERE id_classe = {$id_classe}
                            )
                         )
                         AND cl.flag_canc = 0
                         AND cl.tipo LIKE '%-" . strtoupper($tipo) . "-%'
                    ORDER BY cl.nome,
                        cl.descrizione";

        $res = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($res);

        $elenco = $numero > 0 ? pg_fetch_all($res) : [];

        $elenco_finale = [];

        foreach ($elenco as $singolo_elemento) {
            $elenco_finale[$singolo_elemento['id_campo_libero']]['id_campo_libero'] = $singolo_elemento['id_campo_libero'];
            $elenco_finale[$singolo_elemento['id_campo_libero']]['nome'] = $singolo_elemento['nome'];
            $elenco_finale[$singolo_elemento['id_campo_libero']]['descrizione'] = $singolo_elemento['descrizione'];
            $elenco_finale[$singolo_elemento['id_campo_libero']]['descrizione_estesa'] = $singolo_elemento['descrizione_estesa'];

            // Recupero le materie nella classe che usano l'obiettivo/competenza
            if ($singolo_elemento['id_materia'] == 0) {
                $elenco_finale[$singolo_elemento['id_campo_libero']]['materie'] = $desc_materie_docente;
            } else {
                $elenco_finale[$singolo_elemento['id_campo_libero']]['materie'][$singolo_elemento['id_materia']] = $singolo_elemento['desc_materia'];
            }
            $id_classe = $singolo_elemento['id_classe'];
            $query = "SELECT row_number() over (order by codice_indirizzi, classe, sezione nulls last) as rownum,
                                id_classe, classe ||  ' ' || sezione || ' ' || codice_indirizzi as desc_classe
                                FROM classi_complete
                                WHERE id_indirizzo = -1 * {$id_classe}
                                    OR id_classe in (0, {$id_classe})
                                    ORDER BY 3, 1, 2";

            $res = pgsql_query($query) or die("Invalid $query");
            $numero = pg_num_rows($res);

            $lista_classi = $numero > 0 ? pg_fetch_all($res) : [];
            foreach ($lista_classi as $classe)
            {
                $elenco_finale[$singolo_elemento['id_campo_libero']]['classi'][$classe['rownum']] = $classe['desc_classe'];
            }
        }
    }

    return $elenco_finale;
	/*}}}*/
}

/**
 * Arrotonda la media secondo l'arrotondamento indicato (.25, .50, intero)
 *
 * @param decimal $media
 * @param int $arrotondamento
 * @return numeric
 */
function arrotonda_media($media, $arrotondamento)
{
    /*{{{ */
	$interi = intval($media);
    $decimali = intval(($media - $interi) * 100);

    switch($arrotondamento) {
        case 25:
            if ($decimali < 12.5) {
                $media_finale = $interi;
            } else if ($decimali < 37.5) {
                $media_finale = $interi + 0.25;
            } else if ($decimali < 62.5) {
                $media_finale = $interi + 0.50;
            } else if ($decimali < 87.5) {
                $media_finale = $interi + 0.75;
            } else {
                $media_finale = $interi + 1;
            }
            break;
        case 50:
            if ($decimali < 25) {
                $media_finale = $interi;
            } else if ($decimali < 75) {
                $media_finale = $interi + 0.50;
            } else {
                $media_finale = $interi + 1;
            }
            break;
        case 100:
            if ($decimali < 50) {
                $media_finale = $interi;
            } else {
                $media_finale = $interi + 1;
            }
            break;
        default:
            $media_finale = $media;
            break;
    }

    return $media_finale;
	/*}}}*/
}

/**
 * Estrae i voti di tutti gli studenti inseriti nel periodo scelto
 *
 * @param int $data_inizio
 * @param int $data_fine
 * @return array
 */
function estrai_voti_periodo($data_inizio, $data_fine, $ordinamento = '')
{
    //{{{ <editor-fold defaultstate="collapsed">
    if ($ordinamento == 'voto')
    {
       $order = " ORDER BY voto";
    }
    else
    {
        $order = "";
    }

    $sql = "SELECT voti.*,
                utenti.cognome || ' ' || utenti.nome as docente,
                studenti.cognome || ' ' || studenti.nome as studente,
                materie.descrizione as materia,
                materie.nome_materia_breve,
                materie.tipo_valutazione,
                classi_complete.classe,
                classi_complete.sezione,
                classi_complete.descrizione_indirizzi,
                classi_complete.codice_indirizzi
            FROM voti
            LEFT JOIN utenti ON voti.id_professore = utenti.id_utente
            LEFT JOIN studenti ON voti.id_studente = studenti.id_studente
            LEFT JOIN materie ON voti.id_materia = materie.id_materia
            LEFT JOIN classi_complete ON voti.id_classe = classi_complete.id_classe
            WHERE voti.data_inserimento >= $data_inizio
                AND voti.data_inserimento <= $data_fine
                AND voti.flag_canc = 0
            $order
    ";

    $res = pgsql_query($sql) or die("Invalid $sql");
    $numero = pg_num_rows($res);

    if ($numero > 0)
    {
        $voti = pg_fetch_all($res);
    }
    else
    {
        $voti = [];
    }

    return $voti;
    //}}} </editor-fold>
}

function estrai_pesi_voti($filtro = 'attivi')
{
    $and = "";

    if ($filtro == 'attivi'){
        $and .= " AND p.attivo = 'SI' ";
    }

    $elenco_pesi = [];

    $sql = "
        SELECT p.id_peso,
                p.peso,
                p.descrizione,
                p.peso_default,
                p.attivo,
                p.tipo,
                count(v.*) as voti
        FROM pesi p
        LEFT JOIN voti v ON v.id_peso = p.id_peso
            AND v.flag_canc = 0
        WHERE p.flag_canc = 0
            AND p.tipo = 'voti'
            {$and}
        GROUP BY p.id_peso, peso, descrizione, peso_default, p.attivo, p.tipo
        ORDER BY peso;
    ";
    $res = pgsql_query($sql) or die("Invalid $sql");
    $numero = pg_num_rows($res);

    if ($numero > 0){
        $elenco = pg_fetch_all($res);
        foreach ($elenco as $peso){
            $peso_tmp = [];
            $peso_tmp['id_peso'] = $peso['id_peso'];
            $peso_tmp['peso'] = $peso['peso'];
            $peso_tmp['descrizione'] = decode($peso['descrizione']);
            $peso_tmp['peso_default'] = $peso['peso_default'];
            $peso_tmp['voti'] = $peso['voti'];
            $peso_tmp['attivo'] = $peso['attivo'];
            $peso_tmp['tipo'] = $peso['tipo'];
            $elenco_pesi[] = $peso_tmp;
        }
    }

    return $elenco_pesi;
}

function estrai_pesi_competenze($filtro = 'attivi')
{
    $and = "";

    if ($filtro == 'attivi'){
        $and .= " AND attivo = 'SI' ";
    }

    $elenco_pesi = [];

    $sql = "
        SELECT id_peso,
                peso,
                descrizione,
                peso_default,
                attivo,
                tipo,
                utilizzi_competenze
        FROM pesi
        WHERE flag_canc = 0
            AND tipo = 'competenze'
            {$and}
        ORDER BY peso;
    ";
    $res = pgsql_query($sql) or die("Invalid $sql");
    $numero = pg_num_rows($res);

    if ($numero > 0){
        $elenco = pg_fetch_all($res);
        foreach ($elenco as $peso){
            $peso_tmp = [];
            $peso_tmp['id_peso'] = $peso['id_peso'];
            $peso_tmp['peso'] = $peso['peso'];
            $peso_tmp['descrizione'] = decode($peso['descrizione']);
            $peso_tmp['peso_default'] = $peso['peso_default'];
            $peso_tmp['utilizzi_competenze'] = $peso['utilizzi_competenze'];
            $peso_tmp['attivo'] = $peso['attivo'];
            $peso_tmp['tipo'] = $peso['tipo'];
            $elenco_pesi[] = $peso_tmp;
        }
    }

    return $elenco_pesi;
}

function inserisci_peso($peso, $descrizione, $tipo, $current_user)
{
    $descrizione = encode($descrizione);

    $insert = "INSERT INTO pesi (peso, descrizione, tipo) VALUES ({$peso}, '{$descrizione}', '{$tipo}') RETURNING id_peso";
    $res = pgsql_query($insert) or die("Invalid $insert");
    $numero = pg_num_rows($res);

    if ($numero > 0) {
        $id_peso = pg_fetch_result($res, 0, "id_peso");
    }

    inserisci_log(["id_peso" => $id_peso], "pesi", $current_user, "INTERFACCIA", "INSERIMENTO");

    return $id_peso;
}

function setta_peso_default($id_peso_default, $tipo, $current_user)
{
    if ($id_peso_default > 0){
        $update1 = "UPDATE pesi SET peso_default = false WHERE tipo = '{$tipo}' AND flag_canc = 0";
        $res = pgsql_query($update1) or die("Invalid $update1");

        $update2 = "UPDATE pesi SET peso_default = true WHERE id_peso = {$id_peso_default}";
        $res2 = pgsql_query($update2) or die("Invalid $update2");
        inserisci_log(["id_peso" => $id_peso], "pesi", $current_user, "INTERFACCIA", "MODIFICA");

        return true;
    } else {
        return false;
    }
}

function modifica_peso($id_peso, $dati)
{
    if ($id_peso > 0){

        if (isset($dati['descrizione'])){
            $set[] = "descrizione = '".$dati['descrizione']."'";
        }
        if (isset($dati['peso'])){
            $set[] = "peso = ".$dati['peso'];
        }
        if (isset($dati['peso_default'])){
            $set[] = "peso_default = ".$dati['peso_default'];
        }
        if (isset($dati['attivo'])){
            $set[] = "attivo = '".$dati['attivo']."'";
        }

        if (count($set) > 0){
            $set = implode(', ', $set);
            $update = "UPDATE pesi SET ".$set." WHERE id_peso = ".$id_peso;
            $res = pgsql_query($update) or die("Invalid $update");
            inserisci_log(["id_peso" => $id_peso], "pesi", $current_user, "INTERFACCIA", "MODIFICA");

            return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
}

function elimina_peso($id_peso, $current_user)
{
    $update = "UPDATE pesi SET flag_canc = ".time()." WHERE id_peso = ".$id_peso;
    $res = pgsql_query($update) or die("Invalid $update");

    inserisci_log(["id_peso" => $id_peso], "pesi", $current_user, "INTERFACCIA", "ELIMINAZIONE");
}

function estrai_utilizzi_peso($id_peso, $mode='fast')
{
    if ($mode == 'fast'){
        $sql = "SELECT COUNT(*) as conta
                FROM voti
                WHERE id_peso = {$id_peso} ";
        $res = pgsql_query($sql) or die("Invalid $sql");
        $conta = pg_fetch_result($res, 0, "conta");

        return $conta;
    }
}

function estrai_peso_default($tipo)
{
    $sql = "SELECT *
            FROM pesi
            WHERE flag_canc = 0
                AND peso_default = true
                AND tipo = '{$tipo}'
                AND attivo = 'SI'
            LIMIT 1";
    $res = pgsql_query($sql) or die("Invalid $sql");
    $numero = pg_num_rows($res);

    $peso_default = null;

    if ($numero > 0) {
        $peso_default = pg_fetch_all($res)[0];
        foreach ($peso_default as $key => $value) {
            $peso_default[$key] = decode($value);
        }
    }

    return $peso_default;
}

function estrai_voti_senza_peso($mode='fast')
{
    if ($mode == 'fast'){
        $sql = "SELECT count(*) as conta
                FROM voti
                WHERE flag_canc = 0
                    AND id_peso is null";
        $res = pgsql_query($sql) or die("Invalid $sql");
        $conta = pg_fetch_result($res, 0, "conta");
        return $conta;
    }
}